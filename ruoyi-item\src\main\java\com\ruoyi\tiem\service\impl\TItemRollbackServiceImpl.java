package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TItemRollback;
import com.ruoyi.tiem.mapper.TItemRollbackMapper;
import com.ruoyi.tiem.service.ITItemRollbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目节点回退信息
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
public class TItemRollbackServiceImpl implements ITItemRollbackService
{
    @Autowired
    private TItemRollbackMapper tItemRollbackMapper;

    /**
     * 查询项目节点回退信息

     * 
     * @param id 项目节点回退信息
主键
     * @return 项目节点回退信息

     */
    @Override
    public TItemRollback selectTItemRollbackById(Long id)
    {
        return tItemRollbackMapper.selectTItemRollbackById(id);
    }

    /**
     * 查询项目节点回退信息
列表
     * 
     * @param tItemRollback 项目节点回退信息

     * @return 项目节点回退信息

     */
    @Override
    public List<TItemRollback> selectTItemRollbackList(TItemRollback tItemRollback)
    {
        return tItemRollbackMapper.selectTItemRollbackList(tItemRollback);
    }

    /**
     * 新增项目节点回退信息

     * 
     * @param tItemRollback 项目节点回退信息

     * @return 结果
     */
    @Override
    public int insertTItemRollback(TItemRollback tItemRollback)
    {
        return tItemRollbackMapper.insertTItemRollback(tItemRollback);
    }

    /**
     * 修改项目节点回退信息

     * 
     * @param tItemRollback 项目节点回退信息

     * @return 结果
     */
    @Override
    public int updateTItemRollback(TItemRollback tItemRollback)
    {
        return tItemRollbackMapper.updateTItemRollback(tItemRollback);
    }

    /**
     * 批量删除项目节点回退信息

     * 
     * @param ids 需要删除的项目节点回退信息
主键
     * @return 结果
     */
    @Override
    public int deleteTItemRollbackByIds(Long[] ids)
    {
        return tItemRollbackMapper.deleteTItemRollbackByIds(ids);
    }

    /**
     * 删除项目节点回退信息
信息
     * 
     * @param id 项目节点回退信息
主键
     * @return 结果
     */
    @Override
    public int deleteTItemRollbackById(Long id)
    {
        return tItemRollbackMapper.deleteTItemRollbackById(id);
    }
}
