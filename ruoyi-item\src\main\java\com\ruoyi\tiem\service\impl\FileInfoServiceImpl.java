package com.ruoyi.tiem.service.impl;

import java.util.Arrays;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.FileInfoMapper;
import com.ruoyi.tiem.domain.FileInfo;
import com.ruoyi.tiem.service.IFileInfoService;

/**
 * 文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class FileInfoServiceImpl implements IFileInfoService
{
    @Autowired
    private FileInfoMapper fileInfoMapper;

    /**
     * 查询文件
     *
     * @param id 文件主键
     * @return 文件
     */
    @Override
    public FileInfo selectFileInfoById(Long id)
    {
        return fileInfoMapper.selectFileInfoById(id);
    }

    /**
     * 查询文件列表
     *
     * @param fileInfo 文件
     * @return 文件
     */
    @Override
    public List<FileInfo> selectFileInfoList(FileInfo fileInfo)
    {
        return fileInfoMapper.selectFileInfoList(fileInfo);
    }

    /**
     * 新增文件
     *
     * @param fileInfo 文件
     * @return 结果
     */
    @Override
    public int insertFileInfo(FileInfo fileInfo)
    {
        fileInfo.setCreateTime(DateUtils.getNowDate());
        return fileInfoMapper.insertFileInfo(fileInfo);
    }

    /**
     * 修改文件
     *
     * @param fileInfo 文件
     * @return 结果
     */
    @Override
    public int updateFileInfo(FileInfo fileInfo)
    {
        String username = SecurityUtils.getUsername();
        fileInfo.setCreateBy(username);
        return fileInfoMapper.updateFileInfo(fileInfo);
    }

    /**
     * 批量删除文件
     *
     * @param ids 需要删除的文件主键
     * @return 结果
     */
    @Override
    public int deleteFileInfoByIds(Long[] ids)
    {
        return fileInfoMapper.deleteFileInfoByIds(ids);
    }

    /**
     * 删除文件信息
     *
     * @param id 文件主键
     * @return 结果
     */
    @Override
    public int deleteFileInfoById(Long id)
    {
        return fileInfoMapper.deleteFileInfoById(id);
    }

    @Override
    public List<FileInfo> selectByFileIds(final String fileIds) {
        long[] longs = Arrays.stream(fileIds.split(",")).mapToLong(Long::parseLong).toArray();
        return fileInfoMapper.selectByFileIds(longs);
    }
}
