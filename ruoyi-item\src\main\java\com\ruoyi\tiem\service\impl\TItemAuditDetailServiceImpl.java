package com.ruoyi.tiem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.TItemAuditDetailMapper;
import com.ruoyi.tiem.domain.TItemAuditDetail;
import com.ruoyi.tiem.service.ITItemAuditDetailService;

/**
 * 多流程审核明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class TItemAuditDetailServiceImpl implements ITItemAuditDetailService 
{
    @Autowired
    private TItemAuditDetailMapper tItemAuditDetailMapper;

    /**
     * 查询多流程审核明细
     * 
     * @param id 多流程审核明细主键
     * @return 多流程审核明细
     */
    @Override
    public TItemAuditDetail selectTItemAuditDetailById(Long id)
    {
        return tItemAuditDetailMapper.selectTItemAuditDetailById(id);
    }

    /**
     * 查询多流程审核明细列表
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 多流程审核明细
     */
    @Override
    public List<TItemAuditDetail> selectTItemAuditDetailList(TItemAuditDetail tItemAuditDetail)
    {
        return tItemAuditDetailMapper.selectTItemAuditDetailList(tItemAuditDetail);
    }

    /**
     * 新增多流程审核明细
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 结果
     */
    @Override
    public int insertTItemAuditDetail(TItemAuditDetail tItemAuditDetail)
    {
        tItemAuditDetail.setCreateTime(DateUtils.getNowDate());
        return tItemAuditDetailMapper.insertTItemAuditDetail(tItemAuditDetail);
    }

    /**
     * 修改多流程审核明细
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 结果
     */
    @Override
    public int updateTItemAuditDetail(TItemAuditDetail tItemAuditDetail)
    {
        return tItemAuditDetailMapper.updateTItemAuditDetail(tItemAuditDetail);
    }

    /**
     * 批量删除多流程审核明细
     * 
     * @param ids 需要删除的多流程审核明细主键
     * @return 结果
     */
    @Override
    public int deleteTItemAuditDetailByIds(Long[] ids)
    {
        return tItemAuditDetailMapper.deleteTItemAuditDetailByIds(ids);
    }

    /**
     * 删除多流程审核明细信息
     * 
     * @param id 多流程审核明细主键
     * @return 结果
     */
    @Override
    public int deleteTItemAuditDetailById(Long id)
    {
        return tItemAuditDetailMapper.deleteTItemAuditDetailById(id);
    }
}
