package com.ruoyi.tiem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目目标考核率对象 t_implementation_rate
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public class TImplementationRate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目类别（设备，房修，基础） */
    @Excel(name = "项目类别", readConverterExp = "设备，房修，基础")
    private String category;

    /** 年份 */
    @Excel(name = "年份")
    private String year;

    /** 月份 */
    @Excel(name = "月份")
    private String month;

    /** 目标执行率 */
    @Excel(name = "目标执行率")
    private String targetRate;

    /** 状态（1：正常；9：删除） */
    @Excel(name = "状态", readConverterExp = "1=：正常；9：删除")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setYear(String year) 
    {
        this.year = year;
    }

    public String getYear() 
    {
        return year;
    }
    public void setMonth(String month) 
    {
        this.month = month;
    }

    public String getMonth() 
    {
        return month;
    }
    public void setTargetRate(String targetRate) 
    {
        this.targetRate = targetRate;
    }

    public String getTargetRate() 
    {
        return targetRate;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("category", getCategory())
            .append("year", getYear())
            .append("month", getMonth())
            .append("targetRate", getTargetRate())
            .append("status", getStatus())
            .toString();
    }
}
