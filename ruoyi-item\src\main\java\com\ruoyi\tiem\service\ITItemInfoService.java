package com.ruoyi.tiem.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.tiem.domain.*;

/**
 * 项目信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface ITItemInfoService {
    /**
     * 查询项目信息
     *
     * @param id 项目信息主键
     * @return 项目信息
     */
    public TItemInfo selectTItemInfoById(Long id);

    /**
     * 查询项目信息列表
     *
     * @param tItemInfo 项目信息
     * @return 项目信息集合
     */
    public List<TItemInfo> selectTItemInfoList(TItemInfo tItemInfo);

    /**
     * 新增项目信息
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    public Long insertTItemInfo(TItemInfo tItemInfo);

    public Long insertTItemInfoTwo(TItemInfo tItemInfo);

    /**
     * 修改项目信息
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    public int updateTItemInfo(TItemInfo tItemInfo);

    int updateTItemInfoTwo(TItemInfo tItemInfo);

    /**
     * 批量删除项目信息
     *
     * @param ids 需要删除的项目信息主键集合
     * @return 结果
     */
    public int deleteTItemInfoByIds(Long[] ids);

    /**
     * 删除项目信息信息
     *
     * @param id 项目信息主键
     * @return 结果
     */
    public int deleteTItemInfoById(Long id);

    /**
     * 导入项目信息
     *
     * @param
     * @return 结果
     */
    public String importItemInfo(List<TItemInfo> itemInfoList, Boolean isUpdateSupport);

    /**
     * 处理专业审核信息
     *
     * @param reviewVO 专业审核信息
     * @return 结果
     */
    public void processProfessionalReview(ProfessionalReviewVO<SortItemVO> reviewVO);

    void compositeReview(ProfessionalReviewVO<CompositeVO> reviewVO);

    void storeReview(ProfessionalReviewVO<StoreVO> reviewVO);

    void planReview(ProfessionalReviewVO<PlanVO> reviewVO);
}
