package com.ruoyi.tiem.service;

import com.ruoyi.tiem.domain.TItemRollback;

import java.util.List;

/**
 * 项目节点回退信息
Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ITItemRollbackService 
{
    /**
     * 查询项目节点回退信息

     * 
     * @param id 项目节点回退信息
主键
     * @return 项目节点回退信息

     */
    public TItemRollback selectTItemRollbackById(Long id);

    /**
     * 查询项目节点回退信息
列表
     * 
     * @param tItemRollback 项目节点回退信息

     * @return 项目节点回退信息
集合
     */
    public List<TItemRollback> selectTItemRollbackList(TItemRollback tItemRollback);

    /**
     * 新增项目节点回退信息

     * 
     * @param tItemRollback 项目节点回退信息

     * @return 结果
     */
    public int insertTItemRollback(TItemRollback tItemRollback);

    /**
     * 修改项目节点回退信息

     * 
     * @param tItemRollback 项目节点回退信息

     * @return 结果
     */
    public int updateTItemRollback(TItemRollback tItemRollback);

    /**
     * 批量删除项目节点回退信息

     * 
     * @param ids 需要删除的项目节点回退信息
主键集合
     * @return 结果
     */
    public int deleteTItemRollbackByIds(Long[] ids);

    /**
     * 删除项目节点回退信息
信息
     * 
     * @param id 项目节点回退信息
主键
     * @return 结果
     */
    public int deleteTItemRollbackById(Long id);
}
