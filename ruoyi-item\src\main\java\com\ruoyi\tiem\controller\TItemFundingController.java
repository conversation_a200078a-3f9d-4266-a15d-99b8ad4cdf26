package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemFunding;
import com.ruoyi.tiem.service.ITItemFundingService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 绩效评价-项目资金Controller
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@RestController
@RequestMapping("/system/funding")
public class TItemFundingController extends BaseController
{
    @Autowired
    private ITItemFundingService tItemFundingService;

    /**
     * 查询绩效评价-项目资金列表
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:list')")
    @GetMapping("/list")
    public TableDataInfo list(TItemFunding tItemFunding)
    {
        startPage();
        List<TItemFunding> list = tItemFundingService.selectTItemFundingList(tItemFunding);
        return getDataTable(list);
    }

    /**
     * 导出绩效评价-项目资金列表
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:export')")
    @Log(title = "绩效评价-项目资金", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemFunding tItemFunding)
    {
        List<TItemFunding> list = tItemFundingService.selectTItemFundingList(tItemFunding);
        ExcelUtil<TItemFunding> util = new ExcelUtil<TItemFunding>(TItemFunding.class);
        util.exportExcel(response, list, "绩效评价-项目资金数据");
    }

    /**
     * 获取绩效评价-项目资金详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tItemFundingService.selectTItemFundingById(id));
    }

    /**
     * 新增绩效评价-项目资金
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:add')")
    @Log(title = "绩效评价-项目资金", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemFunding tItemFunding)
    {
        return toAjax(tItemFundingService.insertTItemFunding(tItemFunding));
    }

    /**
     * 批量新增绩效评价-项目资金
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:add')")
    @Log(title = "绩效评价-项目资金", businessType = BusinessType.INSERT)
    @PostMapping("/addInBatches")
    public AjaxResult add(@RequestBody List<TItemFunding> list)
    {
        int result = 200;
        for (TItemFunding itemFunding : list) {
            result = tItemFundingService.insertTItemFunding(itemFunding);
        }
        return toAjax(result);
    }

    /**
     * 修改绩效评价-项目资金
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:edit')")
    @Log(title = "绩效评价-项目资金", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemFunding tItemFunding)
    {
        return toAjax(tItemFundingService.updateTItemFunding(tItemFunding));
    }

    /**
     * 批量修改绩效评价-项目资金
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:edit')")
    @Log(title = "绩效评价-项目资金", businessType = BusinessType.UPDATE)
    @PutMapping("/editInBatches")
    public AjaxResult edit(@RequestBody List<TItemFunding> list)
    {
        int result = 200;
        for (TItemFunding itemFunding : list) {
            result = tItemFundingService.updateTItemFunding(itemFunding);
        }
        return toAjax(result);
    }

    /**
     * 删除绩效评价-项目资金
     */
//    @PreAuthorize("@ss.hasPermi('system:funding:remove')")
    @Log(title = "绩效评价-项目资金", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tItemFundingService.deleteTItemFundingByIds(ids));
    }
}
