package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TImplementationRate;
import com.ruoyi.tiem.mapper.TImplementationRateMapper;
import com.ruoyi.tiem.service.ITImplementationRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目目标考核率Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
public class TImplementationRateServiceImpl implements ITImplementationRateService
{
    @Autowired
    private TImplementationRateMapper tImplementationRateMapper;

    /**
     * 查询项目目标考核率
     * 
     * @param id 项目目标考核率主键
     * @return 项目目标考核率
     */
    @Override
    public TImplementationRate selectTImplementationRateById(Long id)
    {
        return tImplementationRateMapper.selectTImplementationRateById(id);
    }

    /**
     * 查询项目目标考核率列表
     * 
     * @param tImplementationRate 项目目标考核率
     * @return 项目目标考核率
     */
    @Override
    public List<TImplementationRate> selectTImplementationRateList(TImplementationRate tImplementationRate)
    {
        return tImplementationRateMapper.selectTImplementationRateList(tImplementationRate);
    }

    /**
     * 新增项目目标考核率
     * 
     * @param tImplementationRate 项目目标考核率
     * @return 结果
     */
    @Override
    public int insertTImplementationRate(TImplementationRate tImplementationRate)
    {
        return tImplementationRateMapper.insertTImplementationRate(tImplementationRate);
    }

    /**
     * 修改项目目标考核率
     * 
     * @param tImplementationRate 项目目标考核率
     * @return 结果
     */
    @Override
    public int updateTImplementationRate(TImplementationRate tImplementationRate)
    {
        return tImplementationRateMapper.updateTImplementationRate(tImplementationRate);
    }

    /**
     * 批量删除项目目标考核率
     * 
     * @param ids 需要删除的项目目标考核率主键
     * @return 结果
     */
    @Override
    public int deleteTImplementationRateByIds(Long[] ids)
    {
        return tImplementationRateMapper.deleteTImplementationRateByIds(ids);
    }

    /**
     * 删除项目目标考核率信息
     * 
     * @param id 项目目标考核率主键
     * @return 结果
     */
    @Override
    public int deleteTImplementationRateById(Long id)
    {
        return tImplementationRateMapper.deleteTImplementationRateById(id);
    }
}
