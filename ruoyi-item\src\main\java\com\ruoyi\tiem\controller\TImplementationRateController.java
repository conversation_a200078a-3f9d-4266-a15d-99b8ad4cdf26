package com.ruoyi.tiem.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.tiem.domain.ImplementationRateParams;
import com.ruoyi.tiem.domain.TImplementationRate;
import com.ruoyi.tiem.mapper.TImplementationRateMapper;
import com.ruoyi.tiem.service.ITImplementationRateService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目目标考核率Controller
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@RestController
@RequestMapping("/implementation/rate")
public class TImplementationRateController extends BaseController {
    @Autowired
    private ITImplementationRateService tImplementationRateService;

    @Autowired
    private TImplementationRateMapper tImplementationRateMapper;

    /**
     * 查询项目目标考核率列表
     */
    @PreAuthorize("@ss.hasPermi('system:rate:list')")
    @GetMapping("/list")
    public TableDataInfo list(TImplementationRate tImplementationRate) {
        startPage();
        List<TImplementationRate> list = tImplementationRateService.selectTImplementationRateList(tImplementationRate);
        return getDataTable(list);
    }

    /**
     * 导出项目目标考核率列表
     */
    @PreAuthorize("@ss.hasPermi('system:rate:export')")
    @Log(title = "项目目标考核率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TImplementationRate tImplementationRate) {
        List<TImplementationRate> list = tImplementationRateService.selectTImplementationRateList(tImplementationRate);
        ExcelUtil<TImplementationRate> util = new ExcelUtil<TImplementationRate>(TImplementationRate.class);
        util.exportExcel(response, list, "项目目标考核率数据");
    }

    /**
     * 获取项目目标考核率详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:rate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tImplementationRateService.selectTImplementationRateById(id));
    }

    @Autowired
    private ISysDictDataService dictDataService;
    /**
     * 获取项目目标考核率详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rate:query')")
    @GetMapping("/listDetail")
    public AjaxResult getListDetail(String year) {
        String[] months = {"jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"};
        List<TImplementationRate> tImplementationRateList = tImplementationRateMapper.
                selectTImplementationRateListByYear(year);
        List<ImplementationRateParams> implementationRateParamsList = new ArrayList<ImplementationRateParams>();

        //*/ 获取项目类别字典
        SysDictData dictData = new SysDictData();
        dictData.setDictType("item_category");
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        String[] itemCategories = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            itemCategories[i] = list.get(i).getDictValue();
        }

        for (String itemCategory : itemCategories) {
            ImplementationRateParams implementationRateParams = new ImplementationRateParams();
            implementationRateParams.setYear(year);
            implementationRateParams.setCategory(itemCategory);
            Map<String, Object> map = new HashMap<>();
            implementationRateParams.setParams(map);

            for (TImplementationRate tImplementationRate : tImplementationRateList) {
                String key = months[Integer.parseInt(tImplementationRate.getMonth()) - 1];
                String value = tImplementationRate.getTargetRate();
                if (tImplementationRate.getCategory().equals(itemCategory)) {
                    implementationRateParams.getParams().put(key, value);
                }
            }

            implementationRateParamsList.add(implementationRateParams);
        }

        return success(implementationRateParamsList);
    }

    /**
     * 新增项目目标考核率
     */
    @PreAuthorize("@ss.hasPermi('system:rate:add')")
    @Log(title = "项目目标考核率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TImplementationRate tImplementationRate) {
        return toAjax(tImplementationRateService.insertTImplementationRate(tImplementationRate));
    }

    /**
     * 批量新增项目目标考核率
     */
    @PreAuthorize("@ss.hasPermi('system:rate:add')")
    @Log(title = "项目目标考核率", businessType = BusinessType.INSERT)
    @PostMapping("/addInBatches")
    public AjaxResult addInBatches(@RequestBody List<ImplementationRateParams> ImplementationRateParams) {
        String[] months = {"jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"};
        for (ImplementationRateParams implementationRateParams : ImplementationRateParams) {
            Map<String, Object> map = implementationRateParams.getParams();
            for (int i = 0; i < months.length; i++) {
                TImplementationRate implementationRate = new TImplementationRate();
                implementationRate.setCategory(implementationRateParams.getCategory());
                implementationRate.setYear(implementationRateParams.getYear());
                implementationRate.setMonth(String.valueOf(i + 1));
                implementationRate.setTargetRate(map.get(months[i]).toString());
                tImplementationRateService.insertTImplementationRate(implementationRate);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 批量编辑项目目标考核率
     */
    @PreAuthorize("@ss.hasPermi('system:rate:edit')")
    @Log(title = "项目目标考核率", businessType = BusinessType.INSERT)
    @PostMapping("/editInBatches")
    public AjaxResult editInBatches(@RequestBody List<ImplementationRateParams> ImplementationRateParams) {
        String[] months = {"jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"};
        for (ImplementationRateParams implementationRateParams : ImplementationRateParams) {
            Map<String, Object> map = implementationRateParams.getParams();
            for (int i = 0; i < months.length; i++) {
                TImplementationRate implementationRate = new TImplementationRate();
                implementationRate.setCategory(implementationRateParams.getCategory());
                implementationRate.setYear(implementationRateParams.getYear());
                implementationRate.setMonth(String.valueOf(i + 1));
                implementationRate.setTargetRate(map.get(months[i]).toString());
                tImplementationRateMapper.updateTImplementationRateTarget(implementationRate);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 修改项目目标考核率
     */
    @PreAuthorize("@ss.hasPermi('system:rate:edit')")
    @Log(title = "项目目标考核率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TImplementationRate tImplementationRate) {
        return toAjax(tImplementationRateService.updateTImplementationRate(tImplementationRate));
    }

    /**
     * 删除项目目标考核率
     */
    @PreAuthorize("@ss.hasPermi('system:rate:remove')")
    @Log(title = "项目目标考核率", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tImplementationRateService.deleteTImplementationRateByIds(ids));
    }
}
