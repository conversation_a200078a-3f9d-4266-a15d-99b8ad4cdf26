package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TItemBudget;
import com.ruoyi.tiem.mapper.TItemBudgetMapper;
import com.ruoyi.tiem.service.ITItemBudgetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目支出预算明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class TItemBudgetServiceImpl implements ITItemBudgetService
{
    @Autowired
    private TItemBudgetMapper tItemBudgetMapper;

    /**
     * 查询项目支出预算明细
     * 
     * @param id 项目支出预算明细主键
     * @return 项目支出预算明细
     */
    @Override
    public TItemBudget selectTItemBudgetById(Long id)
    {
        return tItemBudgetMapper.selectTItemBudgetById(id);
    }

    /**
     * 查询项目支出预算明细列表
     * 
     * @param tItemBudget 项目支出预算明细
     * @return 项目支出预算明细
     */
    @Override
    public List<TItemBudget> selectTItemBudgetList(TItemBudget tItemBudget)
    {
        return tItemBudgetMapper.selectTItemBudgetList(tItemBudget);
    }

    /**
     * 新增项目支出预算明细
     * 
     * @param tItemBudget 项目支出预算明细
     * @return 结果
     */
    @Override
    public int insertTItemBudget(TItemBudget tItemBudget)
    {
        return tItemBudgetMapper.insertTItemBudget(tItemBudget);
    }

    /**
     * 修改项目支出预算明细
     * 
     * @param tItemBudget 项目支出预算明细
     * @return 结果
     */
    @Override
    public int updateTItemBudget(TItemBudget tItemBudget)
    {
        return tItemBudgetMapper.updateTItemBudget(tItemBudget);
    }

    /**
     * 批量删除项目支出预算明细
     * 
     * @param ids 需要删除的项目支出预算明细主键
     * @return 结果
     */
    @Override
    public int deleteTItemBudgetByIds(Long[] ids)
    {
        return tItemBudgetMapper.deleteTItemBudgetByIds(ids);
    }

    /**
     * 删除项目支出预算明细信息
     * 
     * @param id 项目支出预算明细主键
     * @return 结果
     */
    @Override
    public int deleteTItemBudgetById(Long id)
    {
        return tItemBudgetMapper.deleteTItemBudgetById(id);
    }
}
