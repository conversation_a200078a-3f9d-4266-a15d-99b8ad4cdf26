package com.ruoyi.tiem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.tiem.domain.TContractInfo;
import com.ruoyi.tiem.mapper.TContractInfoMapper;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.ExecutePlanMapper;
import com.ruoyi.tiem.domain.ExecutePlan;
import com.ruoyi.tiem.service.IExecutePlanService;

/**
 * 执行计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class ExecutePlanServiceImpl implements IExecutePlanService
{
    @Autowired
    private ExecutePlanMapper executePlanMapper;

    @Autowired
    private TContractInfoMapper tContractInfoMapper;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    /**
     * 查询执行计划
     *
     * @param id 执行计划主键
     * @return 执行计划
     */
    @Override
    public ExecutePlan selectExecutePlanById(Long id)
    {
        return executePlanMapper.selectExecutePlanById(id);
    }

    /**
     * 查询执行计划列表
     *
     * @param executePlan 执行计划
     * @return 执行计划
     */
    @Override
    public List<ExecutePlan> selectExecutePlanList(ExecutePlan executePlan)
    {
        return executePlanMapper.selectExecutePlanList(executePlan);
    }

    /**
     * 新增执行计划
     *
     * @param executePlan 执行计划
     * @return 结果
     */
    @Override
    public int insertExecutePlan(ExecutePlan executePlan)
    {
        executePlan.setCreateTime(DateUtils.getNowDate());
        int row = executePlanMapper.insertExecutePlan(executePlan);
        //更新合同累计开票、付款金额
        tContractInfoMapper.updateInvoiceAndPayment(executePlan.getContractId());
        //更新项目累计开票金额、累计付款金额
        TContractInfo tContractInfo = tContractInfoMapper.selectTContractInfoById(executePlan.getContractId());
        itemInfoMapper.updateInvoiceAndPayment(tContractInfo.getItemIds());
        //更新执行率
        itemInfoMapper.updateExecuteRate(tContractInfo.getItemIds());
        return row;
    }

    /**
     * 修改执行计划
     *
     * @param executePlan 执行计划
     * @return 结果
     */
    @Override
    public int updateExecutePlan(ExecutePlan executePlan)
    {
        return executePlanMapper.updateExecutePlan(executePlan);
    }

    /**
     * 批量删除执行计划
     *
     * @param ids 需要删除的执行计划主键
     * @return 结果
     */
    @Override
    public int deleteExecutePlanByIds(Long[] ids)
    {
        return executePlanMapper.deleteExecutePlanByIds(ids);
    }

    /**
     * 删除执行计划信息
     *
     * @param id 执行计划主键
     * @return 结果
     */
    @Override
    public int deleteExecutePlanById(Long id)
    {
        return executePlanMapper.deleteExecutePlanById(id);
    }
}
