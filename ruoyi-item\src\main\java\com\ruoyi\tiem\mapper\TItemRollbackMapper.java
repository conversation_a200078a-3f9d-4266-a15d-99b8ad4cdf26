package com.ruoyi.tiem.mapper;

import com.ruoyi.tiem.domain.TItemRollback;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目节点回退信息
Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface TItemRollbackMapper 
{
    /**
     * 查询项目节点回退信息

     * 
     * @param id 项目节点回退信息
主键
     * @return 项目节点回退信息

     */
    public TItemRollback selectTItemRollbackById(Long id);

    /**
     * 查询项目节点回退信息
列表
     * 
     * @param tItemRollback 项目节点回退信息

     * @return 项目节点回退信息
集合
     */
    public List<TItemRollback> selectTItemRollbackList(TItemRollback tItemRollback);

    /**
     * 新增项目节点回退信息

     * 
     * @param tItemRollback 项目节点回退信息

     * @return 结果
     */
    public int insertTItemRollback(TItemRollback tItemRollback);

    /**
     * 修改项目节点回退信息

     * 
     * @param tItemRollback 项目节点回退信息

     * @return 结果
     */
    public int updateTItemRollback(TItemRollback tItemRollback);

    /**
     * 删除项目节点回退信息

     * 
     * @param id 项目节点回退信息
主键
     * @return 结果
     */
    public int deleteTItemRollbackById(Long id);

    /**
     * 批量删除项目节点回退信息

     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTItemRollbackByIds(Long[] ids);

    public void insertTItemRollbackBatch(@Param("itemList") List<TItemRollback> list);
}
