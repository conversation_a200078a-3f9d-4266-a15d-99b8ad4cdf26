package com.ruoyi.common.utils;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.NodeVisitor;

public class RichTextUtil {
    public static String toPlainTextWithNewLine(String html) {
        if (html == null) {
            return "";
        }
        Document doc = Jsoup.parse(html);
        StringBuilder sb = new StringBuilder();

        doc.body().traverse(new NodeVisitor() {
            @Override
            public void head(Node node, int depth) {
                if (node instanceof TextNode) {
                    sb.append(((TextNode) node).text());
                } else if ("br".equalsIgnoreCase(node.nodeName())
                        || "p".equalsIgnoreCase(node.nodeName())
                        || "div".equalsIgnoreCase(node.nodeName())) {
                    // 换行标签，拼接换行
                    sb.append("\n");
                }
            }

            @Override
            public void tail(Node node, int depth) {
                // nothing
            }
        });

        // 去除多余的连续换行
        return sb.toString().replaceAll("[ \\t\\x0B\\f\\r]+", " ")
                .replaceAll("\\n{2,}", "\n")
                .trim();
    }

    public static void main(String[] args) {
        String html = "<p>你好，<b>世界</b>！<br>这是第二行。</p><div>第三行</div>";
        String plain = RichTextUtil.toPlainTextWithNewLine(html);
        System.out.println(plain);
        // 输出：
        // 你好，世界！
        // 这是第二行。
        // 第三行
    }
}
