package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 执行计划对象 t_execute_plan
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
public class ExecutePlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 付款类别 */
    private String paymentCategory;

    /** 付款比例 */
    private BigDecimal paymentRatio;

    /** 开票额 */
    @Excel(name = "开票额")
    private BigDecimal invoiceAmount;

    /** 支付额 */
    @Excel(name = "支付额")
    private BigDecimal paymentAmount;

    /** 凭证号 */
    @Excel(name = "凭证号")
    private String voucherNumber;

    /** 合同id */
    @Excel(name = "合同id")
    private Long contractId;

    /** 状态1：正常 9：删除 */
    @Excel(name = "状态1：正常 9：删除")
    private String status;

    /** 执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "执行时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date executeTime;

    /** 本月目标 */
    @Excel(name = "本月目标")
    private BigDecimal targetMonth;

    /** 计划支付金额申报日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划支付金额申报日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentPlanTime;

    /** 计划支付金额 */
    @Excel(name = "计划支付金额")
    private BigDecimal paymentPlan;

    /** 责任人 */
    @Excel(name = "责任人")
    private String dutyPerson;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setInvoiceAmount(BigDecimal invoiceAmount) 
    {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getInvoiceAmount() 
    {
        return invoiceAmount;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) 
    {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() 
    {
        return paymentAmount;
    }
    public void setVoucherNumber(String voucherNumber) 
    {
        this.voucherNumber = voucherNumber;
    }

    public String getVoucherNumber() 
    {
        return voucherNumber;
    }
    public void setContractId(Long contractId) 
    {
        this.contractId = contractId;
    }

    public Long getContractId() 
    {
        return contractId;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setExecuteTime(Date executeTime) 
    {
        this.executeTime = executeTime;
    }

    public Date getExecuteTime() 
    {
        return executeTime;
    }
    public void setTargetMonth(BigDecimal targetMonth) 
    {
        this.targetMonth = targetMonth;
    }

    public BigDecimal getTargetMonth() 
    {
        return targetMonth;
    }
    public void setPaymentPlanTime(Date paymentPlanTime) 
    {
        this.paymentPlanTime = paymentPlanTime;
    }

    public Date getPaymentPlanTime() 
    {
        return paymentPlanTime;
    }
    public void setPaymentPlan(BigDecimal paymentPlan) 
    {
        this.paymentPlan = paymentPlan;
    }

    public BigDecimal getPaymentPlan() 
    {
        return paymentPlan;
    }
    public void setDutyPerson(String dutyPerson) 
    {
        this.dutyPerson = dutyPerson;
    }

    public String getDutyPerson() 
    {
        return dutyPerson;
    }

    public String getPaymentCategory() {
        return paymentCategory;
    }

    public void setPaymentCategory(String paymentCategory) {
        this.paymentCategory = paymentCategory;
    }

    public BigDecimal getPaymentRatio() {
        return paymentRatio;
    }

    public void setPaymentRatio(BigDecimal paymentRatio) {
        this.paymentRatio = paymentRatio;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("paymentCategory", getPaymentCategory())
            .append("paymentRatio", getPaymentRatio())
            .append("invoiceAmount", getInvoiceAmount())
            .append("paymentAmount", getPaymentAmount())
            .append("voucherNumber", getVoucherNumber())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("contractId", getContractId())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("executeTime", getExecuteTime())
            .append("targetMonth", getTargetMonth())
            .append("paymentPlanTime", getPaymentPlanTime())
            .append("paymentPlan", getPaymentPlan())
            .append("dutyPerson", getDutyPerson())
            .toString();
    }
}
