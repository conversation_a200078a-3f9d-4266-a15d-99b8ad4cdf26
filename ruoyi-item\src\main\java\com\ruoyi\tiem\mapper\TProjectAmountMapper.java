package com.ruoyi.tiem.mapper;

import com.ruoyi.tiem.domain.TProjectAmount;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 年度立项总金额Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-28
 */
public interface TProjectAmountMapper 
{
    /**
     * 查询年度立项总金额
     * 
     * @param id 年度立项总金额主键
     * @return 年度立项总金额
     */
    public TProjectAmount selectTProjectAmountById(Long id);

    /**
     * 查询年度立项总金额列表
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 年度立项总金额集合
     */
    public List<TProjectAmount> selectTProjectAmountList(TProjectAmount tProjectAmount);

    /**
     * 根据年度查询立项总金额
     *
     * @param year 年度
     * @return 年度立项总金额集合
     */
    public BigDecimal selectAmountOfYear(String year);

    /**
     * 新增年度立项总金额
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    public int insertTProjectAmount(TProjectAmount tProjectAmount);

    /**
     * 修改年度立项总金额
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    public int updateTProjectAmount(TProjectAmount tProjectAmount);

    /**
     * 根据year和itemCategory编辑年度立项总金额
     *
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    public int editTProjectAmount(TProjectAmount tProjectAmount);

    /**
     * 删除年度立项总金额
     * 
     * @param id 年度立项总金额主键
     * @return 结果
     */
    public int deleteTProjectAmountById(Long id);

    /**
     * 批量删除年度立项总金额
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTProjectAmountByIds(Long[] ids);



        /**
         * 根据年份和项目类别查询年度立项总金额
         */
        public  TProjectAmount selectTProjectAmountByYearAndCategory(@Param("year")String year, @Param("itemCategory")String itemCategory);


}
