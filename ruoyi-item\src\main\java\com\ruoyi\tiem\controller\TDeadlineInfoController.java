package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TDeadlineInfo;
import com.ruoyi.tiem.service.ITDeadlineInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目关门节点Controller
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@RestController
@RequestMapping("/deadline/info")
public class TDeadlineInfoController extends BaseController
{
    @Autowired
    private ITDeadlineInfoService tDeadlineInfoService;

    /**
     * 查询项目关门节点列表
     */
//    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(TDeadlineInfo tDeadlineInfo)
    {
        startPage();
        List<TDeadlineInfo> list = tDeadlineInfoService.selectTDeadlineInfoList(tDeadlineInfo);
        return getDataTable(list);
    }

    /**
     * 导出项目关门节点列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "项目关门节点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TDeadlineInfo tDeadlineInfo)
    {
        List<TDeadlineInfo> list = tDeadlineInfoService.selectTDeadlineInfoList(tDeadlineInfo);
        ExcelUtil<TDeadlineInfo> util = new ExcelUtil<TDeadlineInfo>(TDeadlineInfo.class);
        util.exportExcel(response, list, "项目关门节点数据");
    }

    /**
     * 获取项目关门节点详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tDeadlineInfoService.selectTDeadlineInfoById(id));
    }

    /**
     * 新增项目关门节点
     */
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "项目关门节点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TDeadlineInfo tDeadlineInfo)
    {
        return toAjax(tDeadlineInfoService.insertTDeadlineInfo(tDeadlineInfo));
    }

    /**
     * 修改项目关门节点
     */
    @PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "项目关门节点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TDeadlineInfo tDeadlineInfo)
    {
        return toAjax(tDeadlineInfoService.updateTDeadlineInfo(tDeadlineInfo));
    }

    /**
     * 删除项目关门节点
     */
    @PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "项目关门节点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tDeadlineInfoService.deleteTDeadlineInfoByIds(ids));
    }
}
