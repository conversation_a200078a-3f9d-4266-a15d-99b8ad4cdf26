package com.ruoyi.tiem.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目节点回退信息
 * 对象 t_item_rollback
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public class TItemRollback extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 项目id
     */
    @Excel(name = "项目id")
    private Long itemId;


    /**
     * 审批节点
     */
    private Integer flow;

    /**
     * 退回人
     */
    @Excel(name = "退回人")
    private String person;

    /**
     * 退回理由
     */
    @Excel(name = "退回理由")
    private String reason;

    /**
     * 退回时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "退回时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /**
     * 状态（1：正常；9：删除）
     */
    @Excel(name = "状态", readConverterExp = "1=：正常；9：删除")
    private String status;

    private String auditDeptName;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }

    public String getAuditDeptName() {
        return auditDeptName;
    }

    public void setAuditDeptName(String auditDeptName) {
        this.auditDeptName = auditDeptName;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public String getPerson() {
        return person;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Date getTime() {
        return time;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("itemId", getItemId())
                .append("flow", getFlow())
                .append("person", getPerson())
                .append("reason", getReason())
                .append("time", getTime())
                .append("status", getStatus())
                .append("auditDeptName", getAuditDeptName())
                .toString();
    }
}
