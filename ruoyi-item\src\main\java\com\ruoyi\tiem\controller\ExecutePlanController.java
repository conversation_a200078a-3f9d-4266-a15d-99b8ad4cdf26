package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.ExecutePlan;
import com.ruoyi.tiem.service.IExecutePlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 执行计划Controller
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/execute/plan")
public class ExecutePlanController extends BaseController
{
    @Autowired
    private IExecutePlanService executePlanService;

    /**
     * 查询执行计划列表
     */
    // @PreAuthorize("@ss.hasPermi('execute:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecutePlan executePlan)
    {
        startPage();
        List<ExecutePlan> list = executePlanService.selectExecutePlanList(executePlan);
        return getDataTable(list);
    }

    /**
     * 导出执行计划列表
     */
    // @PreAuthorize("@ss.hasPermi('execute:plan:export')")
    @Log(title = "执行计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecutePlan executePlan)
    {
        List<ExecutePlan> list = executePlanService.selectExecutePlanList(executePlan);
        ExcelUtil<ExecutePlan> util = new ExcelUtil<ExecutePlan>(ExecutePlan.class);
        util.exportExcel(response, list, "执行计划数据");
    }

    /**
     * 获取执行计划详细信息
     */
    // @PreAuthorize("@ss.hasPermi('execute:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(executePlanService.selectExecutePlanById(id));
    }

    /**
     * 新增执行计划
     */
    // @PreAuthorize("@ss.hasPermi('execute:plan:add')")
    @Log(title = "执行计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody List<ExecutePlan> executePlanList)
    {
        executePlanList.forEach(executePlan -> {
            executePlanService.insertExecutePlan(executePlan);
        });
        return AjaxResult.success();
    }

    /**
     * 修改执行计划
     */
    // @PreAuthorize("@ss.hasPermi('execute:plan:edit')")
    @Log(title = "执行计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecutePlan executePlan)
    {
        return toAjax(executePlanService.updateExecutePlan(executePlan));
    }

    /**
     * 删除执行计划
     */
    // @PreAuthorize("@ss.hasPermi('execute:plan:remove')")
    @Log(title = "执行计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(executePlanService.deleteExecutePlanByIds(ids));
    }
}
