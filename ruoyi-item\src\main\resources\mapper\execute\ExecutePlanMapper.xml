<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.ExecutePlanMapper">

    <resultMap type="ExecutePlan" id="ExecutePlanResult">
        <result property="id"    column="id"    />
        <result property="paymentCategory"    column="payment_category"    />
        <result property="paymentRatio"    column="invoice_ratio"    />
        <result property="invoiceAmount"    column="invoice_amount"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="voucherNumber"    column="voucher_number"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="contractId"    column="contract_id"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="targetMonth"    column="target_month"    />
        <result property="paymentPlanTime"    column="payment_plan_time"    />
        <result property="paymentPlan"    column="payment_plan"    />
        <result property="dutyPerson"    column="duty_person"    />
    </resultMap>

    <sql id="selectExecutePlanVo">
        select id, payment_category, payment_ratio, invoice_amount, payment_amount, voucher_number, create_by, create_time, contract_id, status, remark, execute_time, target_month, payment_plan_time, payment_plan, duty_person from t_execute_plan
    </sql>

    <select id="selectExecutePlanList" parameterType="ExecutePlan" resultMap="ExecutePlanResult">
        <include refid="selectExecutePlanVo"/>
        <where>
            <if test="paymentCategory != null "> and payment_category = #{paymentCategory}</if>
            <if test="paymentRatio != null "> and payment_ratio = #{paymentRatio}</if>
            <if test="invoiceAmount != null "> and invoice_amount = #{invoiceAmount}</if>
            <if test="paymentAmount != null "> and payment_amount = #{paymentAmount}</if>
            <if test="voucherNumber != null  and voucherNumber != ''"> and voucher_number = #{voucherNumber}</if>
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="executeTime != null "> and execute_time = #{executeTime}</if>
            <if test="targetMonth != null "> and target_month = #{targetMonth}</if>
            <if test="paymentPlanTime != null "> and payment_plan_time = #{paymentPlanTime}</if>
            <if test="paymentPlan != null "> and payment_plan = #{paymentPlan}</if>
            <if test="dutyPerson != null  and dutyPerson != ''"> and duty_person = #{dutyPerson}</if>
        </where>
    </select>

    <select id="selectExecutePlanById" parameterType="Long" resultMap="ExecutePlanResult">
        <include refid="selectExecutePlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertExecutePlan" parameterType="ExecutePlan" useGeneratedKeys="true" keyProperty="id">
        insert into t_execute_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceAmount != null">invoice_amount,</if>
            <if test="paymentCategory != null">payment_category,</if>
            <if test="paymentRatio != null">payment_ratio,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="voucherNumber != null">voucher_number,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="targetMonth != null">target_month,</if>
            <if test="paymentPlanTime != null">payment_plan_time,</if>
            <if test="paymentPlan != null">payment_plan,</if>
            <if test="dutyPerson != null">duty_person,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceAmount != null">#{invoiceAmount},</if>
            <if test="paymentCategory != null">#{paymentCategory},</if>
            <if test="paymentRatio != null">#{paymentRatio},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="voucherNumber != null">#{voucherNumber},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="targetMonth != null">#{targetMonth},</if>
            <if test="paymentPlanTime != null">#{paymentPlanTime},</if>
            <if test="paymentPlan != null">#{paymentPlan},</if>
            <if test="dutyPerson != null">#{dutyPerson},</if>
         </trim>
    </insert>

    <update id="updateExecutePlan" parameterType="ExecutePlan">
        update t_execute_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceAmount != null">invoice_amount = #{invoiceAmount},</if>
            <if test="paymentCategory != null">payment_category = #{paymentCategory},</if>
            <if test="paymentRatio != null">payment_ratio = #{paymentRatio},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="voucherNumber != null">voucher_number = #{voucherNumber},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="targetMonth != null">target_month = #{targetMonth},</if>
            <if test="paymentPlanTime != null">payment_plan_time = #{paymentPlanTime},</if>
            <if test="paymentPlan != null">payment_plan = #{paymentPlan},</if>
            <if test="dutyPerson != null">duty_person = #{dutyPerson},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteExecutePlanById" parameterType="Long">
        update t_execute_plan set status = 9
        where id = #{id}
    </update>

    <update id="deleteExecutePlanByIds" parameterType="String">
        update t_execute_plan set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
