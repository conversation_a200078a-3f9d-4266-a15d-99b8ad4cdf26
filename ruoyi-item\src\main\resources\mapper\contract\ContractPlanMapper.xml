<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.ContractPlanMapper">

    <resultMap type="ContractPlan" id="ContractPlanResult">
        <result property="id"    column="id"    />
        <result property="contractName"    column="contract_name"    />
        <result property="contractType"    column="contract_type"    />
        <result property="purpose"    column="purpose"    />
        <result property="contractAmount"    column="contract_amount"    />
        <result property="status"    column="status"    />
        <result property="itemId"    column="item_id"    />
        <result property="purchaseType"    column="purchase_type"    />
        <result property="economyType"    column="economy_type"    />
        <result property="detailType"    column="detail_type"    />
    </resultMap>

    <sql id="selectContractPlanVo">
        select id, contract_name, contract_type, purpose, contract_amount, status, item_id, purchase_type, economy_type, detail_type from t_contract_plan
    </sql>

    <select id="selectContractPlanList" parameterType="ContractPlan" resultMap="ContractPlanResult">
        <include refid="selectContractPlanVo"/>
        where status = 1
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="contractType != null  and contractType != ''"> and contract_type like concat('%', #{contractType}, '%')</if>
            <if test="purpose != null  and purpose != ''"> and purpose = #{purpose}</if>
            <if test="contractAmount != null "> and contract_amount = #{contractAmount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>

    </select>

    <select id="selectV2ContractPlanList" parameterType="Long" resultMap="ContractPlanResult">
        <include refid="selectContractPlanVo"/>
        where status = 1 and item_id = #{itemId} and contract_type = 'V2'
    </select>

    <select id="selectContractPlanById" parameterType="Long" resultMap="ContractPlanResult">
        <include refid="selectContractPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertContractPlan" parameterType="ContractPlan" useGeneratedKeys="true" keyProperty="id">
        insert into t_contract_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractName != null">contract_name,</if>
            <if test="contractType != null">contract_type,</if>
            <if test="purpose != null">purpose,</if>
            <if test="contractAmount != null">contract_amount,</if>
            <if test="status != null">status,</if>
            <if test="itemId != null">item_id,</if>
            <if test="purchaseType != null">purchase_type,</if>
            <if test="economyType != null">economy_type,</if>
            <if test="detailType != null">detail_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractName != null">#{contractName},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="contractAmount != null">#{contractAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="purchaseType != null">#{purchaseType},</if>
            <if test="economyType != null">#{economyType},</if>
            <if test="detailType != null">#{detailType},</if>
         </trim>
    </insert>

    <update id="updateContractPlan" parameterType="ContractPlan">
        update t_contract_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractName != null">contract_name = #{contractName},</if>
            <if test="contractType != null">contract_type = #{contractType},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="contractAmount != null">contract_amount = #{contractAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="purchaseType != null">purchase_type = #{purchaseType},</if>
            <if test="economyType != null">economy_type = #{economyType},</if>
            <if test="detailType != null">detail_type = #{detailType},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteContractPlanById" parameterType="Long">
        update t_contract_plan set status = 9
        where id = #{id}
    </update>

    <update id="deleteContractPlanByIds" parameterType="String">
        update t_contract_plan set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteContractPlanByItemIds" parameterType="String">
        update t_contract_plan set status = 9 where item_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
