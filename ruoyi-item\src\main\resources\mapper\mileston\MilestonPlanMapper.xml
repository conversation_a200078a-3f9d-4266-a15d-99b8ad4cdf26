<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.MilestonPlanMapper">

    <resultMap type="MilestonPlan" id="MilestonPlanResult">
        <result property="id"    column="id"    />
        <result property="milestoneTime"    column="milestone_time"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="itemId"    column="item_id"    />
        <result property="sort"    column="sort"    />
        <result property="dutyUnit"    column="duty_unit"    />
        <result property="templateId"    column="template_id"    />
    </resultMap>

    <sql id="selectMilestonPlanVo">
        select id, milestone_time, description, status, item_id, sort ,duty_unit, template_id from t_mileston_plan
    </sql>

    <select id="selectMilestonPlanList" parameterType="MilestonPlan" resultMap="MilestonPlanResult">
        <include refid="selectMilestonPlanVo"/>
        where status = 1
            <if test="milestoneTime != null "> and milestone_time = #{milestoneTime}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="dutyUnit != null  and dutyUnit != ''"> and duty_unit = #{dutyUnit}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>

        order by sort
    </select>

    <select id="selectMilestonPlanById" parameterType="Long" resultMap="MilestonPlanResult">
        <include refid="selectMilestonPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertMilestonPlan" parameterType="MilestonPlan" useGeneratedKeys="true" keyProperty="id">
        insert into t_mileston_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="milestoneTime != null">milestone_time,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="itemId != null">item_id,</if>
            <if test="sort != null">sort,</if>
            <if test="dutyUnit != null">duty_unit,</if>
            <if test="templateId != null">template_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="milestoneTime != null">#{milestoneTime},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="dutyUnit != null">#{dutyUnit},</if>
            <if test="templateId != null">#{templateId},</if>
         </trim>
    </insert>

    <update id="updateMilestonPlan" parameterType="MilestonPlan">
        update t_mileston_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="milestoneTime != null">milestone_time = #{milestoneTime},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="dutyUnit != null">duty_unit = #{dutyUnit},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMilestonPlanById" parameterType="Long">
        update t_mileston_plan set status = 9
        where id = #{id}
    </update>

    <update id="deleteMilestonPlanByIds" parameterType="String">
        update t_mileston_plan set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
