package com.ruoyi.tiem.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TProjectAmount;
import com.ruoyi.tiem.service.ITProjectAmountService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 年度立项总金额Controller
 * 
 * <AUTHOR>
 * @date 2025-02-28
 */
@RestController
@RequestMapping("/system/amount")
public class TProjectAmountController extends BaseController
{
    @Autowired
    private ITProjectAmountService tProjectAmountService;

    /**
     * 查询年度立项总金额列表
     */
//    @PreAuthorize("@ss.hasPermi('system:amount:list')")
    @GetMapping("/list")
    public TableDataInfo list(TProjectAmount tProjectAmount)
    {
        startPage();
        List<TProjectAmount> list = tProjectAmountService.selectTProjectAmountList(tProjectAmount);
        return getDataTable(list);
    }

    /**
     * 根据年度查询立项总金额
     */
//    @PreAuthorize("@ss.hasPermi('system:amount:list')")
    @GetMapping("/amountOfYear")
    public AjaxResult amountOfYear(String year)
    {
        startPage();
        BigDecimal amount = tProjectAmountService.selectAmountOfYear(year);
        return success(amount);
    }

    /**
     * 导出年度立项总金额列表
     */
    @PreAuthorize("@ss.hasPermi('system:amount:export')")
    @Log(title = "年度立项总金额", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TProjectAmount tProjectAmount)
    {
        List<TProjectAmount> list = tProjectAmountService.selectTProjectAmountList(tProjectAmount);
        ExcelUtil<TProjectAmount> util = new ExcelUtil<TProjectAmount>(TProjectAmount.class);
        util.exportExcel(response, list, "年度立项总金额数据");
    }

    /**
     * 获取年度立项总金额详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:amount:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tProjectAmountService.selectTProjectAmountById(id));
    }

    /**
     * 新增年度立项总金额
     */
//    @PreAuthorize("@ss.hasPermi('system:amount:add')")
    @Log(title = "年度立项总金额", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TProjectAmount tProjectAmount)
    {
        return toAjax(tProjectAmountService.insertTProjectAmount(tProjectAmount));
    }

    /**
     * 修改年度立项总金额
     */
//    @PreAuthorize("@ss.hasPermi('system:amount:edit')")
    @Log(title = "年度立项总金额", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TProjectAmount tProjectAmount)
    {
        return toAjax(tProjectAmountService.updateTProjectAmount(tProjectAmount));
    }

    /**
     * 删除年度立项总金额
     */
    @PreAuthorize("@ss.hasPermi('system:amount:remove')")
    @Log(title = "年度立项总金额", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tProjectAmountService.deleteTProjectAmountByIds(ids));
    }

    /**
     * 新增或更新年度立项总金额
     */
    @PostMapping("/addOrEdit")
    public AjaxResult addOrEditAmount(@RequestBody List<TProjectAmount> prolist)
    {

        for (TProjectAmount projectAmount : prolist) {
            // 根据 year 和 itemCategory 查询是否存在相同记录
            TProjectAmount existingProjectAmount = tProjectAmountService.selectTProjectAmountByYearAndCategory(projectAmount.getYear(), projectAmount.getItemCategory());

            if (existingProjectAmount != null) {
                // 如果存在，更新 amount
                existingProjectAmount.setAmount(projectAmount.getAmount());
                tProjectAmountService.editTProjectAmount(existingProjectAmount);
            } else {
                // 如果不存在，插入新记录
                tProjectAmountService.insertTProjectAmount(projectAmount);
            }
        }
        return AjaxResult.success();
    }
}
