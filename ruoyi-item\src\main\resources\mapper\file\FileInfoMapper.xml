<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.FileInfoMapper">

    <resultMap type="FileInfo" id="FileInfoResult">
        <result property="id"    column="id"    />
        <result property="fileName"    column="file_name"    />
        <result property="source"    column="source"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="originalFileName"    column="original_file_name"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFileInfoVo">
        select id, file_name, source, file_path, file_url, create_by, create_time, status, original_file_name,remark from t_file_info
    </sql>

    <select id="selectFileInfoList" parameterType="FileInfo" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        where source is not null and status = 1
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="originalFileName != null  and originalFileName != ''"> and original_file_name like concat('%', #{originalFileName}, '%')</if>
        order by create_time desc
    </select>

    <select id="selectFileInfoById" parameterType="Long" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectByFileIds" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        where status = 1 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFileInfo" parameterType="FileInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_file_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">file_name,</if>
            <if test="source != null">source,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="originalFileName != null">original_file_name,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">#{fileName},</if>
            <if test="source != null">#{source},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="originalFileName != null">#{originalFileName},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFileInfo" parameterType="FileInfo">
        update t_file_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="source != null">source = #{source},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="originalFileName != null">original_file_name = #{originalFileName},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteFileInfoById" parameterType="Long">
        update t_file_info set status = 9
        where id = #{id}
    </update>

    <update id="deleteFileInfoByIds" parameterType="String">
        update t_file_info set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
