package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
/**
 * 项目信息对象 t_item_info
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
public class TItemInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String itemName;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String itemNumber;

    /** 项目负责人 */
    @Excel(name = "项目负责人")
    private String leader;

    /** 项目联系人 */
    @Excel(name = "项目联系人")
    private String contact;

    /** 电子邮件 */
    @Excel(name = "电子邮件")
    private String email;

    /** 项目地点 */
    @Excel(name = "项目地点")
    private String place;

    /** 项目实施必要性分析 */
    @Excel(name = "项目实施必要性分析")
    private String necessity;

    /** 项目实施可行性分析 */
    @Excel(name = "项目实施可行性分析")
    private String feasibility;

    /** 项目实施条件 */
    @Excel(name = "项目实施条件")
    private String conditions;

    /** 项目实施主要内容及相关预算 */
    @Excel(name = "项目实施主要内容及相关预算")
    private String content;

    /** 项目进度与计划安排 */
    @Excel(name = "项目进度与计划安排")
    private String arrange;

    /** 项目风险与不确定性分析 */
    @Excel(name = "项目风险与不确定性分析")
    private String risk;

    /** 预期经济社会效益 */
    @Excel(name = "预期经济社会效益")
    private String benefit;

    /** 详细内容 */
    private String details;

    /** 申报年度 */
    @Excel(name = "申报年度")
    private String applyYear;

    /** 申报单位 */
    @Excel(name = "申报单位")
    private String applyUnit;

    /** 申报单位部门id */
    private String applyUnitId;

    /** 合同编号 */
    private String contract;

    /** 申报金额 */
    @Excel(name = "概算金额")
    private BigDecimal applyAmount;

    /** 项目类别 */
    @Excel(name = "项目类别：房屋修缮，基础设施改造等")
    private String itemCategory;

    /** 是否政采分类 政采指标、非政采指标 */
    private String purchaseType;

    /** 政府预算经济分类 */
    private String economyType;

    /** 明细分类 */
    private String detailType;

    /** 立项数量 */
    private Integer projectNumber;

    /** 立项单价 */
    private BigDecimal projectPrice;

    /** 年初立项金额 */
    private BigDecimal beginYearProjectAmount;

    /** 立项调整金额 */
    private BigDecimal adjustProjectAmount;

    /** 总立项金额 */
    private BigDecimal totalProjectAmount;

    /** 合同数量 */
    private BigDecimal contractNumber;

    /** 合同单价 */
    private BigDecimal contractPrice;

    /** 合同金额 */
    private BigDecimal contractAmount;

    /** 供应商 */
    private String supplier;

    /** 结算金额 */
    private BigDecimal calculateAmount;

    /** 动态成本 */
    private BigDecimal dynamicCost;

    // @Excel(name = "项目状态", readConverterExp = "1=已提交,2=院长已审批,3=归口单位经办人已审批,4=归口单位领导已审批,5=国资处经办人已审批")
    @Excel(name = "项目状态", readConverterExp = "1=已提交,2=院长已审批,3=归口单位领导已审批,4=专业审核通过,5=综合评审通过,6=入库评审通过,7=入库评审通过,8=执行计划排序")
    private Integer flow;

    /** 状态 1：正常 2：草稿 9：删除 */
    private String status;

    /**
     * 是否可以审核 1:可审核
     */
    private Integer isAudit = 0 ;

    /** 工信部审定 数量 */
    private Long miitNumber;

    /** 工信部审定 单价 */
    private BigDecimal miitPrice;

    /** 工信部审定 合计 */
    private BigDecimal mittAmount;

    /** 合同id */
    private String contractId;

    /** 累计支付金额 */
    private BigDecimal paymentAmount;

    /** 累计开票金额 */
    private BigDecimal invoiceAmount;

    /** 执行率 */
    private String executeRate;

    /** 签约率 */
    private String signRate;

    /** 申报人联系方式 */
    @Excel(name = "申报人联系方式")
    private String createPhone;

    /** 固定资产托底责任人 */
    private String assetsPerson;

    /** 固定资产托底责任人联系方式 */
    private String assetsPersonPhone;

    /** 归口单位 */
    @Excel(name = "归口单位")
    private String focalUnit;

    /** 归口单位id */
    private Long focalUnitId;

    /** 预算表文件id */
    private String budgetFileIds;

    /** 预算金额 */
    private BigDecimal budget;

    /** 校内评审资料ids */
    private String schoolReviewFileIds;

    /** 工信部评审资料ids */
    private String miitReviewFileIds;

    /** 深化审计及概算资料ids */
    private String deepDesignFileIds;

    /** 学校库排序 */
    private Long schoolSort;

    /** 工信部库排序 */
    private Long ministrySort;

    /** 专业审核排序 */
    @Excel(name = "专业审核排序")
    private Long majorSort;

    /** 专业审核意见表ids */
    private String majorFileIds;

    /** 综合评审排序 */
    @Excel(name = "综合评审排序")
    private String compoSort;

    /** 综合评审意见表ids */
    private String compoFileIds;

    /** 入库评审-评审建议预算 */
    @Excel(name = "评审建议预算")
    private BigDecimal storeBudget;

    /** 入库评审-评审意见 */
    @Excel(name = "评审意见")
    private String storeOpinion;

    /** 入库评审-上传报告ids */
    private String storeFileIds;

    /** 执行计划排序 */
    @Excel(name = "执行计划排序")
    private Long planSort;

    /** 执行计划排序资料ids */
    private String planFileIds;

    /** 项目申报人id */
    private Long createById;

    /**
     * 是否可以修改0:不可修改 1:申报人可以修改 2:管理员可修改
     */
    private Integer isEdit = 0;

    /** 项目类别 */
    @Excel(name = "项目类别：安防，消防等")
    private String projectCategory;

    /** 入学校库年度 */
    private String schoolEnterYear;

    /** 入工信部库年度 */
    @Excel(name = "入工信部库年度")
    private String ministryEnterYear;

    /** 立项执行年度 */
    @Excel(name = "立项执行年度")
    private String executeYear;

    /** 项目建设周期 */
    private String cycles;

    /** 项目预期目标 */
    private String goal;

    /** 实际完成情况 */
    private String actualCompletion;

    /** 测算依据及说明 */
    private String explains;

    /**
     * 项目ids
     */
    private String ids;

    /**
     * 项目节点
     */
    private String flows;

    /**
     * 项目类别
     */
    private String itemCategories;

    /**
     * 合约规划集合V1
     */
    private List<ContractPlan> contractPlanList;

    /**
     * 合约规划集合V2
     */
    private List<ContractPlan> contractPlanListV2;

    /**
     * 绩效信息集合
     */
    private List<Performance> performanceList;

    /**
     * 里程碑集合
     */
    private List<MilestonPlan> milestonPlanList;

    @TableField(exist = false)
    private String currentFlowStatus;

    /** 是否只筛选待审核项目 */
    @TableField(exist = false)
    private Boolean pendingAudit;
}
