package com.ruoyi.tiem.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 里程碑计划信息对象 t_mileston_plan
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public class MilestonPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 里程碑时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "里程碑时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date milestoneTime;

    /** 里程碑描述 */
    @Excel(name = "里程碑描述")
    private String description;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 责任单位 */
    @Excel(name = "责任单位")
    private String dutyUnit;

    /** 模版 */
    @Excel(name = "模版")
    private Long templateId;

    /** 是否可删除 */
    private boolean canDelete;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setMilestoneTime(Date milestoneTime)
    {
        this.milestoneTime = milestoneTime;
    }

    public Date getMilestoneTime()
    {
        return milestoneTime;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }

    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort()
    {
        return sort;
    }

    public String getDutyUnit() {
        return dutyUnit;
    }

    public void setDutyUnit(final String dutyUnit) {
        this.dutyUnit = dutyUnit;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(final Long templateId) {
        this.templateId = templateId;
    }

    public boolean isCanDelete() {
        return canDelete;
    }

    public void setCanDelete(boolean canDelete) {
        this.canDelete = canDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("milestoneTime", getMilestoneTime())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("itemId", getItemId())
            .append("sort", getSort())
            .toString();
    }
}
