package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemCategoryGoal;
import com.ruoyi.tiem.service.ITItemCategoryGoalService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目类别目标Controller
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@RestController
@RequestMapping("/system/goal")
public class TItemCategoryGoalController extends BaseController
{
    @Autowired
    private ITItemCategoryGoalService tItemCategoryGoalService;

    /**
     * 查询项目类别目标列表
     */
//    @PreAuthorize("@ss.hasPermi('system:goal:list')")
    @GetMapping("/list")
    public TableDataInfo list(TItemCategoryGoal tItemCategoryGoal)
    {
        startPage();
        List<TItemCategoryGoal> list = tItemCategoryGoalService.selectTItemCategoryGoalList(tItemCategoryGoal);
        return getDataTable(list);
    }

    /**
     * 导出项目类别目标列表
     */
//    @PreAuthorize("@ss.hasPermi('system:goal:export')")
    @Log(title = "项目类别目标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemCategoryGoal tItemCategoryGoal)
    {
        List<TItemCategoryGoal> list = tItemCategoryGoalService.selectTItemCategoryGoalList(tItemCategoryGoal);
        ExcelUtil<TItemCategoryGoal> util = new ExcelUtil<TItemCategoryGoal>(TItemCategoryGoal.class);
        util.exportExcel(response, list, "项目类别目标数据");
    }

    /**
     * 获取项目类别目标详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:goal:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tItemCategoryGoalService.selectTItemCategoryGoalById(id));
    }

    /**
     * 新增项目类别目标
     */
//    @PreAuthorize("@ss.hasPermi('system:goal:add')")
    @Log(title = "项目类别目标", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemCategoryGoal tItemCategoryGoal)
    {
        return toAjax(tItemCategoryGoalService.insertTItemCategoryGoal(tItemCategoryGoal));
    }

    /**
     * 修改项目类别目标
     */
//    @PreAuthorize("@ss.hasPermi('system:goal:edit')")
    @Log(title = "项目类别目标", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemCategoryGoal tItemCategoryGoal)
    {
        return toAjax(tItemCategoryGoalService.updateTItemCategoryGoal(tItemCategoryGoal));
    }

    /**
     * 删除项目类别目标
     */
//    @PreAuthorize("@ss.hasPermi('system:goal:remove')")
    @Log(title = "项目类别目标", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tItemCategoryGoalService.deleteTItemCategoryGoalByIds(ids));
    }
}
