package com.ruoyi.tiem.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.MilestonPlan;
import com.ruoyi.tiem.service.IMilestonPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 里程碑计划信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/mileston/plan")
public class MilestonPlanController extends BaseController
{
    @Autowired
    private IMilestonPlanService milestonPlanService;

    /**
     * 查询里程碑计划信息列表
     */
    @PreAuthorize("@ss.hasPermi('mileston:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(MilestonPlan milestonPlan)
    {
        startPage();
        List<MilestonPlan> list = milestonPlanService.selectMilestonPlanList(milestonPlan);
        return getDataTable(list);
    }

    /**
     * 导出里程碑计划信息列表
     */
    @PreAuthorize("@ss.hasPermi('mileston:plan:export')")
    @Log(title = "里程碑计划信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MilestonPlan milestonPlan)
    {
        List<MilestonPlan> list = milestonPlanService.selectMilestonPlanList(milestonPlan);
        ExcelUtil<MilestonPlan> util = new ExcelUtil<MilestonPlan>(MilestonPlan.class);
        util.exportExcel(response, list, "里程碑计划信息数据");
    }

    /**
     * 获取里程碑计划信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('mileston:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(milestonPlanService.selectMilestonPlanById(id));
    }

    /**
     * 新增里程碑计划信息
     */
    @PreAuthorize("@ss.hasPermi('mileston:plan:add')")
    @Log(title = "里程碑计划信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MilestonPlan milestonPlan)
    {
        return toAjax(milestonPlanService.insertMilestonPlan(milestonPlan));
    }

    /**
     * 修改里程碑计划信息
     */
    @PreAuthorize("@ss.hasPermi('mileston:plan:edit')")
    @Log(title = "里程碑计划信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MilestonPlan milestonPlan)
    {
        return toAjax(milestonPlanService.updateMilestonPlan(milestonPlan));
    }

    /**
     * 删除里程碑计划信息
     */
    @PreAuthorize("@ss.hasPermi('mileston:plan:remove')")
    @Log(title = "里程碑计划信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(milestonPlanService.deleteMilestonPlanByIds(ids));
    }


    // 定义常量
    private static final String NON_GOVERNMENT_PURCHASE = "非政采指标";
    private static final String GOVERNMENT_PURCHASE = "政采指标";
    private static final String EQUIPMENT = "设备";
    private static final String ENGINEERING = "工程";
    private static final BigDecimal BUDGET_THRESHOLD = new BigDecimal("200000");

    /**
     * 根据参数获取里程碑模板信息
     * @param itemInfo
     * @return
     */
    @GetMapping("/getMilestonPlanTemplate")
    public AjaxResult getMilestonPlanTemplate(TItemInfo itemInfo){
        Long templateId = 1L;
        if (itemInfo.getPurchaseType() != null && itemInfo.getBudget() != null && itemInfo.getItemCategory() != null) {
            if (itemInfo.getPurchaseType().equals(NON_GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) < 0 && itemInfo.getItemCategory().equals(ENGINEERING)) {
                templateId = 1L;
            } else if (itemInfo.getPurchaseType().equals(NON_GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) >= 0 && itemInfo.getItemCategory().equals(ENGINEERING)) {
                templateId = 3L;
            } else if (itemInfo.getPurchaseType().equals(GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) < 0 && itemInfo.getItemCategory().equals(ENGINEERING)) {
                templateId = 2L;
            } else if (itemInfo.getPurchaseType().equals(GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) >= 0 && itemInfo.getItemCategory().equals(ENGINEERING)) {
                templateId = 4L;
            } else if (itemInfo.getPurchaseType().equals(NON_GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) < 0 && itemInfo.getItemCategory().equals(EQUIPMENT)) {
                templateId = 5L;
            } else if (itemInfo.getPurchaseType().equals(NON_GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) >= 0 && itemInfo.getItemCategory().equals(EQUIPMENT)) {
                templateId = 7L;
            } else if (itemInfo.getPurchaseType().equals(GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) < 0 && itemInfo.getItemCategory().equals(EQUIPMENT)) {
                templateId = 6L;
            } else if (itemInfo.getPurchaseType().equals(GOVERNMENT_PURCHASE) && itemInfo.getBudget().compareTo(BUDGET_THRESHOLD) >= 0 && itemInfo.getItemCategory().equals(EQUIPMENT)) {
                templateId = 8L;
            }
        }
        MilestonPlan milestonPlan = new MilestonPlan();
        milestonPlan.setTemplateId(templateId);
        // 根据模板id查询相关里程碑模版
        List<MilestonPlan> milestonPlans = milestonPlanService.selectMilestonPlanList(milestonPlan);
        for (MilestonPlan plan : milestonPlans) {
            plan.setId(null);
            plan.setTemplateId(null);
        }

        String[] engineeringType = new String[]{"意向公开", "发布招标公告", "主合同签订", "项目验收", "项目送审"};
        String[] equipmentType = new String[]{"意向公开", "发布招标公告", "合同签订", "项目验收"};


        if (itemInfo.getItemCategory() != null) {
            if (itemInfo.getItemCategory().equals(ENGINEERING)) {
                //*/工程类项目
                for (MilestonPlan plan : milestonPlans) {
                    if (Arrays.asList(engineeringType).contains(plan.getDescription())) {
                        plan.setCanDelete(true);
                    }
                }
            } else if (itemInfo.getItemCategory().equals(EQUIPMENT)) {
                //*/非工程类项目
                for (MilestonPlan plan : milestonPlans) {
                    if (Arrays.asList(equipmentType).contains(plan.getDescription())) {
                        plan.setCanDelete(true);
                    }
                }
            }
        } else {
            for (MilestonPlan plan : milestonPlans) {
                if (Arrays.asList(engineeringType).contains(plan.getDescription())) {
                    plan.setCanDelete(true);
                }
            }
        }
        return AjaxResult.success(milestonPlans);
    }
}
