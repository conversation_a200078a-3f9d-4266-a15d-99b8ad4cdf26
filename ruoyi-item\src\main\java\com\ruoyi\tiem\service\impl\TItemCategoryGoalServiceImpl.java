package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TItemCategoryGoal;
import com.ruoyi.tiem.mapper.TItemCategoryGoalMapper;
import com.ruoyi.tiem.service.ITItemCategoryGoalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目类别目标Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class TItemCategoryGoalServiceImpl implements ITItemCategoryGoalService
{
    @Autowired
    private TItemCategoryGoalMapper tItemCategoryGoalMapper;

    /**
     * 查询项目类别目标
     * 
     * @param id 项目类别目标主键
     * @return 项目类别目标
     */
    @Override
    public TItemCategoryGoal selectTItemCategoryGoalById(Long id)
    {
        return tItemCategoryGoalMapper.selectTItemCategoryGoalById(id);
    }

    /**
     * 查询项目类别目标列表
     * 
     * @param tItemCategoryGoal 项目类别目标
     * @return 项目类别目标
     */
    @Override
    public List<TItemCategoryGoal> selectTItemCategoryGoalList(TItemCategoryGoal tItemCategoryGoal)
    {
        return tItemCategoryGoalMapper.selectTItemCategoryGoalList(tItemCategoryGoal);
    }

    /**
     * 新增项目类别目标
     * 
     * @param tItemCategoryGoal 项目类别目标
     * @return 结果
     */
    @Override
    public int insertTItemCategoryGoal(TItemCategoryGoal tItemCategoryGoal)
    {
        return tItemCategoryGoalMapper.insertTItemCategoryGoal(tItemCategoryGoal);
    }

    /**
     * 修改项目类别目标
     * 
     * @param tItemCategoryGoal 项目类别目标
     * @return 结果
     */
    @Override
    public int updateTItemCategoryGoal(TItemCategoryGoal tItemCategoryGoal)
    {
        return tItemCategoryGoalMapper.updateTItemCategoryGoal(tItemCategoryGoal);
    }

    /**
     * 批量删除项目类别目标
     * 
     * @param ids 需要删除的项目类别目标主键
     * @return 结果
     */
    @Override
    public int deleteTItemCategoryGoalByIds(Long[] ids)
    {
        return tItemCategoryGoalMapper.deleteTItemCategoryGoalByIds(ids);
    }

    /**
     * 删除项目类别目标信息
     * 
     * @param id 项目类别目标主键
     * @return 结果
     */
    @Override
    public int deleteTItemCategoryGoalById(Long id)
    {
        return tItemCategoryGoalMapper.deleteTItemCategoryGoalById(id);
    }
}
