package com.ruoyi.tiem.mapper;

import java.util.List;
import com.ruoyi.tiem.domain.TContractInfo;
import com.ruoyi.tiem.domain.TItemInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 合同信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface TContractInfoMapper
{
    /**
     * 查询合同信息
     *
     * @param id 合同信息主键
     * @return 合同信息
     */
    public TContractInfo selectTContractInfoById(Long id);

    /**
     * 查询合同信息列表
     *
     * @param tContractInfo 合同信息
     * @return 合同信息集合
     */
    public List<TContractInfo> selectTContractInfoList(TContractInfo tContractInfo);

    /**
     * 新增合同信息
     *
     * @param tContractInfo 合同信息
     * @return 结果
     */
    public int insertTContractInfo(TContractInfo tContractInfo);

    /**
     * 修改合同信息
     *
     * @param tContractInfo 合同信息
     * @return 结果
     */
    public int updateTContractInfo(TContractInfo tContractInfo);

    /**
     * 删除合同信息
     *
     * @param id 合同信息主键
     * @return 结果
     */
    public int deleteTContractInfoById(Long id);

    /**
     * 批量删除合同信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTContractInfoByIds(Long[] ids);

    /**
     * 根据项目id批量删除合同信息
     *
     * @param ids 需要删除的项目id数组
     * @return 结果
     */
    public int deleteTContractInfoByItemIds(Long[] ids);

    /**
     * 根据项目ids 查询项目信息
     * @param split
     * @return
     */
    List<TItemInfo> selectByItemIds(@Param("ids") String[] split);

    /**
     * 更新累计开票金额、累计付款金额
     * @param contractId
     */
    void updateInvoiceAndPayment(@Param("id") Long contractId);
}
