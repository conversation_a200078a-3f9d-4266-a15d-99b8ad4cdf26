package com.ruoyi;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * 职工数据同步客户端
 * 用于从外部API获取职工数据并同步到系统中
 * <p>
 * 实现了CommandLineRunner接口，可以在Spring Boot应用启动时执行
 * 但默认不会自动执行同步，需要手动调用main方法或syncDepartmentData方法
 */
@Component
public class StaffClient implements CommandLineRunner {

    /**
     * 注入职工数据访问层Mapper
     */
    @Resource
    private SysUserMapper userMapper;

    @Autowired
    private ISysUserService userService;

    /**
     * 注入部门数据访问层Mapper
     */
    @Resource
    private SysDeptMapper deptMapper;

    /**
     * Spring Boot启动时执行的方法
     * 当前配置为不自动执行同步，需要手动调用main方法
     */
    @Override
    public void run(String... args) {
        // 在程序启动时不自动执行同步，需要手动调用main方法执行
    }

    /**
     * 主方法，用于手动启动数据同步
     * 先初始化Spring应用上下文，再获取StaffClient实例调用同步方法
     *
     * @param args 命令行参数，未使用
     */
    public static void main(String[] args) {
        // 创建Spring上下文环境并启动应用
        ConfigurableApplicationContext context = SpringApplication.run(RuoYiApplication.class, args);

        try {
            // 从Spring容器中获取StaffClient实例
            StaffClient client = context.getBean(StaffClient.class);

            // 调用同步方法
            System.out.println("开始执行职工数据同步...");
            client.syncDepartmentData();
            System.out.println("职工数据同步结束！");


        } catch (Exception e) {
            System.out.println("职工数据同步异常：" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭Spring上下文
            if (context != null) {
                context.close();
            }
        }
    }

    /**
     * 同步职工数据主方法
     * 负责从外部API获取数据，然后调用处理方法进行同步
     * 移除了@Transactional注解，避免整体回滚
     */
    public void syncDepartmentData() {
        HttpURLConnection connection = null;
        try {
            // API访问地址
            String url = "http://ods4.nuaa.edu.cn/openApi/API/getREST_SJJS_JZGJBXX_QL_df44ff0f61b146dbb0bef9e9304547a1";

            // 构建请求参数
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("page", 1);           // 页码，从第1页开始
            jsonObject.put("pagesize", 20000);    // 每页数据量，设置为2000以获取全量数据

            // 以下为可选参数，当前未使用
            // JSONObject orderParams = new JSONObject();
            // orderParams.put("排序字段名", "DESC");
            // jsonObject.put("order", orderParams);
            //orderParams 为排序参数,根据传入的排序字段与排序规则对数据进行排序
            // JSONObject params = new JSONObject();
            // params.put("入参参数名", "入参参数值");
            // jsonObject.put("params", params);

            // 创建HTTP连接
            URL apiUrl = new URL(url);
            connection = (HttpURLConnection) apiUrl.openConnection();
            connection.setRequestMethod("POST");        // 设置请求方法为POST
            connection.setConnectTimeout(10000);        // 连接超时时间：10秒
            connection.setReadTimeout(10000);           // 读取超时时间：10秒

            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json");  // 内容类型为JSON
            connection.setRequestProperty("applyId", "37226788704198656");      // 应用ID
            connection.setRequestProperty("secretKey", "a2c4f2f1c28a4bb9b64b762db1ff50a0"); // 密钥

            // 设置允许输入输出
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 写入请求体（JSON格式）
            String requestBody = jsonObject.toString();
            try (OutputStream os = connection.getOutputStream()) {
                byte[] requestBodyBytes = requestBody.getBytes("UTF-8");
                os.write(requestBodyBytes, 0, requestBodyBytes.length);
                os.flush();
            }

            // 读取响应
            StringBuilder response = new StringBuilder();
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {  // HTTP状态码200表示成功
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                // 处理响应数据并同步到数据库
                System.out.println("开始同步职工数据，总记录数：" + response.toString());
                processAndSyncData(response.toString());

            } else {
                System.out.println("请求失败，响应码：" + responseCode);
            }

        } catch (MalformedURLException e) {
            // URL格式异常
            e.printStackTrace();
        } catch (IOException e) {
            // IO异常
            e.printStackTrace();
        } finally {
            // 断开连接
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 处理API返回的数据并同步到sys_user表
     * 移除了@Transactional注解，改为对每条记录单独处理
     *
     * @param responseData API返回的JSON字符串
     */
    public void processAndSyncData(String responseData) {
        try {
            // 解析JSON响应
            JSONObject jsonResponse = JSONObject.parseObject(responseData);

            String message = jsonResponse.getString("msg");
            if ("成功".equals(message)) {
                // 获取数据记录
                JSONObject data = jsonResponse.getJSONObject("data");
                // 获取Rows属性下的字段数据记录
                JSONArray records = data.getJSONArray("Rows");
                int total = records.size();

                System.out.println("开始同步职工数据，总记录数：" + total);

                // 初始化计数器
                int insertCount = 0; // 新增记录计数
                int updateCount = 0; // 更新记录计数
                int errorCount = 0;  // 错误记录计数

                for (int i = 0; i < records.size(); i++) {
                    JSONObject deptJson = records.getJSONObject(i);
                    String userName = deptJson.getString("xgh"); // 获取学工号
                    String nickName = deptJson.getString("xm"); // 获取姓名
                    String email = deptJson.getString("dzxx");

                    String xzdwdm = deptJson.getString("xzdwdm");
                    String gwlbdm = deptJson.getString("gwlbdm");
                    String gwlbdmText = deptJson.getString("gwlbdm_text");
                    String ksdwdm = deptJson.getString("ksdwdm");
                    String ksdwdmText = deptJson.getString("ksdwdm_text");
                    String ywdwdm = deptJson.getString("ywdwdm");
                    String ywdwdmText = deptJson.getString("ywdwdm_text");
                    String szxqdm = deptJson.getString("szxqdm");
                    String szxqdmText = deptJson.getString("szxqdm_text");

                    try {
                        // 在单独的事务中处理每条记录
                        boolean isUpdate = processUserRecord(userName, nickName, email, xzdwdm, gwlbdm, gwlbdmText, ksdwdm, ksdwdmText, ywdwdm, ywdwdmText, szxqdm, szxqdmText, deptJson);

                        // 更新计数
                        if (isUpdate) {
                            updateCount++;
                        } else {
                            insertCount++;
                        }
                    } catch (Exception e) {
                        System.out.println("处理用户 " + nickName + " 时出错: " + e.getMessage());
                        errorCount++;
                    }
                }

                // 输出最终处理结果统计
                System.out.println("用户数据同步完成！新增：" + insertCount + " 条，更新：" + updateCount + " 条，错误：" + errorCount + " 条");
            } else {
                System.out.println("API返回失败：" + jsonResponse.getString("msg"));
            }

        } catch (Exception e) {
            System.out.println("处理职工数据时发生异常：");
            e.printStackTrace();
            // 不再抛出异常，避免导致整体回滚
        }
    }

    /**
     * 处理单个用户记录，在独立事务中执行
     *
     * @return true表示更新操作，false表示新增操作
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean processUserRecord(String userName, String nickName, String email, String xzdwdm, String gwlbdm, String gwlbdmText, String ksdwdm, String ksdwdmText, String ywdwdm, String ywdwdmText, String szxqdm, String szxqdmText, JSONObject rawData) {

        SysUser sysUser = selectUserByUserName(userName);

        if (sysUser != null) {
            // 用户已存在，执行更新操作
            sysUser.setNickName(nickName);
            sysUser.setEmail(email);

            sysUser.setUpdateBy("admin");
            sysUser.setUpdateTime(new Date());
            sysUser.setStatus("0");
            sysUser.setDelFlag("0");

            sysUser.setXzdwdm(xzdwdm);
            sysUser.setGwlbdm(gwlbdm);
            sysUser.setGwlbdmText(gwlbdmText);
            sysUser.setKsdwdm(ksdwdm);
            sysUser.setKsdwdmText(ksdwdmText);
            sysUser.setYwdwdm(ywdwdm);
            sysUser.setYwdwdmText(ywdwdmText);
            sysUser.setSzxqdm(szxqdm);
            sysUser.setSzxqdmText(szxqdmText);

            // 根据xzdwdm设置部门ID
            Long deptId = selectDeptIdByDeptCode(xzdwdm);
            if (deptId != null) {
                sysUser.setDeptId(deptId);
            }

            // 执行更新
            userMapper.updateUser(sysUser);
            System.out.println("更新用户: " + nickName + " (代码: " + userName + ")");
            return true;
        } else {
            // 职工不存在，创建新职工
            SysUser sysUserItem = new SysUser();

            sysUserItem.setUserName(userName);
            sysUserItem.setNickName(nickName);
            sysUserItem.setEmail(email);
            sysUserItem.setPassword(SecurityUtils.encryptPassword("Aa123456!"));
            sysUserItem.setRoleIds(new Long[]{100L});
            sysUserItem.setStatus("0");
            sysUserItem.setDelFlag("0");
            sysUserItem.setCreateBy("admin");
            sysUserItem.setCreateTime(new Date());

            sysUserItem.setXzdwdm(xzdwdm);
            sysUserItem.setGwlbdm(gwlbdm);
            sysUserItem.setGwlbdmText(gwlbdmText);
            sysUserItem.setKsdwdm(ksdwdm);
            sysUserItem.setKsdwdmText(ksdwdmText);
            sysUserItem.setYwdwdm(ywdwdm);
            sysUserItem.setYwdwdmText(ywdwdmText);
            sysUserItem.setSzxqdm(szxqdm);
            sysUserItem.setSzxqdmText(szxqdmText);

            // 根据xzdwdm设置部门ID
            Long deptId = selectDeptIdByDeptCode(xzdwdm);
            if (deptId != null) {
                sysUserItem.setDeptId(deptId);
            }

            System.out.println("打印新增数据：" + sysUserItem.toString());
            // 新增用户
            userService.insertUser(sysUserItem);
            System.out.println("创建用户: " + nickName + " (账号名称: " + userName + ")");
            return false;
        }
    }

    /**
     * 根据学工号查询职工信息
     *
     * @param userName 职工代码
     * @return 职工信息对象，如果不存在返回null
     */
    private SysUser selectUserByUserName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return null;
        }
        // 直接使用Mapper提供的方法
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 根据部门代码查询部门ID
     *
     * @param deptCode 部门代码
     * @return 部门ID，如果不存在返回null
     */
    private Long selectDeptIdByDeptCode(String deptCode) {
        if (StringUtils.isEmpty(deptCode)) {
            return null;
        }
        // 查询部门信息，通过dm字段匹配xzdwdm
        SysDept depts = deptMapper.selectDeptByDm(deptCode);
        return depts != null ? depts.getDeptId() : null;
    }


}
