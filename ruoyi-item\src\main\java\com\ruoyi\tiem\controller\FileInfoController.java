package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.FileInfo;
import com.ruoyi.tiem.service.IFileInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件Controller
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@RestController
@RequestMapping("/file/info")
public class FileInfoController extends BaseController
{
    @Autowired
    private IFileInfoService fileInfoService;

    /**
     * 查询文件列表
     */
    // @PreAuthorize("@ss.hasPermi('file:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(FileInfo fileInfo)
    {
        startPage();
        List<FileInfo> list = fileInfoService.selectFileInfoList(fileInfo);
        return getDataTable(list);
    }

    /**
     * 导出文件列表
     */
    @PreAuthorize("@ss.hasPermi('file:info:export')")
    @Log(title = "文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FileInfo fileInfo)
    {
        List<FileInfo> list = fileInfoService.selectFileInfoList(fileInfo);
        ExcelUtil<FileInfo> util = new ExcelUtil<FileInfo>(FileInfo.class);
        util.exportExcel(response, list, "文件数据");
    }

    /**
     * 获取文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('file:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(fileInfoService.selectFileInfoById(id));
    }

    /**
     * 新增文件
     */
    @PreAuthorize("@ss.hasPermi('file:info:add')")
    @Log(title = "文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FileInfo fileInfo)
    {
        return toAjax(fileInfoService.insertFileInfo(fileInfo));
    }


    @PostMapping("/uploadFile")
    public AjaxResult instructionUploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            // String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            String url = fileName.replace("/profile/", "http://***************:8112/file/upload");
            //插入文件信息
            FileInfo instructionFile = new FileInfo();
            instructionFile.setFileName(FileUtils.getName(fileName));
            instructionFile.setFilePath(fileName);
            instructionFile.setFileUrl(url);
            instructionFile.setOriginalFileName(file.getOriginalFilename());
            int row = fileInfoService.insertFileInfo(instructionFile);
            FileInfo instructionFile1 = fileInfoService.selectFileInfoById(instructionFile.getId());
            ajax.put("data",instructionFile1);
            ajax.put("FileId", instructionFile.getId());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改文件
     */
    @PreAuthorize("@ss.hasPermi('file:info:edit')")
    @Log(title = "文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FileInfo fileInfo)
    {
        return toAjax(fileInfoService.updateFileInfo(fileInfo));
    }

    /**
     * 删除文件
     */
    @PreAuthorize("@ss.hasPermi('file:info:remove')")
    @Log(title = "文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(fileInfoService.deleteFileInfoByIds(ids));
    }


    @GetMapping("/selectByFileIds")
    public AjaxResult selectByFileIds(@RequestParam("fileIds") String fileIds) {
        List<FileInfo> list = fileInfoService.selectByFileIds(fileIds);
        return AjaxResult.success(list);
    }
}
