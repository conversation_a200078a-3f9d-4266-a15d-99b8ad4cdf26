package com.ruoyi.tiem.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.ruoyi.tiem.domain.ExecutedItemVO;
import com.ruoyi.tiem.domain.ItemRank;
import com.ruoyi.tiem.domain.TItemInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 项目信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface TItemInfoMapper {
    /**
     * 查询项目信息
     *
     * @param id 项目信息主键
     * @return 项目信息
     */
    public TItemInfo selectTItemInfoById(Long id);

    /**
     * 根据项目名称查询项目信息
     *
     * @param itemName 项目名称
     * @return 项目信息
     */
    public TItemInfo selectTItemInfoByItemName(String itemName);

    /**
     * 查询项目信息列表
     *
     * @param tItemInfo 项目信息
     * @return 项目信息集合
     */
    public List<TItemInfo> selectTItemInfoList(TItemInfo tItemInfo);

    /**
     * 根据执行年度和项目类别查询已立项执行项目
     *
     * @param info 项目信息
     * @return 项目信息集合
     */
    public List<TItemInfo> selectExecutedItemInfoList(TItemInfo info);

    /**
     * 新增项目信息
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    public int insertTItemInfo(TItemInfo tItemInfo);

    /**
     * 修改项目信息
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    public int updateTItemInfo(TItemInfo tItemInfo);

    /**
     * 删除项目信息
     *
     * @param id 项目信息主键
     * @return 结果
     */
    public int deleteTItemInfoById(Long id);

    /**
     * 批量删除项目信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTItemInfoByIds(Long[] ids);

    /**
     * 根据项目id查询项目列表信息
     *
     * @param split
     * @return
     */
    List<TItemInfo> selectByItemIds(@Param("ids") String[] split);

    /**
     * 更新项目关联合同信息
     *
     * @param ids
     * @param id
     */
    void updateItemContract(@Param("ids") String[] ids, @Param("contractId") Long id);

    /**
     * 更新项目合同金额
     *
     * @param itemIds
     */
    void updateContractAmount(@Param("id") Long itemIds);

    /**
     * 更新项目累计开票金额、累计付款金额
     *
     * @param itemIds
     */
    void updateInvoiceAndPayment(@Param("id") Long itemIds);

    /**
     * 更新项目执行率
     *
     * @param itemIds
     */
    void updateExecuteRate(@Param("id") Long itemIds);

    /**
     * 更新项目签约率
     *
     * @param itemIds
     */
    void updateSignRate(@Param("id") Long itemIds);

    /**
     * 批量更新项目流程状态
     *
     * @param itemIds
     * @param flow
     */
    void updateItemFlow(@Param("ids") List<String> itemIds, @Param("flow") Integer flow);

    /**
     * 批量更新项目流程状态Two
     *
     * @param itemIds
     * @param flow
     */
    void updateItemFlowTwo(@Param("ids") List<String> itemIds, @Param("flow") Integer flow);

    /**
     * 批量更新入学校库年度
     *
     * @param itemIds
     * @param
     */
    void updateSchoolEnterYear(@Param("ids") List<String> itemIds, @Param("year") String year);

    /**
     * 批量更新入工信部库年度
     *
     * @param itemIds
     * @param
     */
    void updateMinistryEnterYear(@Param("ids") List<String> itemIds, @Param("year") String year);

    /**
     * 批量更新立项执行年度
     *
     * @param itemIds
     * @param
     */
    void updateExecuteYear(@Param("ids") List<String> itemIds, @Param("year") String year);

    /**
     * 获取工作台统计接口
     *
     * @return
     */
    Map getStatisticsOne(TItemInfo tItemInfo);

    /**
     * 获取项目资金分配统计
     *
     * @return
     */
    List<Map> getStatisticsTwo(String year);

    /**
     * 根据项目id获取项目资金分配统计
     *
     * @return
     */
    List<Map> getStatisticsTwoByItemIds(List<Long> ids);

    /**
     * 根据单位id获取对应项目id
     *
     * @param deptId
     * @return
     */
    List<Long> getItemIdsByDeptId(Long deptId);

    /**
     * 根据归口单位id获取对应项目id
     *
     * @param focalUnitId
     * @return
     */
    List<Long> getItemIdsByFocalUnitId(Long focalUnitId);

    /**
     * 获取项目执行率
     *
     * @return
     */
    List<Map> getStatisticsThree(Long itemId);

    /**
     * 获取合同占比
     *
     * @return
     */
    List<Map> getStatisticsFour(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 获取三大基础类别一级指标占比情况
     *
     * @return
     */
    List<Map> getAmountOfBasicCategory(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("itemCategory") String itemCategory);

    /**
     * 获取项目合计统计信息
     *
     * @param tItemInfo 项目信息
     * @return
     */
    Map getItemStatistics(TItemInfo tItemInfo);

    /**
     * 获取项目排名
     *
     * @param year
     * @return
     */
    List<ItemRank> getItemSort(String year);

    /**
     * 获取比目标项目执行率更低的项目数量
     *
     * @param executeRate
     * @return
     */
    Integer getItemNumberLowerRank(BigDecimal executeRate);

    /**
     * 获取项目执行模块列表数据
     *
     * @return
     */
    Map executionItemInfo(TItemInfo itemInfo);

    /**
     * 获取某项目类别下具体项目列表数据
     *
     * @param executeYear  项目立项执行年份
     * @param itemCategory 项目类别
     * @return
     */
    List<Map> executionItemDetail(@Param("executeYear") String executeYear, @Param("itemCategory") String itemCategory);

    /**
     * 获取某项目下具体合同列表数据
     *
     * @param itemId 项目id
     * @return
     */
    List<Map> executionContractDetail(@Param("executeYear") String executeYear, @Param("itemId") Long itemId);

    /**
     * 查询执行项目信息
     *
     * @param executedItemVO
     * @return
     */
    List<Map> queryExecutedItemInfo(ExecutedItemVO executedItemVO);

    /**
     * 获取绩效评价模块列表数据
     *
     * @return
     */
    Map performanceItemInfo(TItemInfo itemInfo);

    BigDecimal queryPaymentAmount(@Param("year") String year, @Param("itemId") String itemId);

    /**
     * 查询已执行完成项目
     *
     * @param executeYear  项目立项执行年份
     * @param itemCategory 项目类别
     * @return
     */
    List<Map> performanceItemDetail(@Param("executeYear") String executeYear, @Param("itemCategory") String itemCategory);

    /**
     * 根据执行年度和项目类别查询已执行完成项目
     *
     * @param info 项目信息
     * @return 项目信息集合
     */
    public List<TItemInfo> selectFinishedItemInfoList(TItemInfo info);

    /**
     * 专业审核
     */
    public void updateMajor(@Param("id") Long id, @Param("sort") Long majorSort, @Param("fileIds") String fileIds);

    /**
     * 综合评审
     */
    public void updateCompo(@Param("id") Long id, @Param("sort") String compoSort, @Param("fileIds") String fileIds);

    /**
     * 入库评审
     */
    public void updateStore(@Param("id") Long id, @Param("budget") BigDecimal storeBudget, @Param("opinion") String storeOpinion, @Param("fileIds") String fileIds);

    /**
     * 执行计划排序
     */
    public void updatePlan(@Param("id") Long id, @Param("budget") BigDecimal storeBudget, @Param("sort") Long planSort, @Param("fileIds") String fileIds);
}
