package com.ruoyi.tiem.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.MilestonPlanMapper;
import com.ruoyi.tiem.domain.MilestonPlan;
import com.ruoyi.tiem.service.IMilestonPlanService;

/**
 * 里程碑计划信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
public class MilestonPlanServiceImpl implements IMilestonPlanService 
{
    @Autowired
    private MilestonPlanMapper milestonPlanMapper;

    /**
     * 查询里程碑计划信息
     * 
     * @param id 里程碑计划信息主键
     * @return 里程碑计划信息
     */
    @Override
    public MilestonPlan selectMilestonPlanById(Long id)
    {
        return milestonPlanMapper.selectMilestonPlanById(id);
    }

    /**
     * 查询里程碑计划信息列表
     * 
     * @param milestonPlan 里程碑计划信息
     * @return 里程碑计划信息
     */
    @Override
    public List<MilestonPlan> selectMilestonPlanList(MilestonPlan milestonPlan)
    {
        return milestonPlanMapper.selectMilestonPlanList(milestonPlan);
    }

    /**
     * 新增里程碑计划信息
     * 
     * @param milestonPlan 里程碑计划信息
     * @return 结果
     */
    @Override
    public int insertMilestonPlan(MilestonPlan milestonPlan)
    {
        return milestonPlanMapper.insertMilestonPlan(milestonPlan);
    }

    /**
     * 修改里程碑计划信息
     * 
     * @param milestonPlan 里程碑计划信息
     * @return 结果
     */
    @Override
    public int updateMilestonPlan(MilestonPlan milestonPlan)
    {
        return milestonPlanMapper.updateMilestonPlan(milestonPlan);
    }

    /**
     * 批量删除里程碑计划信息
     * 
     * @param ids 需要删除的里程碑计划信息主键
     * @return 结果
     */
    @Override
    public int deleteMilestonPlanByIds(Long[] ids)
    {
        return milestonPlanMapper.deleteMilestonPlanByIds(ids);
    }

    /**
     * 删除里程碑计划信息信息
     * 
     * @param id 里程碑计划信息主键
     * @return 结果
     */
    @Override
    public int deleteMilestonPlanById(Long id)
    {
        return milestonPlanMapper.deleteMilestonPlanById(id);
    }
}
