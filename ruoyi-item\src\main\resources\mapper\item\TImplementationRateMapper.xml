<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TImplementationRateMapper">

    <resultMap type="TImplementationRate" id="TImplementationRateResult">
        <result property="id"    column="id"    />
        <result property="category"    column="category"    />
        <result property="year"    column="year"    />
        <result property="month"    column="month"    />
        <result property="targetRate"    column="targetRate"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectTImplementationRateVo">
        select id, category, year, month, targetRate, status from t_implementation_rate
    </sql>

    <select id="selectTImplementationRateList" parameterType="TImplementationRate" resultMap="TImplementationRateResult">
        <include refid="selectTImplementationRateVo"/>
        <where>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="month != null  and month != ''"> and month = #{month}</if>
            <if test="targetRate != null  and targetRate != ''"> and targetRate = #{targetRate}</if>
            and status = 1
        </where>
    </select>

    <select id="selectTImplementationRateListByYear" parameterType="String" resultMap="TImplementationRateResult">
        <include refid="selectTImplementationRateVo"/>
        where year = #{year} and status = 1
    </select>

    <select id="selectTImplementationRateById" parameterType="Long" resultMap="TImplementationRateResult">
        <include refid="selectTImplementationRateVo"/>
        where id = #{id}
    </select>

    <insert id="insertTImplementationRate" parameterType="TImplementationRate" useGeneratedKeys="true" keyProperty="id">
        insert into t_implementation_rate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="category != null">category,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="targetRate != null">targetRate,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="category != null">#{category},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="targetRate != null">#{targetRate},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateTImplementationRate" parameterType="TImplementationRate">
        update t_implementation_rate
        <trim prefix="SET" suffixOverrides=",">
            <if test="category != null">category = #{category},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="targetRate != null">targetRate = #{targetRate},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>



    <update id="updateTImplementationRateTarget" parameterType="TImplementationRate">
        update t_implementation_rate
        <trim prefix="SET" suffixOverrides=",">
            <if test="targetRate != null">targetRate = #{targetRate},</if>
        </trim>
        where category = #{category} and year = #{year} and month = #{month}
    </update>

    <update id="deleteTImplementationRateById" parameterType="Long">
        update t_implementation_rate set status = 9
        where id = #{id}
    </update>

    <update id="deleteTImplementationRateByIds" parameterType="String">
        update t_implementation_rate set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
