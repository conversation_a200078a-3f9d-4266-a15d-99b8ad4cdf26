<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.PerformanceMapper">

    <resultMap type="Performance" id="PerformanceResult">
        <result property="id"    column="id"    />
        <result property="firstIndex"    column="first_index"    />
        <result property="secondIndex"    column="second_index"    />
        <result property="threeIndex"    column="three_index"    />
        <result property="symbol"    column="symbol"    />
        <result property="indexValue"    column="index_value"    />
        <result property="indexUnit"    column="index_unit"    />
        <result property="completionValue"    column="completion_value"    />
        <result property="scoreValue"    column="score_value"    />
        <result property="score"    column="score"    />
        <result property="reason"    column="reason"    />
        <result property="status"    column="status"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCategory"    column="item_category"    />
        <result property="executeYear"    column="execute_year"    />
    </resultMap>

    <sql id="selectPerformanceVo">
        select id, first_index, second_index, three_index, symbol, index_value, index_unit, completion_value, score_value, score, reason, status, item_id, item_category, execute_year  from t_performance
    </sql>

    <select id="selectPerformanceList" parameterType="Performance" resultMap="PerformanceResult">
        <include refid="selectPerformanceVo"/>
        where status = 1
            <if test="firstIndex != null  and firstIndex != ''"> and first_index = #{firstIndex}</if>
            <if test="secondIndex != null  and secondIndex != ''"> and second_index = #{secondIndex}</if>
            <if test="symbol != null  and symbol != ''"> and symbol = #{symbol}</if>
            <if test="threeIndex != null  and threeIndex != ''"> and three_index = #{threeIndex}</if>
            <if test="indexValue != null "> and index_value = #{indexValue}</if>
            <if test="indexUnit != null "> and index_unit = #{indexUnit}</if>
            <if test="completionValue != null "> and completion_value = #{completionValue}</if>
            <if test="scoreValue != null "> and score_value = #{scoreValue}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="reason != null "> and reason = #{reason}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
    </select>

    <select id="selectPerformanceById" parameterType="Long" resultMap="PerformanceResult">
        <include refid="selectPerformanceVo"/>
        where id = #{id}
    </select>

    <select id="selectPerformanceByCategory" parameterType="String" resultMap="PerformanceResult">
        <include refid="selectPerformanceVo"/>
        where status = 1 and item_category = #{category} and execute_year = #{year}
    </select>

    <select id="selectItemPerformanceByCategory" parameterType="String" resultMap="PerformanceResult">
        <include refid="selectPerformanceVo"/>
        where item_id in (
            select id from t_item_info where status = 1 and item_category = #{category} and execute_year = #{year}
            )
        group by first_index, second_index, three_index
    </select>

    <insert id="insertPerformance" parameterType="Performance" useGeneratedKeys="true" keyProperty="id">
        insert into t_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="firstIndex != null">first_index,</if>
            <if test="secondIndex != null">second_index,</if>
            <if test="threeIndex != null">three_index,</if>
            <if test="symbol != null">symbol,</if>
            <if test="indexValue != null">index_value,</if>
            <if test="indexUnit != null">index_unit,</if>
            <if test="completionValue != null">completion_value,</if>
            <if test="scoreValue != null">score_value,</if>
            <if test="score != null">score,</if>
            <if test="reason != null">reason,</if>
            <if test="status != null">status,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCategory != null">item_category,</if>
            <if test="executeYear != null">execute_year,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="firstIndex != null">#{firstIndex},</if>
            <if test="secondIndex != null">#{secondIndex},</if>
            <if test="threeIndex != null">#{threeIndex},</if>
            <if test="symbol != null">#{symbol},</if>
            <if test="indexValue != null">#{indexValue},</if>
            <if test="indexUnit != null">#{indexUnit},</if>
            <if test="completionValue != null">#{completionValue},</if>
            <if test="scoreValue != null">#{scoreValue},</if>
            <if test="score != null">#{score},</if>
            <if test="reason != null">#{reason},</if>
            <if test="status != null">#{status},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCategory != null">#{itemCategory},</if>
            <if test="executeYear != null">#{executeYear},</if>
         </trim>
    </insert>

    <update id="updatePerformance" parameterType="Performance">
        update t_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="firstIndex != null">first_index = #{firstIndex},</if>
            <if test="secondIndex != null">second_index = #{secondIndex},</if>
            <if test="threeIndex != null">three_index = #{threeIndex},</if>
            <if test="symbol != null">symbol = #{symbol},</if>
            <if test="indexValue != null">index_value = #{indexValue},</if>
            <if test="indexUnit != null">index_unit = #{indexUnit},</if>
            <if test="completionValue != null">completion_value = #{completionValue},</if>
            <if test="scoreValue != null">score_value = #{scoreValue},</if>
            <if test="score != null">score = #{score},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCategory != null">item_category = #{itemCategory},</if>
            <if test="executeYear != null">execute_year = #{executeYear},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deletePerformanceById" parameterType="Long">
        update t_performance set status = 9
        where id = #{id}
    </update>

    <update id="deletePerformanceByIds" parameterType="String">
        update t_performance set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
