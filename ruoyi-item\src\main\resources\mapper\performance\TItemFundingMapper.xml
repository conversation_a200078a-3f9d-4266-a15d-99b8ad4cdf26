<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemFundingMapper">

    <resultMap type="TItemFunding" id="TItemFundingResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="budgetStartOfYear"    column="budget_start_of_year"    />
        <result property="budgetAllOfYear"    column="budget_all_of_year"    />
        <result property="executionAmount"    column="execution_amount"    />
        <result property="scoreValue"    column="score_value"    />
        <result property="score"    column="score"    />
        <result property="implementionRate"    column="implemention_rate"    />
        <result property="status"    column="status"    />
        <result property="itemCategory"    column="item_category"    />
        <result property="executeYear"    column="execute_year"    />
    </resultMap>

    <sql id="selectTItemFundingVo">
        select id, type, budget_start_of_year, budget_all_of_year, execution_amount, score_value, score, implemention_rate, status, item_category, execute_year from t_item_funding
    </sql>

    <select id="selectTItemFundingList" parameterType="TItemFunding" resultMap="TItemFundingResult">
        <include refid="selectTItemFundingVo"/>
        <where>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="budgetStartOfYear != null "> and budget_start_of_year = #{budgetStartOfYear}</if>
            <if test="budgetAllOfYear != null "> and budget_all_of_year = #{budgetAllOfYear}</if>
            <if test="executionAmount != null "> and execution_amount = #{executionAmount}</if>
            <if test="scoreValue != null "> and score_value = #{scoreValue}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="implementionRate != null  and implementionRate != ''"> and implemention_rate = #{implementionRate}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="itemCategory != null  and itemCategory != ''"> and item_category = #{itemCategory}</if>
            <if test="executeYear != null  and executeYear != ''"> and execute_year = #{executeYear}</if>
             and status = 1
        </where>
    </select>

    <select id="selectTItemFundingById" parameterType="Long" resultMap="TItemFundingResult">
        <include refid="selectTItemFundingVo"/>
        where id = #{id} and status = 1
    </select>

    <insert id="insertTItemFunding" parameterType="TItemFunding">
        insert into t_item_funding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="budgetStartOfYear != null">budget_start_of_year,</if>
            <if test="budgetAllOfYear != null">budget_all_of_year,</if>
            <if test="executionAmount != null">execution_amount,</if>
            <if test="scoreValue != null">score_value,</if>
            <if test="score != null">score,</if>
            <if test="implementionRate != null">implemention_rate,</if>
            <if test="status != null">status,</if>
            <if test="itemCategory != null">item_category,</if>
            <if test="executeYear != null">execute_year,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="budgetStartOfYear != null">#{budgetStartOfYear},</if>
            <if test="budgetAllOfYear != null">#{budgetAllOfYear},</if>
            <if test="executionAmount != null">#{executionAmount},</if>
            <if test="scoreValue != null">#{scoreValue},</if>
            <if test="score != null">#{score},</if>
            <if test="implementionRate != null">#{implementionRate},</if>
            <if test="status != null">#{status},</if>
            <if test="itemCategory != null">#{itemCategory},</if>
            <if test="executeYear != null">#{executeYear},</if>
         </trim>
    </insert>

    <update id="updateTItemFunding" parameterType="TItemFunding">
        update t_item_funding
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="budgetStartOfYear != null">budget_start_of_year = #{budgetStartOfYear},</if>
            <if test="budgetAllOfYear != null">budget_all_of_year = #{budgetAllOfYear},</if>
            <if test="executionAmount != null">execution_amount = #{executionAmount},</if>
            <if test="scoreValue != null">score_value = #{scoreValue},</if>
            <if test="score != null">score = #{score},</if>
            <if test="implementionRate != null">implemention_rate = #{implementionRate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="itemCategory != null">item_category = #{itemCategory},</if>
            <if test="executeYear != null">execute_year = #{executeYear},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemFundingById" parameterType="Long">
        update t_item_funding set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemFundingByIds" parameterType="String">
        update t_item_funding set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
