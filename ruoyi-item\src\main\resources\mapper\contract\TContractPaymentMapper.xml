<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TContractPaymentMapper">

    <resultMap type="TContractPayment" id="TContractPaymentResult">
        <result property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="billingAmount" column="billing_amount"/>
        <result property="voucher" column="voucher"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTContractPaymentVo">
        select id, item_id, contract_id, payment_amount, billing_amount, voucher, year, month, status, remark
        from t_contract_payment
    </sql>

    <select id="selectTContractPaymentList" parameterType="TContractPayment" resultMap="TContractPaymentResult">
        <include refid="selectTContractPaymentVo"/>
        <where>
            <if test="itemId != null  and itemId != ''">and item_id = #{itemId}</if>
            <if test="contractId != null  and contractId != ''">and contract_id = #{contractId}</if>
            <if test="paymentAmount != null ">and payment_amount = #{paymentAmount}</if>
            <if test="billingAmount != null ">and billing_amount = #{billingAmount}</if>
            <if test="voucher != null ">and voucher = #{voucher}</if>
            <if test="year != null  and year != ''">and year = #{year}</if>
            <if test="month != null  and month != ''">and month = #{month}</if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            and status = "1"
        </where>
    </select>

    <select id="selectTotalPaymentAmount" parameterType="TContractPayment" resultType="java.math.BigDecimal">
        select sum(payment_amount)
        from t_contract_payment
        where year = #{year}
          and month &lt;= #{month}
          and status = 1
    </select>

    <select id="selectTContractPaymentById" parameterType="Long" resultMap="TContractPaymentResult">
        <include refid="selectTContractPaymentVo"/>
        where id = #{id}
    </select>

    <select id="selectContractPaymentByYearAndcontractId" parameterType="map" resultMap="TContractPaymentResult">
        <include refid="selectTContractPaymentVo"/>
        where status = 1 and year = #{year} and contract_id = #{contractId} and month = #{month} and remark = #{remark}
    </select>

    <insert id="insertTContractPayment" parameterType="TContractPayment" useGeneratedKeys="true" keyProperty="id">
        insert into t_contract_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="billingAmount != null">billing_amount,</if>
            <if test="voucher != null">voucher,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="billingAmount != null">#{billingAmount},</if>
            <if test="voucher != null">#{voucher},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTContractPayment" parameterType="TContractPayment">
        update t_contract_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="billingAmount != null">billing_amount = #{billingAmount},</if>
            <if test="voucher != null">voucher = #{voucher},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTContractPaymentById" parameterType="Long">
        update t_contract_payment
        set status = 9
        where id = #{id}
    </update>

    <update id="deleteTContractPaymentByIds" parameterType="String">
        update t_contract_payment set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteTContractPaymentByItemIds" parameterType="String">
        update t_contract_payment set status = 9 where item_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="editTContractPayment" parameterType="TContractPayment">
        update t_contract_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="billingAmount != null">billing_amount = #{billingAmount},</if>
            <if test="voucher != null">voucher = #{voucher},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where year = #{year} and contract_id = #{contractId} and month = #{month}
    </update>

    <select id="selectTotalEveryMonthPayment" resultType="map">
        select
        COALESCE (sum(payment_amount), 0) as everyMonthPayment,
        COALESCE (sum(billing_amount), 0) as everyMonthBilling,
        month
        from t_contract_payment where status = 1
        <if test="year != null">and year = #{year}</if>
        group by month
    </select>
    <select id="selectItemEveryMonthPayment" resultType="map">
        select sum(payment_amount) as everyMonthPayment, sum(billing_amount) as everyMonthBilling, month
        from t_contract_payment where status = 1 and item_id = #{id}
        <!--if test="year != null">and year = #{year}</if-->
        group by month
    </select>
    <select id="selectContractEveryMonthPayment" resultType="map">
        select sum(payment_amount) as everyMonthPayment, sum(billing_amount) as everyMonthBilling, month
        from t_contract_payment where status = 1 and contract_id = #{contractId}
        <if test="year != null">and year = #{year}</if>
        group by month
    </select>
</mapper>
