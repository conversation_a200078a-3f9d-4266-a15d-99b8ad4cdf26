package com.ruoyi.framework.cas.util;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.framework.cas.bean.UserInfo;
import com.ruoyi.framework.cas.util.CasUtil;
import org.jasig.cas.client.validation.Assertion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述：使用cas对接封装的cas返回的用户信息的对象
 */
public class CasUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CasUtil.class);
    /**
     * cas client 默认的session key
     */
    public final static String CAS = "_const_cas_assertion_";

    /**
     * 封装UserInfo
     *
     * @param request
     * @return
     */
    public static UserInfo getUserInfoFromCas(HttpServletRequest request) {
        Object object = request.getSession().getAttribute(CAS);
        if (null == object) {
            return null;
        }
        Assertion assertion = (Assertion) object;
        return buildUserInfoByCas(assertion);
    }

    /**
     * 构建UserInfo
     *
     * @param assertion
     * @return
     */
    private static UserInfo buildUserInfoByCas(Assertion assertion) {
        if (null == assertion) {
            LOGGER.error("cas对接buildUserInfoByCas没有获取到用户");
            return null;
        }
        UserInfo userInfo = new UserInfo();
        String userName = assertion.getPrincipal().getName();
        LOGGER.info("cas对接登录用户buildUserInfoByCas：" + userName);
        userInfo.setUserAccount(userName);
        //获取属性值
        Map<String, Object> attributes = assertion.getPrincipal().getAttributes();
        Object name = attributes.get("cn");
        userInfo.setUserName(name == null ? userName : name.toString());

        userInfo.setAttributes(attributes);

        LOGGER.info("buildUserInfoByCas, UserInfo:{}", JSON.toJSONString(userInfo));

        return userInfo;
    }

    public static UserInfo getUserInfoByResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            LOGGER.error("CAS响应为空");
            return null;
        }

        try {
            // 解析XML响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(response.getBytes("UTF-8")));

            // 检查是否认证成功
            NodeList authSuccessList = document.getElementsByTagNameNS("http://www.yale.edu/tp/cas", "authenticationSuccess");
            if (authSuccessList.getLength() == 0) {
                LOGGER.error("CAS认证失败，响应中没有authenticationSuccess节点");
                return null;
            }

            Element authSuccess = (Element) authSuccessList.item(0);

            // 获取用户账号
            NodeList userList = authSuccess.getElementsByTagNameNS("http://www.yale.edu/tp/cas", "user");
            if (userList.getLength() == 0) {
                LOGGER.error("CAS响应中没有找到用户信息");
                return null;
            }
            String userAccount = userList.item(0).getTextContent();

            // 获取属性节点
            NodeList attributesList = authSuccess.getElementsByTagNameNS("http://www.yale.edu/tp/cas", "attributes");
            Map<String, Object> attributes = new HashMap<>();
            String userName = userAccount; // 默认使用账号作为用户名

            if (attributesList.getLength() > 0) {
                Element attributesElement = (Element) attributesList.item(0);
                NodeList childNodes = attributesElement.getChildNodes();

                for (int i = 0; i < childNodes.getLength(); i++) {
                    Node node = childNodes.item(i);
                    if (node.getNodeType() == Node.ELEMENT_NODE) {
                        Element element = (Element) node;
                        String localName = element.getLocalName();
                        String value = element.getTextContent().trim();

                        // 存储所有属性
                        attributes.put(localName, value);

                        // 优先使用userName，其次使用cn作为用户姓名
                        if ("userName".equals(localName) && !value.isEmpty()) {
                            userName = value;
                        } else if ("cn".equals(localName) && !value.isEmpty() && userName.equals(userAccount)) {
                            userName = value;
                        }
                    }
                }
            }

            // 构建UserInfo对象
            UserInfo userInfo = new UserInfo();
            userInfo.setUserAccount(userAccount);
            userInfo.setUserName(userName);
            userInfo.setAttributes(attributes);

            LOGGER.info("解析CAS响应成功, UserInfo:{}", JSON.toJSONString(userInfo));
            return userInfo;

        } catch (Exception e) {
            LOGGER.error("解析CAS响应失败", e);
            return null;
        }
    }
}
