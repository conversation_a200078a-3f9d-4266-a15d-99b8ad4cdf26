<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemBudgetMapper">

    <resultMap type="TItemBudget" id="TItemBudgetResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="type"    column="type"    />
        <result property="amount"    column="amount"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectTItemBudgetVo">
        select id, item_id, type, amount, status from t_item_budget
    </sql>

    <select id="selectTItemBudgetList" parameterType="TItemBudget" resultMap="TItemBudgetResult">
        <include refid="selectTItemBudgetVo"/>
        <where>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectTItemBudgetById" parameterType="Long" resultMap="TItemBudgetResult">
        <include refid="selectTItemBudgetVo"/>
        where id = #{id}
    </select>

    <insert id="insertTItemBudget" parameterType="TItemBudget" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_budget
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="type != null">type,</if>
            <if test="amount != null">amount,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="type != null">#{type},</if>
            <if test="amount != null">#{amount},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateTItemBudget" parameterType="TItemBudget">
        update t_item_budget
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemBudgetById" parameterType="Long">
        update t_item_budget set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemBudgetByIds" parameterType="String">
        update t_item_budget set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
