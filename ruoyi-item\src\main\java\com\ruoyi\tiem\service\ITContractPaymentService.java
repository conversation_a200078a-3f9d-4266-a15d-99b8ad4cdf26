package com.ruoyi.tiem.service;

import com.ruoyi.tiem.domain.TContractPayment;

import java.util.List;

/**
 * 合同金额支付Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface ITContractPaymentService 
{
    /**
     * 查询合同金额支付
     * 
     * @param id 合同金额支付主键
     * @return 合同金额支付
     */
    public TContractPayment selectTContractPaymentById(Long id);

    /**
     * 查询合同金额支付列表
     * 
     * @param tContractPayment 合同金额支付
     * @return 合同金额支付集合
     */
    public List<TContractPayment> selectTContractPaymentList(TContractPayment tContractPayment);

    /**
     * 新增合同金额支付
     * 
     * @param tContractPayment 合同金额支付
     * @return 结果
     */
    public int insertTContractPayment(TContractPayment tContractPayment);

    /**
     * 修改合同金额支付
     * 
     * @param tContractPayment 合同金额支付
     * @return 结果
     */
    public int updateTContractPayment(TContractPayment tContractPayment);

    /**
     * 批量删除合同金额支付
     * 
     * @param ids 需要删除的合同金额支付主键集合
     * @return 结果
     */
    public int deleteTContractPaymentByIds(Long[] ids);

    /**
     * 删除合同金额支付信息
     * 
     * @param id 合同金额支付主键
     * @return 结果
     */
    public int deleteTContractPaymentById(Long id);

    /**
     * 根据执行年份查询某个具体合同某个月月支付额
     *
     */
    TContractPayment selectContractPaymentByYearAndcontractId(Integer year, Long contractId, Integer month, String remark);

    /**
     * 根据month和year和itemCategory编辑支付额
     *
     */
    int editTContractPayment(TContractPayment contractPayment);
}
