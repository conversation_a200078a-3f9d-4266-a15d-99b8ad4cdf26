<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TDeadlineInfoMapper">

    <resultMap type="TDeadlineInfo" id="TDeadlineInfoResult">
        <result property="id"    column="id"    />
        <result property="category"    column="category"    />
        <result property="deadline"    column="deadline"    />
        <result property="year"    column="year"    />
    </resultMap>

    <sql id="selectTDeadlineInfoVo">
        select id, category, deadline, year from t_deadline_info
    </sql>

    <select id="selectTDeadlineInfoList" parameterType="TDeadlineInfo" resultMap="TDeadlineInfoResult">
        <include refid="selectTDeadlineInfoVo"/>
        <where>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="deadline != null "> and deadline = #{deadline}</if>
            <if test="year != null  and year != ''"> and year = #{year}</if>
        </where>
    </select>

    <select id="selectTDeadlineInfoById" parameterType="Long" resultMap="TDeadlineInfoResult">
        <include refid="selectTDeadlineInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertTDeadlineInfo" parameterType="TDeadlineInfo">
        insert into t_deadline_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="category != null">category,</if>
            <if test="deadline != null">deadline,</if>
            <if test="year != null">year,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="category != null">#{category},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="year != null">#{year},</if>
         </trim>
    </insert>

    <update id="updateTDeadlineInfo" parameterType="TDeadlineInfo">
        update t_deadline_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="category != null">category = #{category},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="year != null">year = #{year},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTDeadlineInfoById" parameterType="Long">
        update t_deadline_info set status = 9
        where id = #{id}
    </update>

    <update id="deleteTDeadlineInfoByIds" parameterType="String">
        delete from t_deadline_info where id in
        <foreach collection="array" item="deadlineId" open="(" separator="," close=")">
            #{deadlineId}
        </foreach>
    </update>
</mapper>
