package com.ruoyi.tiem.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目审核信息对象 t_item_audit
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
public class TItemAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    /** 审批节点 */
    @Excel(name = "审批节点")
    private Integer flow;

    /** 审核状态 1：通过 2：拒绝 */
    @Excel(name = "审核状态 1：通过 2：拒绝 3:待审核")
    private String auditStatus;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reason;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 审核部门名称 */
    @Excel(name = "审核部门名称")
    private String auditDeptName;

}
