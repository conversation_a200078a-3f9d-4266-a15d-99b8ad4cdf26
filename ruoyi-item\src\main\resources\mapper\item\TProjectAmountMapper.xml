<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TProjectAmountMapper">

    <resultMap type="TProjectAmount" id="TProjectAmountResult">
        <result property="id"    column="id"    />
        <result property="year"    column="year"    />
        <result property="amount"    column="amount"    />
        <result property="status"    column="status"    />
        <result property="itemCategory"    column="item_category"    />
    </resultMap>

    <sql id="selectTProjectAmountVo">
        select id, year, amount, status, item_category from t_project_amount
    </sql>

    <select id="selectTProjectAmountList" parameterType="TProjectAmount" resultMap="TProjectAmountResult">
        <include refid="selectTProjectAmountVo"/>
        <where>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectTProjectAmountById" parameterType="Long" resultMap="TProjectAmountResult">
        <include refid="selectTProjectAmountVo"/>
        where id = #{id}
    </select>

    <select id="selectAmountOfYear" parameterType="String">
        select amount from t_project_amount
        where status = 1 and year = #{year}
    </select>

    <select id="selectTProjectAmountByYearAndCategory" parameterType="map" resultMap="TProjectAmountResult">
        <include refid="selectTProjectAmountVo"/>
        where status = 1 and year = #{year} and item_category = #{itemCategory}
    </select>


    <insert id="insertTProjectAmount" parameterType="TProjectAmount" useGeneratedKeys="true" keyProperty="id">
        insert into t_project_amount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="year != null">year,</if>
            <if test="amount != null">amount,</if>
            <if test="status != null">status,</if>
            <if test="itemCategory != null">item_category,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="year != null">#{year},</if>
            <if test="amount != null">#{amount},</if>
            <if test="status != null">#{status},</if>
            <if test="itemCategory != null">#{itemCategory},</if>
         </trim>
    </insert>

    <update id="updateTProjectAmount" parameterType="TProjectAmount">
        update t_project_amount
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where year = #{year}
    </update>

    <update id="editTProjectAmount" parameterType="TProjectAmount">
        update t_project_amount
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where year = #{year} and item_category = #{itemCategory}
    </update>

    <update id="deleteTProjectAmountById" parameterType="Long">
        update t_project_amount set status = 9
        where id = #{id}
    </update>

    <update id="deleteTProjectAmountByIds" parameterType="String">
        update t_project_amount set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
