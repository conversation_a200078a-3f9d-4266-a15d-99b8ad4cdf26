<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TFoundingSourceMapper">

    <resultMap type="TFoundingSource" id="TFoundingSourceResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="applyFounding"    column="apply_founding"    />
        <result property="otherFounding"    column="other_founding"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectTFoundingSourceVo">
        select id, item_id, apply_founding, other_founding, status from t_founding_source
    </sql>

    <select id="selectTFoundingSourceList" parameterType="TFoundingSource" resultMap="TFoundingSourceResult">
        <include refid="selectTFoundingSourceVo"/>
        <where>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="applyFounding != null  and applyFounding != ''"> and apply_founding = #{applyFounding}</if>
            <if test="otherFounding != null  and otherFounding != ''"> and other_founding = #{otherFounding}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectTFoundingSourceById" parameterType="Long" resultMap="TFoundingSourceResult">
        <include refid="selectTFoundingSourceVo"/>
        where id = #{id}
    </select>

    <insert id="insertTFoundingSource" parameterType="TFoundingSource" useGeneratedKeys="true" keyProperty="id">
        insert into t_founding_source
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="applyFounding != null">apply_founding,</if>
            <if test="otherFounding != null and otherFounding != ''">other_founding,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="applyFounding != null">#{applyFounding},</if>
            <if test="otherFounding != null and otherFounding != ''">#{otherFounding},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateTFoundingSource" parameterType="TFoundingSource">
        update t_founding_source
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="applyFounding != null">apply_founding = #{applyFounding},</if>
            <if test="otherFounding != null and otherFounding != ''">other_founding = #{otherFounding},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTFoundingSourceById" parameterType="Long">
        update t_founding_source set status = 9
        where id = #{id}
    </update>

    <update id="deleteTFoundingSourceByIds" parameterType="String">
        update t_founding_source set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
