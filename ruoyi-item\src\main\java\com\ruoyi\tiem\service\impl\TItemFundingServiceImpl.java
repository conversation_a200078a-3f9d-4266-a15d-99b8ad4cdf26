package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TItemFunding;
import com.ruoyi.tiem.mapper.TItemFundingMapper;
import com.ruoyi.tiem.service.ITItemFundingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 绩效评价-项目资金Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class TItemFundingServiceImpl implements ITItemFundingService
{
    @Autowired
    private TItemFundingMapper tItemFundingMapper;

    /**
     * 查询绩效评价-项目资金
     * 
     * @param id 绩效评价-项目资金主键
     * @return 绩效评价-项目资金
     */
    @Override
    public TItemFunding selectTItemFundingById(Long id)
    {
        return tItemFundingMapper.selectTItemFundingById(id);
    }

    /**
     * 查询绩效评价-项目资金列表
     * 
     * @param tItemFunding 绩效评价-项目资金
     * @return 绩效评价-项目资金
     */
    @Override
    public List<TItemFunding> selectTItemFundingList(TItemFunding tItemFunding)
    {
        return tItemFundingMapper.selectTItemFundingList(tItemFunding);
    }

    /**
     * 新增绩效评价-项目资金
     * 
     * @param tItemFunding 绩效评价-项目资金
     * @return 结果
     */
    @Override
    public int insertTItemFunding(TItemFunding tItemFunding)
    {
        return tItemFundingMapper.insertTItemFunding(tItemFunding);
    }

    /**
     * 修改绩效评价-项目资金
     * 
     * @param tItemFunding 绩效评价-项目资金
     * @return 结果
     */
    @Override
    public int updateTItemFunding(TItemFunding tItemFunding)
    {
        return tItemFundingMapper.updateTItemFunding(tItemFunding);
    }

    /**
     * 批量删除绩效评价-项目资金
     * 
     * @param ids 需要删除的绩效评价-项目资金主键
     * @return 结果
     */
    @Override
    public int deleteTItemFundingByIds(Long[] ids)
    {
        return tItemFundingMapper.deleteTItemFundingByIds(ids);
    }

    /**
     * 删除绩效评价-项目资金信息
     * 
     * @param id 绩效评价-项目资金主键
     * @return 结果
     */
    @Override
    public int deleteTItemFundingById(Long id)
    {
        return tItemFundingMapper.deleteTItemFundingById(id);
    }
}
