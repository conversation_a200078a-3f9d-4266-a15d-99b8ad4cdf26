package com.ruoyi.tiem.mapper;

import com.ruoyi.tiem.domain.TFoundingSource;

import java.util.List;

/**
 * 项目资金来源Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface TFoundingSourceMapper 
{
    /**
     * 查询项目资金来源
     * 
     * @param id 项目资金来源主键
     * @return 项目资金来源
     */
    public TFoundingSource selectTFoundingSourceById(Long id);

    /**
     * 查询项目资金来源列表
     * 
     * @param tFoundingSource 项目资金来源
     * @return 项目资金来源集合
     */
    public List<TFoundingSource> selectTFoundingSourceList(TFoundingSource tFoundingSource);

    /**
     * 新增项目资金来源
     * 
     * @param tFoundingSource 项目资金来源
     * @return 结果
     */
    public int insertTFoundingSource(TFoundingSource tFoundingSource);

    /**
     * 修改项目资金来源
     * 
     * @param tFoundingSource 项目资金来源
     * @return 结果
     */
    public int updateTFoundingSource(TFoundingSource tFoundingSource);

    /**
     * 删除项目资金来源
     * 
     * @param id 项目资金来源主键
     * @return 结果
     */
    public int deleteTFoundingSourceById(Long id);

    /**
     * 批量删除项目资金来源
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTFoundingSourceByIds(Long[] ids);
}
