package com.ruoyi.tiem.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.RichTextUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.WordUtil;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.tiem.domain.*;
import com.ruoyi.tiem.mapper.*;
import com.ruoyi.tiem.service.ITItemAuditService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.service.ITItemInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 项目信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@RestController
@RequestMapping("/item/info")
public class TItemInfoController extends BaseController {
    @Autowired
    private ITItemInfoService tItemInfoService;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    @Autowired
    private TImplementationRateMapper implementationRateMapper;

    @Autowired
    private TContractPaymentMapper tContractPaymentMapper;

    @Autowired
    private TContractInfoMapper tContractInfoMapper;

    @Autowired
    private ContractPlanMapper contractPlanMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private TItemAuditMapper tItemAuditMapper;

    @Autowired
    private TItemBudgetMapper tItemBudgetMapper;

    @Autowired
    private TFoundingSourceMapper tFoundingSourceMapper;

    @Autowired
    private PerformanceMapper performanceMapper;

    @Autowired
    private TItemAuditDetailMapper tItemAuditDetailMapper;

    @Autowired
    private ITItemAuditService tItemAuditService;

    /**
     * 查询项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('item:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(TItemInfo tItemInfo) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();
        if (roles.contains("teacher") || roles.contains("dean")) {
            //各学院只能看到自己的需求
            tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
        } else if (roles.contains("focal") || roles.contains("focalLeader")) {
            tItemInfo.setFocalUnitId(dept.getDeptId());
        }
        if (tItemInfo.getIds() != null && !tItemInfo.getIds().equals("")) {
            List<String> ids = Arrays.asList(tItemInfo.getIds().split(","));
            Map<String, Object> params = tItemInfo.getParams();
            params.put("ids", ids);
            tItemInfo.setParams(params);
        }
        if (tItemInfo.getItemCategories() != null && !tItemInfo.getItemCategories().equals("")) {
            List<String> itemCategoryList = Arrays.asList(tItemInfo.getItemCategories().split(","));
            Map<String, Object> params = tItemInfo.getParams();
            params.put("itemCategories", itemCategoryList);
            tItemInfo.setParams(params);
        }
        if (tItemInfo.getFlows() != null && !tItemInfo.getFlows().equals("")) {
            List<String> flows = Arrays.asList(tItemInfo.getFlows().split(","));
            Map<String, Object> params = tItemInfo.getParams();
            params.put("flows", flows);
            tItemInfo.setParams(params);
        }
        startPage("id desc");
        List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);

        /**
         * 项目节点说明：
         * 0：待发起人重新提交
         * 1：老师发起项目申请
         * 2：学院领导审批
         * 3：归口单位经办人审批
         * 4：归口单位领导审批
         * 5：国资处经办人审批（孙老师）
         * 6：已入学校库
         * 7：已入工信部库
         * 8：V1合约规划填报
         * 9：归口单位审核V1合约规划
         * 10：国资处经办人审批
         * 11：已立项执行
         * 12：V2合约规划填报
         * 13：归口单位审核V2合约规划
         * 14：国资处经办人审批
         * 100：已到期出库
         */

        for (TItemInfo item : list) {
            Integer flow = item.getFlow();
            switch (flow) {
                case 0:
                    if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                        // item.setIsAudit(1);
                        item.setIsEdit(1);
                    }
                    break;
                case 1:
                    if (roles.contains("dean")) {
                        item.setIsAudit(1);
                    } else if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                        item.setIsEdit(1);
                    }
                    break;
                case 2:
                    if (roles.contains("focal")) {
                        item.setIsAudit(1);
                    }
                    break;
                case 3:
                    if (roles.contains("focalLeader")) {
                        item.setIsAudit(1);
                    }
                    break;
                case 4:
                case 14:
                    if (roles.contains("projectManager")) {
                        item.setIsAudit(1);
                    }
                    break;
                case 5:
                case 6:
                case 10:
                case 11:
                case 13:
                    if (roles.contains("projectManager")) {
                        item.setIsEdit(2);
                    }
                    break;
                case 7:
                    if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                        item.setIsEdit(3);
                    }
                    if (roles.contains("projectManager")) {
                        item.setIsEdit(2);
                    }
                    break;
                case 8:
                    if (roles.contains("focal") && item.getContractPlanList() != null && item.getContractPlanList().size() > 0) {
                        item.setIsAudit(1);
                    }
                    if (roles.contains("projectManager")) {
                        item.setIsEdit(2);
                    }
                    break;
                case 9:
                    if (roles.contains("projectManager")) {
                        item.setIsEdit(2);
                        item.setIsAudit(1);
                    }
                    break;
                default:
                    break;
            }

        }
        return getDataTable(list);
    }

    /**
     * 查询项目信息列表Two
     */
    @PreAuthorize("@ss.hasPermi('item:info:list')")
    @GetMapping("/listTwo")
    public TableDataInfo listTwo(TItemInfo tItemInfo) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();
        if (roles.contains("teacher") || roles.contains("dean")) {
            //各学院只能看到自己的数据
            tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
        } else if (roles.contains("focal") || roles.contains("focalLeader")) {
            //各归口单位只能看到自己单位下的数据
            tItemInfo.setFocalUnitId(dept.getDeptId());
        }

        // 解析可多选的查询条件：ids、项目类型、项目状态（流程）
        if (tItemInfo.getIds() != null && !tItemInfo.getIds().equals("")) {
            List<String> ids = Arrays.asList(tItemInfo.getIds().split(","));
            Map<String, Object> params = tItemInfo.getParams();
            params.put("ids", ids);
            tItemInfo.setParams(params);
        }
        if (tItemInfo.getItemCategories() != null && !tItemInfo.getItemCategories().equals("")) {
            List<String> itemCategoryList = Arrays.asList(tItemInfo.getItemCategories().split(","));
            Map<String, Object> params = tItemInfo.getParams();
            params.put("itemCategories", itemCategoryList);
            tItemInfo.setParams(params);
        }
        if (tItemInfo.getFlows() != null && !tItemInfo.getFlows().equals("")) {
            List<String> flows = Arrays.asList(tItemInfo.getFlows().split(","));
            Map<String, Object> params = tItemInfo.getParams();
            params.put("flows", flows);
            tItemInfo.setParams(params);
        }

        // 如果是待审核筛选，预先添加角色对应的flow条件，先将大致符合条件的数据过滤一遍
        // 作用：先用sql过滤一遍大致符合条件的数据，提高性能，方便后续的精准查询，增加查询效率
        Boolean isPendingAudit = tItemInfo.getPendingAudit() != null && tItemInfo.getPendingAudit();
        if (isPendingAudit) {
            // 根据角色预先筛选可能需要审核的项目
            List<Integer> possibleFlows = new ArrayList<>();

            // 根据用户角色和业务规则确定可能待审核的flow状态
            if (roles.contains("teacher")) {
                possibleFlows.add(0);
                possibleFlows.add(1); // 可能需要老师审批的项目节点
            }
            if (roles.contains("dean")) {
                possibleFlows.add(1); // 院长审核的节点
            }
            if (roles.contains("focalLeader")) {
                possibleFlows.add(2); // 归口单位领导审核的节点
            }
            if (roles.contains("focal")) {
                possibleFlows.add(3); // 归口单位经办人审核的节点
            }
            if (roles.contains("projectManager")) {
                possibleFlows.add(4); // 国资处经办人可能审核的节点
                possibleFlows.add(5); // 国资处经办人可能审核的其他节点
                possibleFlows.add(6); // 国资处经办人可能审核的其他节点
            }

            // 将可能的flow状态添加到查询参数中
            if (!possibleFlows.isEmpty()) {
                Map<String, Object> params = tItemInfo.getParams() != null ? tItemInfo.getParams() : new HashMap<>();
                params.put("possibleFlows", possibleFlows);
                tItemInfo.setParams(params);
            }

            // 对于多单位联合申报项目的特殊处理
            // 如果是teacher角色，还需要查询可能关联的项目
            if (roles.contains("teacher")) {
                Map<String, Object> params = tItemInfo.getParams() != null ? tItemInfo.getParams() : new HashMap<>();
                params.put("checkMultiDeptTeacher", true);
                params.put("currentDeptId", dept.getDeptId());
                tItemInfo.setParams(params);
            }
            // 如果是dean角色，还需要查询可能关联的项目
            if (roles.contains("dean")) {
                Map<String, Object> params = tItemInfo.getParams() != null ? tItemInfo.getParams() : new HashMap<>();
                params.put("checkMultiDeptDean", true);
                params.put("currentDeptId", dept.getDeptId());
                tItemInfo.setParams(params);
            }

            // 添加待审核筛选条件
            Map<String, Object> params = tItemInfo.getParams() != null ? tItemInfo.getParams() : new HashMap<>();
            params.put("pendingAudit", true);
            params.put("currentUserId", user.getUserId());
            params.put("hasTeacherRole", roles.contains("teacher"));
            tItemInfo.setParams(params);
        }

        // 正常使用分页查询
        startPage("id desc");
        List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);

        /**
         * 项目节点说明：
         * 0：待发起人重新提交
         * 1：老师发起项目申请
         * 1.5：其他项目关联学院老师审批（多学院）
         * 2：所有关联学院院长、副院长审批
         * 3. 归口单位领导审批
         * 4：归口单位经办人专业审核
         * 5：国资处经办人综合评审（孙老师）
         * 6：国资处经办人入库评审
         * 7：国资处经办人执行计划排序
         * 8：国资处经办人添加项目执行（已立项执行）
         * 9：学院老师填报合约规划
         * 10：归口单位经办人审批
         * 11：国资处经办人审批
         * 100：已到期出库
         * 101: 未通过
         */

        /**
         * 项目节点说明：
         * 0：待发起人重新提交
         * 1：老师发起项目申请
         * 2：关联学院院长或副院长已审批
         * 3. 归口单位领导已审批
         * 4：归口单位经办人专业已审核
         * 5：国资处经办人已综合评审（孙老师）
         * 6：国资处经办人已入库评审
         * 7：国资处经办人已执行计划排序
         * 8：国资处经办人添加项目执行（已立项执行）
         * 9：学院老师填报合约规划
         * 10：归口单位经办人审批
         * 11：国资处经办人审批
         * 100：已到期出库
         * 101: 未通过
         */
        // 处理审核权限标志
        String currentDeptName = dept.getDeptName();
        for (TItemInfo item : list) {
            Integer flow = item.getFlow();
            String[] isMulti = item.getApplyUnit() != null ? item.getApplyUnit().split(",") : new String[0];
            if (isMulti.length > 1) { // 如果是多个申报部门，即联合申报
                switch (flow) {
                    case 0:
                        if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                            item.setIsEdit(1);
                        }
                        break;
                    case 1:
                        // 查询t_item_audit_detail表中item_id == item.getId且flow为1的数据项
                        List<TItemAuditDetail> auditDetails = tItemAuditDetailMapper.selectByItemIdAndFlow(item.getId(), 1);

                        // 获取与当前用户部门名称相同的 auditDetails 数据
                        Optional<TItemAuditDetail> matchingDetail = auditDetails.stream().filter(detail -> currentDeptName.equals(detail.getAuditDeptName())).findFirst();
                        if (roles.contains("teacher") && matchingDetail.isPresent()) {
                            // 获得存在匹配的数据
                            TItemAuditDetail matchedDetail = matchingDetail.get();
                            if (matchedDetail.getAuditStatus().equals("3")) {
                                item.setIsAudit(1);
                            }
                        } else if (roles.contains("dean") && auditDetails.stream().allMatch(detail -> "1".equals(detail.getAuditStatus()))) {
                            // 查询t_item_audit_detail表中item_id == item.getId且flow为2的数据项
                            List<TItemAuditDetail> deanAuditDetails = tItemAuditDetailMapper.selectByItemIdAndFlow(item.getId(), 2);
                            // 在这里找到 deanAuditDetails 中 auditStatus 不为 "1"，且 auditDeptName 和 user 的部门名称相同的数据项
                            Optional<TItemAuditDetail> deanMatchingDetail = deanAuditDetails.stream().filter(detail -> !"1".equals(detail.getAuditStatus()) && currentDeptName.equals(detail.getAuditDeptName())).findFirst();
                            if (deanMatchingDetail.isPresent()) {
                                item.setIsAudit(1);
                            }
                        } else if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                            item.setIsEdit(1);
                        }
                        break;
                    case 2:
                        if (roles.contains("focalLeader")) {
                            item.setIsAudit(1);
                        }
                        break;
                    default:
                        break;
                }
            } else {                // 单部门申报
                switch (flow) {
                    case 0:
                        if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                            // item.setIsAudit(1);
                            item.setIsEdit(1);
                        }
                        break;
                    case 1:
                        if (roles.contains("dean")) {
                            item.setIsAudit(1);
                        } else if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                            item.setIsEdit(1);
                        }
                        break;
                    case 2:
                        if (roles.contains("focalLeader")) {
                            item.setIsAudit(1);
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        // 如果是待审核筛选，进一步过滤只保留需要当前用户审核的项目
//        if (isPendingAudit) {
//            Long currentUserId = user.getUserId();
//            List<String> userRoles = roles;
//            list = list.stream()
//                .filter(item ->
//                    // 原有条件：需要当前用户审核的项目，或者flow大于等于3的项目
//                    (item.getIsAudit() != null && item.getIsAudit() == 1) || item.getFlow() >= 3 ||
//                    // 新增条件：待发起人重新提交的项目且当前用户是创建者且拥有teacher角色
//                    (item.getFlow() == 0 && currentUserId.equals(item.getCreateById()) && userRoles.contains("teacher"))
//                )
//                .collect(Collectors.toList());
//        }

        return getDataTable(list);
    }

    /**
     * 导出项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('item:info:export')")
    @Log(title = "项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemInfo tItemInfo) {
        List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);

        // 处理HTML标签
        for (TItemInfo item : list) {
            // 处理六个富文本字段
            if (item.getNecessity() != null) {
                item.setNecessity(removeHtmlTags(item.getNecessity()));
            }
            if (item.getFeasibility() != null) {
                item.setFeasibility(removeHtmlTags(item.getFeasibility()));
            }
            if (item.getConditions() != null) {
                item.setConditions(removeHtmlTags(item.getConditions()));
            }
            if (item.getContent() != null) {
                item.setContent(removeHtmlTags(item.getContent()));
            }
            if (item.getArrange() != null) {
                item.setArrange(removeHtmlTags(item.getArrange()));
            }
            if (item.getRisk() != null) {
                item.setRisk(removeHtmlTags(item.getRisk()));
            }
            if (item.getBenefit() != null) {
                item.setBenefit(removeHtmlTags(item.getBenefit()));
            }
        }

        ExcelUtil<TItemInfo> util = new ExcelUtil<TItemInfo>(TItemInfo.class);
        util.exportExcel(response, list, "项目信息数据");
    }

    /**
     * 去除HTML标签
     */
    private String removeHtmlTags(String html) {
        if (html == null) {
            return null;
        }
        return html.replaceAll("<[^>]*>", "").replaceAll("&nbsp;", " ");
    }

    /**
     * 导出项目信息列表
     */
    @Log(title = "项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/save")
    public AjaxResult save(Long itemId) {
        TItemInfo itemInfo = itemInfoMapper.selectTItemInfoById(itemId);
        // 表格数据集合
        List<List<String[]>> list = new ArrayList<>();

        //模板文件地址
        String inputUrl = RuoYiConfig.getProfile() + "/upload/templates/项目申报书.docx";
        //新生产的模板文件
        String outputUrl = RuoYiConfig.getProfile() + "/upload/项目-" + itemInfo.getId() + ".docx";

        try {
            // 创建临时模板文件，避免修改原始模板
            String tempInputUrl = RuoYiConfig.getProfile() + "/upload/temp/temp_项目申报书_" + System.currentTimeMillis() + ".docx";
            java.nio.file.Path tempDirPath = java.nio.file.Paths.get(RuoYiConfig.getProfile() + "/upload/temp");
            // 确保临时目录存在
            if (!java.nio.file.Files.exists(tempDirPath)) {
                java.nio.file.Files.createDirectories(tempDirPath);
            }
            // 复制原始模板到临时文件
            java.nio.file.Files.copy(java.nio.file.Paths.get(inputUrl), java.nio.file.Paths.get(tempInputUrl), java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            /*---------- 获取项目基本信息 ------*/
            Map<String, String> itemMap = new HashMap<>();
            // 项目编号
            itemMap.put("itemNumber", itemInfo.getItemNumber());
            // 项目名称
            itemMap.put("itemName", itemInfo.getItemName());
            // 项目负责人
            itemMap.put("leader", itemInfo.getLeader());
            // 项目联系人
            itemMap.put("contact", itemInfo.getContact());
            // 联系电话
            itemMap.put("createPhone", itemInfo.getCreatePhone());
            // 电子信箱
            itemMap.put("email", itemInfo.getEmail());
            // 申报单位
            itemMap.put("applyUnit", itemInfo.getApplyUnit() == null ? "" : itemInfo.getApplyUnit());
            // 申报时间
            String[] date = new SimpleDateFormat("yyyy-MM-dd").format(itemInfo.getCreateTime()).split("-");
            String applyTime = date[0] + "年" + date[1] + "月" + date[2] + "日";
            itemMap.put("applyTime", applyTime);
            // 项目地点
            itemMap.put("place", itemInfo.getPlace());
            // 概算金额
            itemMap.put("applyAmount", itemInfo.getApplyAmount() + "元");
            // 概算金额
            itemMap.put("applyYear", date[0]);
            // 项目类型
            itemMap.put("itemCategory", itemInfo.getItemCategory());
            // 项目类别
            itemMap.put("projectCategory", itemInfo.getProjectCategory());
            // 项目实施必要性分析
            itemMap.put("necessity", itemInfo.getNecessity() == null ? "" : itemInfo.getNecessity());
            // 项目实施可行性分析
            itemMap.put("feasibility", itemInfo.getFeasibility() == null ? "" : itemInfo.getFeasibility());
            // 项目实施条件
            itemMap.put("conditions", itemInfo.getConditions() == null ? "" : itemInfo.getConditions());
            // 项目实施主要内容及相关预算
            itemMap.put("content", itemInfo.getContent() == null ? "" : itemInfo.getContent());
            // 项目进度与计划安排
            itemMap.put("arrange", itemInfo.getArrange() == null ? "" : itemInfo.getArrange());
            // 项目风险与不确定性分析
            itemMap.put("risk", itemInfo.getRisk() == null ? "" : itemInfo.getRisk());
            // 预期经济社会效益
            itemMap.put("benefit", itemInfo.getBenefit() == null ? "" : itemInfo.getBenefit());
            /*---------- 获取项目基本信息 ------*/

            /*---------- 获取项目支出预算明细表 ------*/
            TFoundingSource tFoundingSource = new TFoundingSource();
            tFoundingSource.setItemId(itemId);
            List<TFoundingSource> tFoundingSourceList = tFoundingSourceMapper.selectTFoundingSourceList(tFoundingSource);

            TItemBudget tItemBudget = new TItemBudget();
            tItemBudget.setItemId(itemId);
            List<TItemBudget> tItemBudgetList = tItemBudgetMapper.selectTItemBudgetList(tItemBudget);

            // 合计项目资金来源(单位：元)
            BigDecimal totalSource = new BigDecimal("0.00");
            // 申请拨款(单位：元)
            BigDecimal applySource = new BigDecimal(tFoundingSourceList.get(0).getApplyFounding());
            // 其他资金(单位：元)
            BigDecimal otherSource = new BigDecimal(tFoundingSourceList.get(0).getOtherFounding());
            totalSource = applySource.add(otherSource);
            // 合计项目资金来源
            itemMap.put("totalSource", totalSource.toString());
            // 申请拨款
            itemMap.put("applySource", applySource.toString());
            // 其他资金
            itemMap.put("otherSource", otherSource.toString());
            // 明细支出预算
            List<String[]> tableList = new ArrayList<String[]>();
            BigDecimal totalBudget = new BigDecimal("0.00");
            for (TItemBudget itemBudget : tItemBudgetList) {
                tableList.add(new String[]{itemBudget.getType(), itemBudget.getAmount().toString()});
                totalBudget = totalBudget.add(itemBudget.getAmount());
            }
            list.add(tableList);
            // 项目支出明细预算合计(单位：元)
            itemMap.put("totalBudget", totalBudget.toString());
            // 测算依据及说明
            itemMap.put("explains", itemInfo.getExplains() == null ? "" : itemInfo.getExplains());
            /*---------- 获取项目支出预算明细表 ------*/

            /*---------- 获取项目预期绩效目标申报表 ------*/
            // 项目期
            itemMap.put("cycles", itemInfo.getCycles() == null ? "" : itemInfo.getCycles());
            // 年度目标
            itemMap.put("goal", itemInfo.getGoal() == null ? "" : itemInfo.getGoal());
            //*/ 获取绩效一级指标类别字典
            SysDictData dictData = new SysDictData();
            dictData.setDictType("first_order");
            List<SysDictData> sysDictDataList = dictDataService.selectDictDataList(dictData);
            Map<String, String> indexMap = new HashMap<>();
            for (SysDictData sysDictData : sysDictDataList) {
                indexMap.put(sysDictData.getDictValue(), sysDictData.getDictLabel());
            }
            //*/
            // 绩效指标
            List<String[]> tableList1 = new ArrayList<String[]>();
            Performance performance = new Performance();
            performance.setItemId(itemId);
            List<Performance> performanceList = performanceMapper.selectPerformanceList(performance);
            // 定义符号字典映射
            Map<String, String> symbolMap = new HashMap<>();
            symbolMap.put(">=", ">=");
            symbolMap.put("=", "=");
            symbolMap.put("<=", "<=");
            symbolMap.put("其它", "其它");
            for (Performance performance1 : performanceList) {
                String symbolLabel = symbolMap.getOrDefault(performance1.getSymbol(), performance1.getSymbol());
                tableList1.add(new String[]{indexMap.get(performance1.getFirstIndex()), performance1.getSecondIndex(), performance1.getThreeIndex(), symbolLabel + performance1.getIndexValue() + performance1.getIndexUnit()});
            }
            list.add(tableList1);
            /*---------- 获取项目预期绩效目标申报表 ------*/
            List<Map<String, Object>> flowList = tItemAuditService.handleRecord(itemId);

            // 使用临时模板文件而不是原始模板，并添加审核信息表格
            boolean isSuccessful = WordUtil.changWordWithAudit(tempInputUrl, outputUrl, itemMap, list, flowList);

            // 处理完成后删除临时文件
            java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get(tempInputUrl));

            return isSuccessful ? AjaxResult.success() : AjaxResult.error();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("生成文档失败：" + e.getMessage());
        }
    }

    /**
     * 获取项目信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('item:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();
        TItemInfo item = tItemInfoService.selectTItemInfoById(id);
        Integer flow = item.getFlow();
        switch (flow) {
            case 0:
                if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                    item.setIsAudit(1);
                    item.setIsEdit(1);
                }
                break;
            case 1:
                if (roles.contains("dean")) {
                    item.setIsAudit(1);
                } else if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                    item.setIsEdit(1);
                }
                break;
            case 2:
            case 8:
                if (roles.contains("focal")) {
                    item.setIsAudit(1);
                }
                break;
            case 3:
                if (roles.contains("focalLeader")) {
                    item.setIsAudit(1);
                }
                break;
            case 4:
            case 5:
            case 6:
            case 10:
                if (roles.contains("projectManager")) {
                    item.setIsEdit(2);
                }
                break;
            case 7:
                if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
                    item.setIsEdit(3);
                }
                if (roles.contains("projectManager")) {
                    item.setIsEdit(2);
                }
                break;
            case 9:
//                if (user.getUserId().equals(item.getCreateById()) && roles.contains("teacher")) {
//                    item.setIsEdit(4);
//                }
                if (roles.contains("projectManager")) {
                    item.setIsEdit(2);
                }
                break;
            default:
                break;
        }
        return success(item);
    }

    /**
     * 新增项目信息
     */
    @PreAuthorize("@ss.hasPermi('item:info:add')")
    @Log(title = "项目信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemInfo tItemInfo) {
        Long id = tItemInfoService.insertTItemInfoTwo(tItemInfo);
        return AjaxResult.success(id);
    }

    /**
     * 修改项目信息
     */
    @PreAuthorize("@ss.hasPermi('item:info:edit')")
    @Log(title = "项目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemInfo tItemInfo) {
        return toAjax(tItemInfoService.updateTItemInfoTwo(tItemInfo));
    }

    /**
     * 删除项目信息
     */
    @PreAuthorize("@ss.hasPermi('item:info:remove')")
    @Log(title = "项目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 删除与项目信息表相关的其他库表数据
        // 1、合同信息表
        tContractInfoMapper.deleteTContractInfoByItemIds(ids);
        // 2、合同支付表
        tContractPaymentMapper.deleteTContractPaymentByItemIds(ids);
        // 3、合约规划表
        contractPlanMapper.deleteContractPlanByItemIds(ids);
        return toAjax(tItemInfoService.deleteTItemInfoByIds(ids));
    }

    /**
     * 更新项目当前状态
     *
     * @param itemId
     * @param flow
     * @return
     */
    @GetMapping("/updateFlow")
    public AjaxResult updateFlow(@RequestParam("itemId") String itemId, @RequestParam("flow") Integer flow) {
        List<String> itemIds = Arrays.asList(itemId.split(","));
        // 批量更新项目状态
        itemInfoMapper.updateItemFlow(itemIds, flow);

        // 获取当前年份
        String currentYear = String.valueOf(Year.now().getValue());
        //*/ 如果flow为6，则表示将节点更新为已完成学校评审，已入学校库，填充入学校库年度字段
        if (flow == 6) {
            itemInfoMapper.updateSchoolEnterYear(itemIds, currentYear);
        }
        //*/
        //*/ 如果flow为7，则表示将节点更新为已完成工信部评审，已入工信部库，填充入工信部库年度字段
        if (flow == 7) {
            itemInfoMapper.updateMinistryEnterYear(itemIds, currentYear);
        }
        //*/
        //*/ 如果flow为11，则表示将节点更新为已立项执行，填充立项执行年度字段
        if (flow == 11) {
            itemInfoMapper.updateExecuteYear(itemIds, currentYear);
        }
        //*/
        return AjaxResult.success();
    }

    /**
     * 更新项目当前状态Two
     *
     * @param itemId
     * @param flow
     * @return
     */
    @GetMapping("/updateFlowTwo")
    public AjaxResult updateFlowTwo(@RequestParam("itemId") String itemId, @RequestParam("flow") Integer flow) {
        List<String> itemIds = Arrays.asList(itemId.split(","));
        // 批量更新项目状态
        itemInfoMapper.updateItemFlow(itemIds, flow);

        // 获取当前年份
        String currentYear = String.valueOf(Year.now().getValue());

        //*/ 如果flow为1，则表示该节点是重新发起的情况，需要更新flow为1的节点reason
        if (flow == 1) {
            // 查询该项目所有的审核记录，用于后续批量更新
            TItemAudit tItemAudit1 = new TItemAudit();
            tItemAudit1.setItemId(Long.valueOf(itemId));
            List<TItemAudit> elList = tItemAuditMapper.selectTItemAuditList(tItemAudit1);
            elList.stream().filter(itemAudit -> Objects.equals(itemAudit.getFlow(), 1)).findFirst().ifPresent(itemAudit -> {
                itemAudit.setAuditStatus("1");  // 设置为已通过状态
                itemAudit.setReason("已重新发起审批");  // 设置审核意见
                SysUser user = SecurityUtils.getLoginUser().getUser();
                SysDept dept = user.getDept();
                itemAudit.setAuditBy(user.getNickName());  // 设置审核人名称
                itemAudit.setAuditDeptName(dept.getDeptName()); // 设置审核人的审核部门名称
                itemAudit.setCreateTime(DateUtils.getNowDate()); // 设置审核时间
                tItemAuditMapper.updateTItemAudit(itemAudit);
            });
        }

        //*/ 如果flow为7，则表示将节点更新为已立项执行，填充立项执行年度字段
        if (flow == 7) {
            itemInfoMapper.updateExecuteYear(itemIds, currentYear);
        }

        return AjaxResult.success();
    }

    /**
     * 获取申报项目数、合同数、合同总金额、合同累计支付金额
     * year 项目执行年份
     *
     * @return
     */
    @GetMapping("/getStatisticsOne")
    public AjaxResult getStatisticsOne(String year) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setExecuteYear(year);
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                itemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                itemInfo.setFocalUnitId(dept.getDeptId());
            }
        }
        Map map = itemInfoMapper.getStatisticsOne(itemInfo);
        // 立项总金额
        BigDecimal projectAmount = (BigDecimal) map.get("projectAmount");
        // 合同总金额
        BigDecimal contractAmount = (BigDecimal) map.get("contractAmount");
        // 累计支付额
        BigDecimal paymentAmount = (BigDecimal) map.get("paymentAmount");
        // 合同签约率
        BigDecimal signingRate = projectAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount.divide(projectAmount, 2, RoundingMode.HALF_UP);
        map.put("signingRate", signingRate);
        // 合同执行率
        BigDecimal implementationRate = projectAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount.divide(projectAmount, 2, RoundingMode.HALF_UP);
        map.put("implementationRate", signingRate);
        return AjaxResult.success(map);
    }

    /**
     * 获取项目资金分配统计
     *
     * @return
     */
    @GetMapping("/getStatisticsTwo")
    public AjaxResult getStatisticsTwo(String year) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        List<Map> map;
        if (roles.contains("projectManager")) {
            map = itemInfoMapper.getStatisticsTwo(year);
        } else if (roles.contains("focal") || roles.contains("focalLeader")) {
            //*/ 查询出归口单位下所有项目id
            List<Long> ids = itemInfoMapper.getItemIdsByFocalUnitId(dept.getDeptId());
            map = itemInfoMapper.getStatisticsTwoByItemIds(ids);
        } else {
            //*/ 根据当前用户，查询该用户所属单位申报的项目id
            List<Long> ids = itemInfoMapper.getItemIdsByDeptId(dept.getDeptId());
            map = itemInfoMapper.getStatisticsTwoByItemIds(ids);
        }
        return AjaxResult.success(map);
    }

    /**
     * 获取项目执行率
     *
     * @return
     */
    @GetMapping("/getStatisticsThree")
    public AjaxResult getStatisticsThree(Long itemId, String year) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        List<Map> map = new ArrayList<>();
        /*if (roles.contains("projectManager") || roles.contains("focal")) {
            map = itemInfoMapper.getStatisticsThree(itemId);
        } else {
            map = itemInfoMapper.getStatisticsThree(itemId);
        }*/
        // 获取当前项目类别
        String item_category = itemInfoMapper.selectTItemInfoById(itemId).getItemCategory();
        // 获取当前年份
//        String year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
        TImplementationRate tImplementationRate = new TImplementationRate();
        tImplementationRate.setCategory(item_category);
        tImplementationRate.setYear(year);
        // 根据项目类别和当前年份 查询目标执行率
        List<TImplementationRate> tImplementationRates = implementationRateMapper.selectTImplementationRateList(tImplementationRate);
        for (TImplementationRate tImplementationRate1 : tImplementationRates) {
            Map map1 = new HashMap();
            // 月份
            map1.put("month", tImplementationRate1.getMonth());
            // 当月目标执行率
            map1.put("targetRate", tImplementationRate1.getTargetRate());
            // 当月实际执行率
            // 1、获取截止当月已支付合同金额
            TContractPayment tContractPayment = new TContractPayment();
            tContractPayment.setItemId(itemId);
            tContractPayment.setYear(Integer.valueOf(year));
            tContractPayment.setMonth(Integer.valueOf(tImplementationRate1.getMonth()));
            BigDecimal payment = tContractPaymentMapper.selectTotalPaymentAmount(tContractPayment);
            payment = payment == null ? BigDecimal.valueOf(0) : payment;
            // 2、获取项目总立项金额
            TItemInfo itemInfo = itemInfoMapper.selectTItemInfoById(itemId);
            BigDecimal totalAmount = itemInfo.getTotalProjectAmount();
            totalAmount = totalAmount == null ? BigDecimal.valueOf(0) : totalAmount;
            // 3、计算截止当月实际执行率
            DecimalFormat df = new DecimalFormat("0.00%");
            String rate = totalAmount == BigDecimal.valueOf(0) ? "0.00%" : df.format(payment.divide(totalAmount, BigDecimal.ROUND_CEILING));
            map1.put("implementationRate", rate);
            map.add(map1);
        }
        return AjaxResult.success(map);
    }

    /**
     * 获取项目占比
     *
     * @return
     */
    @GetMapping("/getStatisticsFour")
    public AjaxResult getStatisticsFour(String start_time, String end_time) {
        List<Map> map = itemInfoMapper.getStatisticsFour(start_time, end_time);
        return AjaxResult.success(map);
    }

    /**
     * 获取三大基础类别一级指标占比情况
     *
     * @return
     */
    @GetMapping("/getAmountOfBasicCategory")
    public AjaxResult getAmountOfBasicCategory(String start_time, String end_time) {
        Map resultMap = new HashMap();
        // 获取房修类指标占比情况
        List<Map> map = itemInfoMapper.getAmountOfBasicCategory(start_time, end_time, "房修");
        resultMap.put("房修", map);
        // 获取基础类指标占比情况
        List<Map> map1 = itemInfoMapper.getAmountOfBasicCategory(start_time, end_time, "基础");
        resultMap.put("基础", map1);
        // 获取设备类指标占比情况
        List<Map> map2 = itemInfoMapper.getAmountOfBasicCategory(start_time, end_time, "设备");
        resultMap.put("设备", map2);
        return AjaxResult.success(resultMap);
    }

    /**
     * 获取待办事项
     *
     * @return
     */
    @GetMapping("/getToDoInfo")
    public AjaxResult getToDoInfo() {
        AjaxResult ajaxResult = new AjaxResult();
        //获取当前用户
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        TItemInfo tItemInfo = new TItemInfo();
        if (roles.contains("teacher")) {
            //查询被驳回的项目
            tItemInfo.setFlow(0);
            tItemInfo.setCreateById(user.getUserId());
            List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);
            String ids = "";
            if (list != null && list.size() != 0) {
                ids = list.stream().map(TItemInfo::getId).map(Object::toString).collect(Collectors.joining(","));
                ajaxResult.put("type", 1);
            } else {
                ajaxResult.put("type", 3);
            }
            ajaxResult.put("todoCount", list.size());
            ajaxResult.put("ids", ids);

        } else if (roles.contains("dean")) {
            //查询待副院长/院长审核数据
            tItemInfo.setFlow(1);
            tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);
            String ids = "";
            if (list != null && list.size() != 0) {
                ids = list.stream().map(TItemInfo::getId).map(Object::toString).collect(Collectors.joining(","));
                ajaxResult.put("type", 2);
            } else {
                ajaxResult.put("type", 3);
            }
            ajaxResult.put("todoCount", list.size());
            ajaxResult.put("ids", ids);
        } else if (roles.contains("focal")) {
            //查询待副院长/院长审核数据
            tItemInfo.setFlow(2);
            tItemInfo.setFocalUnitId(dept.getDeptId());
            List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);
            String ids = "";
            if (list != null && list.size() != 0) {
                ids = list.stream().map(TItemInfo::getId).map(Object::toString).collect(Collectors.joining(","));
                ajaxResult.put("type", 2);
            } else {
                ajaxResult.put("type", 3);
            }
            ajaxResult.put("todoCount", list.size());
            ajaxResult.put("ids", ids);
        } else if (roles.contains("focalLeader")) {
            //查询待副院长/院长审核数据
            tItemInfo.setFlow(3);
            tItemInfo.setFocalUnitId(dept.getDeptId());
            List<TItemInfo> list = tItemInfoService.selectTItemInfoList(tItemInfo);
            String ids = "";
            if (list != null && list.size() != 0) {
                ids = list.stream().map(TItemInfo::getId).map(Object::toString).collect(Collectors.joining(","));
                ajaxResult.put("type", 2);
            } else {
                ajaxResult.put("type", 3);
            }
            ajaxResult.put("todoCount", list.size());
            ajaxResult.put("ids", ids);
        }
        return ajaxResult;
    }

    /**
     * 获取项目统计信息
     *
     * @return
     */
    @GetMapping("/getItemStatistics")
    public AjaxResult getItemStatistics(TItemInfo tItemInfo) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();

        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                tItemInfo.setFocalUnitId(dept.getDeptId());
            }
        }

        //*/ 获取项目类别字典
        SysDictData dictData = new SysDictData();
        dictData.setDictType("item_category");
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        String[] itemCategories = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            itemCategories[i] = list.get(i).getDictValue();
        }
        //*/
        List<Map> map = new ArrayList<>();
        for (String itemCategory : itemCategories) {
            tItemInfo.setItemCategory(itemCategory);
            Map tMap = new HashMap();
            tMap = itemInfoMapper.getItemStatistics(tItemInfo);
            // 避免数据库表中无法查询到项目类别的数据时，无法显示项目类别的情况
            tMap.put("item_category", itemCategory);
            map.add(tMap);
        }
        return AjaxResult.success(map);
    }

    /**
     * 获取项目的排名信息
     *
     * @param year 查询排名的年份
     * @return 返回包含项目排名信息的 AjaxResult 对象
     */
    @GetMapping("/getItemSort")
    public AjaxResult getItemSort(String year) {
        // 获取当前登录用户的信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 获取当前用户的部门信息
        SysDept dept = user.getDept();
        // 获取用户的所有角色名称
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        // 从数据库中查询按照执行率排序的项目列表
        List<ItemRank> itemRankList = itemInfoMapper.getItemSort(year);

        // 构造查询条件，用于获取当前年份申报的项目总数
        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setStatus("1"); // 状态为"已申报"

        // 如果不是项目管理员，则根据角色过滤项目范围
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导，则只能查看本单位的项目
                itemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或领导，则只能查看归口单位相关的项目
                itemInfo.setFocalUnitId(dept.getDeptId());
            }
        }

        // 查询符合条件的项目列表，并统计数量
        List<TItemInfo> itemInfoList = itemInfoMapper.selectTItemInfoList(itemInfo);
        int count = itemInfoList == null ? 0 : itemInfoList.size(); // 总项目数

        // 根据用户角色决定如何设置项目排名
        if (roles.contains("projectManager") || roles.contains("focal")) {
            // 如果是项目管理员或归口单位相关人员，按顺序设置排名（即直接使用索引）
            for (int i = 0; i < itemRankList.size(); i++) {
                itemRankList.get(i).setRank(String.valueOf(++i)); // 设置排名（从1开始）
            }
        } else {
            // 其他角色（如教师、院长）根据执行率计算排名百分比
            itemRankList.forEach(itemRank -> {
                // 查询执行率低于当前项目的项目数量
                Integer lowerNumber = itemInfoMapper.getItemNumberLowerRank(new BigDecimal(itemRank.getExecuteRate()));
                // 计算排名百分比
                float lowerPercentage = (float) lowerNumber / count;
                // 使用 NumberFormat 格式化百分比，保留两位小数
                NumberFormat numberFormat = NumberFormat.getPercentInstance();
                numberFormat.setMaximumFractionDigits(2);
                // 设置格式化后的排名百分比
                itemRank.setRank(numberFormat.format(lowerPercentage));
            });
        }

        // 返回成功结果，并携带项目排名数据
        return AjaxResult.success(itemRankList);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TItemInfo> util = new ExcelUtil<TItemInfo>(TItemInfo.class);
        util.importTemplateExcel(response, "项目信息");
    }

    /**
     * 导入项目信息
     *
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TItemInfo> util = new ExcelUtil<TItemInfo>(TItemInfo.class);
        List<TItemInfo> TItemInfos = util.importExcel(file.getInputStream());
        String message = tItemInfoService.importItemInfo(TItemInfos, updateSupport);
        return success(message);
    }

    /**
     * 获取项目执行模块列表数据
     */
    @GetMapping("/execution/list")
    public AjaxResult executionList(String year) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();

        //*/ 获取项目类别字典
        SysDictData dictData = new SysDictData();
        dictData.setDictType("item_category");
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        String[] itemCategories = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            itemCategories[i] = list.get(i).getDictValue();
        }
        //*/
        List<Map> mapList = new ArrayList<>();
        //*/ 获取项目合计数据
        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setExecuteYear(year);
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                itemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                itemInfo.setFocalUnitId(dept.getDeptId());
            }
        }
        Map totalMap = itemInfoMapper.executionItemInfo(itemInfo);
        totalMap.put("projectName", "项目合计");
        // 合同金额
        BigDecimal contractAmount = (BigDecimal) totalMap.get("contractAmount");
        // 总立项金额
        BigDecimal totalAmount = (BigDecimal) totalMap.get("totalAmount");
        // 累计支付金额
        BigDecimal paymentAmount = (BigDecimal) totalMap.get("paymentAmount");
        // 累计结算金额
        BigDecimal settlementAmount = (BigDecimal) totalMap.get("settlementAmount");
        // 签约率
        BigDecimal agencyRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);
        // 执行率
        BigDecimal implementationRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);
        // 动态成本
        BigDecimal dynamicCost = settlementAmount.compareTo(new BigDecimal("0.00")) > 0 ? settlementAmount : (contractAmount.compareTo(new BigDecimal("0.00")) > 0 ? contractAmount : totalAmount);
        // 支付额
        List<Map> payList = tContractPaymentMapper.selectTotalEveryMonthPayment(year);
        totalMap.put("agencyRate", agencyRate);
        totalMap.put("implementationRate", implementationRate);
        totalMap.put("dynamicCost", dynamicCost);
        totalMap.put("paymentInfo", payList);
        totalMap.put("hasChildren", false);
        mapList.add(totalMap);
        //*/
        //*/ 获取不同项目类别统计数据
        for (String itemCategory : itemCategories) {
            itemInfo.setItemCategory(itemCategory);
            Map map = itemInfoMapper.executionItemInfo(itemInfo);
            map.put("projectName", itemCategory);
            // 合同金额
            BigDecimal contractAmount1 = (BigDecimal) map.get("contractAmount");
            // 总立项金额
            BigDecimal totalAmount1 = (BigDecimal) map.get("totalAmount");
            // 累计支付金额
            BigDecimal paymentAmount1 = (BigDecimal) map.get("paymentAmount");
            // 累计结算金额
            BigDecimal settlementAmount1 = (BigDecimal) map.get("settlementAmount");
            // 签约率
            BigDecimal agencyRate1 = totalAmount1.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount1.divide(totalAmount1, 2, RoundingMode.HALF_UP);
            // 执行率
            BigDecimal implementationRate1 = totalAmount1.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount1.divide(totalAmount1, 2, RoundingMode.HALF_UP);
            // 动态成本
            BigDecimal dynamicCost1 = settlementAmount1.compareTo(new BigDecimal("0.00")) > 0 ? settlementAmount1 : (contractAmount1.compareTo(new BigDecimal("0.00")) > 0 ? contractAmount1 : totalAmount1);
            // 支付额
            List<Map> payList1 = tContractPaymentMapper.selectTotalEveryMonthPayment(year);
            map.put("agencyRate", agencyRate1);
            map.put("implementationRate", implementationRate1);
            map.put("dynamicCost", dynamicCost1);
            map.put("paymentInfo", payList1);

            //*/ 根据类别下面执行项目数量来判断是否存在子节点
            // 查询已立项执行的具体项目
            List<TItemInfo> infoList = itemInfoMapper.selectExecutedItemInfoList(itemInfo);
            Boolean hasChildren = infoList != null && infoList.size() > 0;
            map.put("hasChildren", hasChildren);
            //*/
            mapList.add(map);
        }
        //*/
        return AjaxResult.success(mapList);
    }

    /**
     * 获取项目执行模块列表数据Two
     * 该方法用于获取项目执行的统计数据，包括项目合计数据和按项目类别分类的统计数据
     *
     * @param year 项目执行年份，用于筛选特定年份的项目数据
     * @return 返回包含项目执行统计数据的AjaxResult对象
     */
    @GetMapping("/execution/listTwo")
    public AjaxResult executionListTwo(String year) {
        // 获取当前登录用户信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 获取用户的所有角色标识
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        // 获取用户所属部门
        SysDept dept = user.getDept();

        // 获取项目类别字典数据
        SysDictData dictData = new SysDictData();
        dictData.setDictType("item_category"); // 设置字典类型为项目类别
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        // 将字典数据转换为字符串数组，用于后续循环处理
        String[] itemCategories = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            itemCategories[i] = list.get(i).getDictValue();
        }

        // 创建返回结果列表
        List<Map> mapList = new ArrayList<>();

        // 获取项目合计数据
        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setExecuteYear(year); // 设置执行年份

        // 根据用户角色设置查询条件，实现数据权限控制
        if (!roles.contains("projectManager")) { // 如果不是项目管理员
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导，只能查看本单位的项目
                itemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导，只能查看归口单位的项目
                itemInfo.setFocalUnitId(dept.getDeptId());
            }
        }

        // 查询项目合计数据
        Map totalMap = itemInfoMapper.executionItemInfo(itemInfo);
        totalMap.put("projectName", "项目合计"); // 设置项目名称为"项目合计"

        // 计算各项财务指标
        // 获取合同总金额
        BigDecimal contractAmount = (BigDecimal) totalMap.get("contractAmount");
        // 获取项目总立项金额
        BigDecimal totalAmount = (BigDecimal) totalMap.get("totalAmount");
        // 获取累计支付金额
        BigDecimal paymentAmount = (BigDecimal) totalMap.get("paymentAmount");
        // 获取累计结算金额
        BigDecimal settlementAmount = (BigDecimal) totalMap.get("settlementAmount");

        // 计算签约率 = 合同金额 / 总立项金额（避免除零错误）
        BigDecimal agencyRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);

        // 计算执行率 = 累计支付金额 / 总立项金额（避免除零错误）
        BigDecimal implementationRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);

        // 计算动态成本：优先使用结算金额，其次是合同金额，最后是总立项金额
        BigDecimal dynamicCost = settlementAmount.compareTo(new BigDecimal("0.00")) > 0 ? settlementAmount : (contractAmount.compareTo(new BigDecimal("0.00")) > 0 ? contractAmount : totalAmount);

        // 获取每月支付情况
        List<Map> payList = tContractPaymentMapper.selectTotalEveryMonthPayment(year);

        // 将计算结果添加到返回数据中
        totalMap.put("agencyRate", agencyRate); // 签约率
        totalMap.put("implementationRate", implementationRate); // 执行率
        totalMap.put("dynamicCost", dynamicCost); // 动态成本
        totalMap.put("paymentInfo", payList); // 支付信息
        totalMap.put("hasChildren", false); // 项目合计没有子节点

        // 将项目合计数据添加到返回列表
        mapList.add(totalMap);

        // 按项目类别获取统计数据
        for (String itemCategory : itemCategories) {
            // 设置当前循环的项目类别
            itemInfo.setItemCategory(itemCategory);
            // 查询该类别的项目执行信息
            Map map = itemInfoMapper.executionItemInfo(itemInfo);
            map.put("projectName", itemCategory); // 设置项目名称为类别名称

            // 计算各项财务指标（与上面合计数据计算逻辑相同）
            // 获取合同金额
            BigDecimal contractAmount1 = (BigDecimal) map.get("contractAmount");
            // 获取总立项金额
            BigDecimal totalAmount1 = (BigDecimal) map.get("totalAmount");
            // 获取累计支付金额
            BigDecimal paymentAmount1 = (BigDecimal) map.get("paymentAmount");
            // 获取累计结算金额
            BigDecimal settlementAmount1 = (BigDecimal) map.get("settlementAmount");

            // 计算签约率
            BigDecimal agencyRate1 = totalAmount1.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount1.divide(totalAmount1, 2, RoundingMode.HALF_UP);

            // 计算执行率
            BigDecimal implementationRate1 = totalAmount1.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount1.divide(totalAmount1, 2, RoundingMode.HALF_UP);

            // 计算动态成本
            BigDecimal dynamicCost1 = settlementAmount1.compareTo(new BigDecimal("0.00")) > 0 ? settlementAmount1 : (contractAmount1.compareTo(new BigDecimal("0.00")) > 0 ? contractAmount1 : totalAmount1);

            // 获取每月支付情况
            List<Map> payList1 = tContractPaymentMapper.selectTotalEveryMonthPayment(year);

            // 将计算结果添加到返回数据中
            map.put("agencyRate", agencyRate1); // 签约率
            map.put("implementationRate", implementationRate1); // 执行率
            map.put("dynamicCost", dynamicCost1); // 动态成本
            map.put("paymentInfo", payList1); // 支付信息

            // 判断该类别是否有子节点（已立项执行的具体项目）
            List<TItemInfo> infoList = itemInfoMapper.selectExecutedItemInfoList(itemInfo);
            Boolean hasChildren = infoList != null && infoList.size() > 0;
            map.put("hasChildren", hasChildren); // 设置是否有子节点标志

            // 将该类别的统计数据添加到返回列表
            mapList.add(map);
        }

        // 返回成功结果，并携带项目执行统计数据
        return AjaxResult.success(mapList);
    }

    /**
     * 获取不同项目类别下具体项目列表数据
     */
    @GetMapping("/execution/listItems")
    public AjaxResult executionListItems(String year, String category) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();

        // 查询指定年份和项目类别下所有的执行阶段项目
        List<Map> mapList = itemInfoMapper.executionItemDetail(year, category);

        //*/ 根据用户角色对项目进行过滤,除国资处经办人以外的角色，需要先查询负责的项目
        TItemInfo tItemInfo = new TItemInfo();
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                tItemInfo.setFocalUnitId(dept.getDeptId());
            }
        }
        List<TItemInfo> itemInfoList = itemInfoMapper.selectTItemInfoList(tItemInfo);
        // 所有负责项目的id集合
        List<Long> itemIds = new ArrayList<>();
        for (TItemInfo info : itemInfoList) {
            itemIds.add(info.getId());
        }
        mapList = mapList.stream().filter(map -> itemIds.contains(map.get("itemId"))).collect(Collectors.toList());
        //*/

        for (Map map : mapList) {
            // 合同金额
            BigDecimal contractAmount = (BigDecimal) map.get("contractAmount");
            // 总立项金额
            BigDecimal totalAmount = (BigDecimal) map.get("totalAmount");
            // 累计结算金额
            BigDecimal settlementAmount = (BigDecimal) map.get("settlementAmount");
            // 累计支付金额
            BigDecimal paymentAmount = (BigDecimal) map.get("paymentAmount");
            // 签约率
            BigDecimal agencyRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);
            // 执行率
            BigDecimal implementationRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);
            // 动态成本
            BigDecimal dynamicCost = settlementAmount.compareTo(new BigDecimal("0.00")) > 0 ? settlementAmount : (contractAmount.compareTo(new BigDecimal("0.00")) > 0 ? contractAmount : totalAmount);
            map.put("agencyRate", agencyRate);
            map.put("implementationRate", implementationRate);
            map.put("dynamicCost", dynamicCost);

            // 支付额
            Long itemId = (Long) map.get("itemId");
            List<Map> payList = tContractPaymentMapper.selectItemEveryMonthPayment(year, itemId);
            map.put("paymentInfo", payList);

            // 根据项目下的合同数量判断是否有子节点
            List<Map> list = itemInfoMapper.executionContractDetail(year, itemId);
            Boolean hasChildren = list != null && list.size() > 0;
            map.put("hasChildren", hasChildren);

            //*/ 根据V2合约规划审批状态来确定各个角色的修改和审批按钮显示与否
            // V2合约规划填报状态
            String V2FillStatus = tItemAuditMapper.selectFlowStatusByItemId(12, itemId);
            // V2合约规划-归口单位审核状态
            String V2AuditStatusFocal = tItemAuditMapper.selectFlowStatusByItemId(13, itemId);
            // V2合约规划-国资处审核状态
            String V2AuditStatusPM = tItemAuditMapper.selectFlowStatusByItemId(14, itemId);
            Long createById = (Long) map.get("createById");
            Integer isEdit = 0;
            Integer isAudit = 0;
            Integer addChildContract = 0;
            if ("3".equals(V2FillStatus)) {
                // 如果处于填报V2合约规划阶段
                // 学院老师显示修改按钮
                if (user.getUserId().equals(createById) && roles.contains("teacher")) {
                    isEdit = 1;
                }
                // 国资处经办人（管理员）显示修改按钮
                if (roles.contains("projectManager")) {
                    isEdit = 2;
                }
            } else if ("1".equals(V2FillStatus) && "3".equals(V2AuditStatusFocal)) {
                // 如果处于V2合约规划审核阶段
                // 归口单位经办人显示审核按钮
                if (roles.contains("focal")) {
                    isAudit = 1;
                }
            } else if ("1".equals(V2AuditStatusFocal) && "3".equals(V2AuditStatusPM)) {
                if (roles.contains("projectManager")) {
                    isAudit = 1;
                }
            } else if ("1".equals(V2AuditStatusPM)) {
                // 如果V2合约规划已完成审批
                // 国资处经办人（管理员）显示修改按钮、添加子合同按钮
                if (roles.contains("projectManager")) {
                    isEdit = 2;
                    addChildContract = 1;
                }
            }
            map.put("isEdit", isEdit);
            map.put("isAudit", isAudit);
            map.put("addChildContract", addChildContract);
            //*/
        }
        return AjaxResult.success(mapList);
    }

    /**
     * 获取不同项目类别下具体项目列表数据Two
     */
    @GetMapping("/execution/listItemsTwo")
    public AjaxResult executionListItemsTwo(String year, String category) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();

        // 查询指定年份和项目类别下所有的执行阶段项目
        List<Map> mapList = itemInfoMapper.executionItemDetail(year, category);

        //*/ 根据用户角色对项目进行过滤,除国资处经办人以外的角色，需要先查询负责的项目
        TItemInfo tItemInfo = new TItemInfo();
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                tItemInfo.setFocalUnitId(dept.getDeptId());
            }
        }
        List<TItemInfo> itemInfoList = itemInfoMapper.selectTItemInfoList(tItemInfo);
        // 所有负责项目的id集合
        List<Long> itemIds = new ArrayList<>();
        for (TItemInfo info : itemInfoList) {
            itemIds.add(info.getId());
        }
        mapList = mapList.stream().filter(map -> itemIds.contains(map.get("itemId"))).collect(Collectors.toList());
        //*/

        for (Map map : mapList) {
            // 合同金额
            BigDecimal contractAmount = (BigDecimal) map.get("contractAmount");
            // 总立项金额
            BigDecimal totalAmount = (BigDecimal) map.get("totalAmount");
            // 累计结算金额
            BigDecimal settlementAmount = (BigDecimal) map.get("settlementAmount");
            // 累计支付金额
            BigDecimal paymentAmount = (BigDecimal) map.get("paymentAmount");
            // 签约率
            BigDecimal agencyRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : contractAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);
            // 执行率
            BigDecimal implementationRate = totalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : paymentAmount.divide(totalAmount, 2, RoundingMode.HALF_UP);
            // 动态成本
            BigDecimal dynamicCost = settlementAmount.compareTo(new BigDecimal("0.00")) > 0 ? settlementAmount : (contractAmount.compareTo(new BigDecimal("0.00")) > 0 ? contractAmount : totalAmount);
            map.put("agencyRate", agencyRate);
            map.put("implementationRate", implementationRate);
            map.put("dynamicCost", dynamicCost);

            // 支付额
            Long itemId = (Long) map.get("itemId");
            List<Map> payList = tContractPaymentMapper.selectItemEveryMonthPayment(year, itemId);
            map.put("paymentInfo", payList);

            // 根据项目下的合同数量判断是否有子节点
            List<Map> list = itemInfoMapper.executionContractDetail(year, itemId);
            Boolean hasChildren = list != null && list.size() > 0;
            map.put("hasChildren", hasChildren);

            //*/ 根据V2合约规划审批状态来确定各个角色的修改和审批按钮显示与否
            // V2合约规划填报状态
            String V2FillStatus = tItemAuditMapper.selectFlowStatusByItemId(12, itemId);
            // V2合约规划-归口单位审核状态
            String V2AuditStatusFocal = tItemAuditMapper.selectFlowStatusByItemId(13, itemId);
            // V2合约规划-国资处审核状态
            String V2AuditStatusPM = tItemAuditMapper.selectFlowStatusByItemId(14, itemId);
            Long createById = (Long) map.get("createById");
            Integer isEdit = 0;
            Integer isAudit = 0;
            Integer addChildContract = 0;
            if ("3".equals(V2FillStatus)) {
                // 如果处于填报V2合约规划阶段
                // 学院老师显示修改按钮
                if (user.getUserId().equals(createById) && roles.contains("teacher")) {
                    isEdit = 1;
                }
                // 国资处经办人（管理员）显示修改按钮
                if (roles.contains("projectManager")) {
                    isEdit = 2;
                }
            } else if ("1".equals(V2FillStatus) && "3".equals(V2AuditStatusFocal)) {
                // 如果处于V2合约规划审核阶段
                // 归口单位经办人显示审核按钮
                if (roles.contains("focal")) {
                    isAudit = 1;
                }
            } else if ("1".equals(V2AuditStatusFocal) && "3".equals(V2AuditStatusPM)) {
                if (roles.contains("projectManager")) {
                    isAudit = 1;
                }
            } else if ("1".equals(V2AuditStatusPM)) {
                // 如果V2合约规划已完成审批
                // 国资处经办人（管理员）显示修改按钮、添加子合同按钮
                if (roles.contains("projectManager")) {
                    isEdit = 2;
                    addChildContract = 1;
                }
            }
            map.put("isEdit", isEdit);
            map.put("isAudit", isAudit);
            map.put("addChildContract", addChildContract);
            //*/
        }
        return AjaxResult.success(mapList);
    }


    /**
     * 获取不同项目下具体合同列表数据
     *
     * @param itemId 项目id
     */
    @GetMapping("/execution/listContracts")
    public AjaxResult executionListContracts(String year, Long itemId) {
        List<Map> mapList = itemInfoMapper.executionContractDetail(year, itemId);
        for (Map map : mapList) {
            Long contractId = (Long) map.get("contractId");
            List<Map> payList = tContractPaymentMapper.selectContractEveryMonthPayment(year, contractId);
            map.put("paymentInfo", payList);

            //*/ 动态成本计算，取数优先顺序为：结算金额-送审金额-合同金额-动态立项金额
            // 结算金额
            BigDecimal settlementAmount = (BigDecimal) map.get("settlementAmount");
            // 送审金额
            BigDecimal checkAmount = (BigDecimal) map.get("checkAmount");
            // 合同金额
            BigDecimal contractAmount = (BigDecimal) map.get("contractAmount");
            // 动态立项金额
            BigDecimal totalAmount = (BigDecimal) map.get("totalAmount");

            BigDecimal dynamicCost = settlementAmount.compareTo(BigDecimal.ZERO) > 0 ? settlementAmount : (checkAmount.compareTo(BigDecimal.ZERO) > 0 ? checkAmount : (contractAmount.compareTo(BigDecimal.ZERO) > 0 ? contractAmount : totalAmount));
            map.put("dynamicCost", dynamicCost);
            //*/
        }
        return AjaxResult.success(mapList);
    }

    /**
     * 执行项目查询接口
     */
    @GetMapping("/execution/query")
    public AjaxResult executionQuery(ExecutedItemVO executedItemVO) {
        List<Map> map = itemInfoMapper.queryExecutedItemInfo(executedItemVO);
        return AjaxResult.success(map);
    }

    /**
     * 获取项目执行模块列表数据
     */
    @GetMapping("/performance/list")
    public AjaxResult performanceList(String year) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();

        //*/ 获取项目类别字典
        SysDictData dictData = new SysDictData();
        dictData.setDictType("item_category");
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        String[] itemCategories = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            itemCategories[i] = list.get(i).getDictValue();
        }
        //*/
        List<Map> mapList = new ArrayList<>();
        //*/ 获取项目合计数据
        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setExecuteYear(year);
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                itemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                itemInfo.setFocalUnitId(dept.getDeptId());
            }
        }
        Map totalMap = itemInfoMapper.performanceItemInfo(itemInfo);
        totalMap.put("projectName", "项目合计");
        totalMap.put("hasChildren", false);
        mapList.add(totalMap);
        //*/
        //*/ 获取不同项目类别统计数据
        for (String itemCategory : itemCategories) {
            itemInfo.setItemCategory(itemCategory);
            Map map = itemInfoMapper.performanceItemInfo(itemInfo);
            map.put("projectName", itemCategory);

            //*/ 根据类别下面执行项目数量来判断是否存在子节点
            // 查询已立项执行的具体项目
            List<TItemInfo> infoList = itemInfoMapper.selectFinishedItemInfoList(itemInfo);
            Boolean hasChildren = infoList != null && infoList.size() > 0;
            map.put("hasChildren", hasChildren);
            //*/
            mapList.add(map);
        }
        //*/
        return AjaxResult.success(mapList);
    }

    /**
     * 绩效评价模块根据项目分类以及执行年度，查询已执行完成项目
     */
    @GetMapping("/performance/listItems")
    public AjaxResult performanceListItems(String year, String category) {
        //根据用户拥有角色进行判断
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        SysDept dept = user.getDept();

        // 查询指定年份和项目类别下所有的已经执行完成项目
        List<Map> mapList = itemInfoMapper.performanceItemDetail(year, category);

        //*/ 根据用户角色对项目进行过滤,除国资处经办人以外的角色，需要先查询负责的项目
        TItemInfo tItemInfo = new TItemInfo();
        if (!roles.contains("projectManager")) {
            if (roles.contains("teacher") || roles.contains("dean")) {
                // 如果是学院老师或者学院领导
                tItemInfo.setApplyUnitId(String.valueOf(dept.getDeptId()));
            } else if (roles.contains("focal") || roles.contains("focalLeader")) {
                // 如果是归口单位经办人或者归口单位领导
                tItemInfo.setFocalUnitId(dept.getDeptId());
            }
        }
        List<TItemInfo> itemInfoList = itemInfoMapper.selectTItemInfoList(tItemInfo);
        // 所有负责项目的id集合
        List<Long> itemIds = new ArrayList<>();
        for (TItemInfo info : itemInfoList) {
            itemIds.add(info.getId());
        }
        mapList = mapList.stream().filter(map -> itemIds.contains(map.get("itemId"))).collect(Collectors.toList());
        //*/

        for (Map map : mapList) {
            BigDecimal paymentAmount = itemInfoMapper.queryPaymentAmount(year, String.valueOf(map.get("itemId")));
            TItemInfo itemInfo = itemInfoMapper.selectTItemInfoById((Long) map.get("itemId"));
            map.put("actualCompletion", itemInfo.getActualCompletion());
            map.put("paymentAmount", paymentAmount);
        }
        return AjaxResult.success(mapList);
    }

    /**
     * 新增专业审核记录
     */
    @PostMapping("/professionalReview")
    public AjaxResult professionalReview(@RequestBody ProfessionalReviewVO<SortItemVO> reviewVO) {
        tItemInfoService.processProfessionalReview(reviewVO);
        return AjaxResult.success();
    }

    /**
     * 新增综合评审记录
     */
    @PostMapping("/compositeReview")
    public AjaxResult compositeReview(@RequestBody ProfessionalReviewVO<CompositeVO> reviewVO) {
        tItemInfoService.compositeReview(reviewVO);
        return AjaxResult.success();
    }

    /**
     * 新增入库评审记录
     */
    @PostMapping("/storeReview")
    public AjaxResult storeReview(@RequestBody ProfessionalReviewVO<StoreVO> reviewVO) {
        tItemInfoService.storeReview(reviewVO);
        return AjaxResult.success();
    }

    /**
     * 新增执行计划排序记录
     */
    @PostMapping("/planReview")
    public AjaxResult planReview(@RequestBody ProfessionalReviewVO<PlanVO> reviewVO) {
        tItemInfoService.planReview(reviewVO);
        return AjaxResult.success();
    }
}
