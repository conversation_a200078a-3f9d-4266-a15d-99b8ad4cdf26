package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目支出预算明细对象 t_item_budget
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public class TItemBudget extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    /** 明细支出预算类型 */
    @Excel(name = "明细支出预算类型")
    private String type;

    /** 明细支出金额（单位：元） */
    @Excel(name = "明细支出金额", readConverterExp = "单=位：元")
    private BigDecimal amount;

    /** 状态（1：正常；9：删除） */
    @Excel(name = "状态", readConverterExp = "1=：正常；9：删除")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemId", getItemId())
            .append("type", getType())
            .append("amount", getAmount())
            .append("status", getStatus())
            .toString();
    }
}
