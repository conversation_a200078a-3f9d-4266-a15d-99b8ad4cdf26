package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 绩效评价-项目资金对象 t_item_funding
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public class TItemFunding extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 项目资金分类 */
    @Excel(name = "项目资金分类")
    private String type;

    /** 年初预算金额 */
    @Excel(name = "年初预算金额")
    private BigDecimal budgetStartOfYear;

    /** 全年预算金额 */
    @Excel(name = "全年预算金额")
    private BigDecimal budgetAllOfYear;

    /** 全年执行金额 */
    @Excel(name = "全年执行金额")
    private BigDecimal executionAmount;

    /** 分值 */
    @Excel(name = "分值")
    private Integer scoreValue;

    /** 得分 */
    @Excel(name = "得分")
    private Integer score;

    /** 执行率 */
    @Excel(name = "执行率")
    private String implementionRate;

    /** 状态（1：启用；9：删除） */
    @Excel(name = "状态", readConverterExp = "1=：启用；9：删除")
    private Integer status;

    /** 项目类别 */
    @Excel(name = "项目类别")
    private String itemCategory;

    /** 执行年度 */
    @Excel(name = "执行年度")
    private String executeYear;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setBudgetStartOfYear(BigDecimal budgetStartOfYear) 
    {
        this.budgetStartOfYear = budgetStartOfYear;
    }

    public BigDecimal getBudgetStartOfYear() 
    {
        return budgetStartOfYear;
    }
    public void setBudgetAllOfYear(BigDecimal budgetAllOfYear) 
    {
        this.budgetAllOfYear = budgetAllOfYear;
    }

    public BigDecimal getBudgetAllOfYear() 
    {
        return budgetAllOfYear;
    }
    public void setExecutionAmount(BigDecimal executionAmount) 
    {
        this.executionAmount = executionAmount;
    }

    public BigDecimal getExecutionAmount() 
    {
        return executionAmount;
    }
    public void setScoreValue(Integer scoreValue) 
    {
        this.scoreValue = scoreValue;
    }

    public Integer getScoreValue() 
    {
        return scoreValue;
    }
    public void setScore(Integer score) 
    {
        this.score = score;
    }

    public Integer getScore() 
    {
        return score;
    }
    public void setImplementionRate(String implementionRate) 
    {
        this.implementionRate = implementionRate;
    }

    public String getImplementionRate() 
    {
        return implementionRate;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setItemCategory(String itemCategory) 
    {
        this.itemCategory = itemCategory;
    }

    public String getItemCategory() 
    {
        return itemCategory;
    }
    public void setExecuteYear(String executeYear) 
    {
        this.executeYear = executeYear;
    }

    public String getExecuteYear() 
    {
        return executeYear;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("budgetStartOfYear", getBudgetStartOfYear())
            .append("budgetAllOfYear", getBudgetAllOfYear())
            .append("executionAmount", getExecutionAmount())
            .append("scoreValue", getScoreValue())
            .append("score", getScore())
            .append("implementionRate", getImplementionRate())
            .append("status", getStatus())
            .append("itemCategory", getItemCategory())
            .append("executeYear", getExecuteYear())
            .toString();
    }
}
