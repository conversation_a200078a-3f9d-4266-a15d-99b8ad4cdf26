package com.ruoyi.tiem.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.tiem.domain.*;
import com.ruoyi.tiem.mapper.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.service.ITItemInfoService;

/**
 * 项目信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Service
public class TItemInfoServiceImpl implements ITItemInfoService {
    private static final Logger log = LoggerFactory.getLogger(TItemInfoServiceImpl.class);
    @Autowired
    private TItemInfoMapper tItemInfoMapper;

    @Autowired
    private TItemAuditMapper auditMapper;

    @Autowired
    private TItemAuditDetailMapper auditDetailMapper;

    @Autowired
    private ContractPlanMapper contractPlanMapper;

    @Autowired
    private MilestonPlanServiceImpl milestonPlanService;

    @Autowired
    private PerformanceMapper performanceMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询项目信息
     *
     * @param id 项目信息主键
     * @return 项目信息
     */
    @Override
    public TItemInfo selectTItemInfoById(Long id) {
        TItemInfo tItemInfo = tItemInfoMapper.selectTItemInfoById(id);
        //获取合约规划、绩效信息、里程碑等信息
        ContractPlan contractPlan = new ContractPlan();
        contractPlan.setItemId(id);
        contractPlan.setContractType("V1");
        List<ContractPlan> contractPlans = contractPlanMapper.selectContractPlanList(contractPlan);

        ContractPlan contractPlanV2 = new ContractPlan();
        contractPlanV2.setItemId(id);
        contractPlanV2.setContractType("V2");
        List<ContractPlan> contractPlansV2 = contractPlanMapper.selectContractPlanList(contractPlanV2);

        //获取绩效信息
        Performance performance = new Performance();
        performance.setItemId(id);
        List<Performance> performances = performanceMapper.selectPerformanceList(performance);
        //获取里程碑信息
        MilestonPlan milestonPlan = new MilestonPlan();
        milestonPlan.setItemId(id);
        List<MilestonPlan> milestonPlans = milestonPlanService.selectMilestonPlanList(milestonPlan);

        tItemInfo.setContractPlanList(contractPlans);
        tItemInfo.setContractPlanListV2(contractPlansV2);
        tItemInfo.setPerformanceList(performances);
        tItemInfo.setMilestonPlanList(milestonPlans);
        return tItemInfo;
    }

    /**
     * 查询项目信息列表
     *
     * @param tItemInfo 项目信息
     * @return 项目信息
     */
    @Override
    public List<TItemInfo> selectTItemInfoList(TItemInfo tItemInfo) {
        List<TItemInfo> itemInfoList = tItemInfoMapper.selectTItemInfoList(tItemInfo);
        for (TItemInfo itemInfo : itemInfoList) {
            //获取合约规划、绩效信息、里程碑等信息
            ContractPlan contractPlan = new ContractPlan();
            contractPlan.setItemId(itemInfo.getId());
            contractPlan.setContractType("V1");
            List<ContractPlan> contractPlans = contractPlanMapper.selectContractPlanList(contractPlan);

            ContractPlan contractPlanV2 = new ContractPlan();
            contractPlanV2.setItemId(itemInfo.getId());
            contractPlanV2.setContractType("V2");
            List<ContractPlan> contractPlansV2 = contractPlanMapper.selectContractPlanList(contractPlanV2);

            itemInfo.setContractPlanList(contractPlans);
            itemInfo.setContractPlanListV2(contractPlansV2);

            // 当前项目在当前flow中的填报状态，用于多单位联合申报的情况
            String currentFlowAuditStatus = auditMapper.selectFlowStatusByItemId(itemInfo.getFlow(),itemInfo.getId());
            itemInfo.setCurrentFlowStatus(currentFlowAuditStatus);
        }
        return itemInfoList;
    }

    /**
     * 根据项目类别转化 项目编号中的简写
     *
     * @param itemCategory
     * @return
     */
    public static String getItemCategoryAbbreviation(String itemCategory) {
        String abbreviation = "";
        switch (itemCategory) {
            case "房屋修缮":
                abbreviation = "FX";
                break;
            case "基础设施改造":
                abbreviation = "JC";
                break;
            case "设备资料购置":
                abbreviation = "SB";
                break;
            default:
                abbreviation = "";
        }
        return abbreviation;
    }

    /**
     * 新增项目信息
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    @Override
    public Long insertTItemInfo(TItemInfo tItemInfo) {
        // 1. 获取当前用户和部门信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        tItemInfo.setCreateTime(DateUtils.getNowDate());
        tItemInfo.setCreateById(user.getUserId());

        // 2. 生成项目编号
        // 编号规则：GS + 申报年度 + 项目类别简写 + 三位序号
        // 项目类别简写：房屋修缮(FX)、基础设施改造(JC)、设备资料购置(SB)
        String abbreviation = getItemCategoryAbbreviation(tItemInfo.getItemCategory());
        // 获取当前年度项目数量，生成三位序号
        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setApplyYear(tItemInfo.getApplyYear());
        itemInfo.setStatus("1");
        Integer number = tItemInfoMapper.selectTItemInfoList(itemInfo).size();
        String formattedNumber = String.format("%03d", number);
        String itemNumber = "GS" + tItemInfo.getApplyYear() + abbreviation + formattedNumber;
        tItemInfo.setItemNumber(itemNumber);

        // 3. 生成申报单位名称
        // 支持多部门申报，部门ID用逗号分隔
        String applyUnitId = tItemInfo.getApplyUnitId();
        String[] ids = applyUnitId.split(",");
        String applyUnit = "";
        for (String id : ids) {
            SysDept sysDept = deptMapper.selectDeptById(Long.valueOf(id));
            applyUnit = applyUnit + sysDept.getDeptName() + ",";
        }
        tItemInfo.setApplyUnit(applyUnit);

        // 4. 保存项目基本信息
        int row = tItemInfoMapper.insertTItemInfo(tItemInfo);

        // 5. 创建项目审核流程记录
        // 5.1 学院老师审核节点（自动通过）
        addItemAudit(tItemInfo, user, dept, "自动通过");

        // 5.2 学院院长/副院长审核节点
        SysDept collegeDept = deptMapper.selectDeptById(Long.valueOf(tItemInfo.getApplyUnitId()));
        List<SysUser> deanUsers = userMapper.selectUserByDeptIdAndRole(Long.valueOf(tItemInfo.getApplyUnitId()), 102L);
        if (deanUsers != null && !deanUsers.isEmpty()) {
            String nickNames = deanUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + collegeDept.getDeptName() + "领导审核";
            addItemAudit(tItemInfo.getId(), nickNames, collegeDept.getDeptName(), reason, 2);
        }

        // 5.3 归口单位经办人审核节点
        SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
        List<SysUser> sysUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 104L);
        if (sysUsers != null && !sysUsers.isEmpty()) {
            String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + focalDept.getDeptName() + "经办人审核";
            addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 3);
        }

        // 5.4 归口单位领导审核节点
        List<SysUser> leader = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 105L);
        if (leader != null && !leader.isEmpty()) {
            String nickNames = leader.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + focalDept.getDeptName() + "领导审核";
            addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 4);
        }

        // 5.5 国资处经办人审核节点
        SysDept manageDept = deptMapper.selectDeptById(213L);
        List<SysUser> manageUsers = userMapper.selectUserByDeptIdAndRole(213L, 106L);
        if (manageUsers != null && !manageUsers.isEmpty()) {
            String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + manageDept.getDeptName() + "经办人审核";
            addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 5);
        }

        // 5.6 学校评审和工信部评审节点（不创建审核记录）

        // 5.7 V1合约规划相关节点
        // 5.7.1 V1合约规划填报节点
        addItemAudit(tItemInfo.getId(), user.getNickName(), dept.getDeptName(), "V1合约规划填报", 8);

        // 5.7.2 归口单位经办人审核V1合约规划
        if (sysUsers != null && !sysUsers.isEmpty()) {
            String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + focalDept.getDeptName() + "经办人审核";
            addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 9);
        }

        // 5.7.3 国资处经办人审核V1合约规划
        if (manageUsers != null && !manageUsers.isEmpty()) {
            String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + manageDept.getDeptName() + "经办人审核";
            addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 10);
        }

        // 5.8 项目执行节点（不创建审核记录）

        // 5.9 V2合约规划相关节点
        // 5.9.1 V2合约规划填报节点
        addItemAudit(tItemInfo.getId(), user.getNickName(), dept.getDeptName(), "V2合约规划填报", 12);

        // 5.9.2 归口单位经办人审核V2合约规划
        if (sysUsers != null && !sysUsers.isEmpty()) {
            String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + focalDept.getDeptName() + "经办人审核";
            addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 13);
        }

        // 5.9.3 国资处经办人审核V2合约规划
        if (manageUsers != null && !manageUsers.isEmpty()) {
            String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
            String reason = "待" + manageDept.getDeptName() + "经办人审核";
            addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 14);
        }

        // 6. 创建其他相关信息
        // 包括合约规划、绩效信息、里程碑等
        addOrUpdateOtherInfo(tItemInfo);

        return tItemInfo.getId();
    }

    /**
     * 新增项目信息Two
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    @Override
    public Long insertTItemInfoTwo(TItemInfo tItemInfo) {
        // 1. 获取当前用户和部门信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        tItemInfo.setCreateTime(DateUtils.getNowDate());
        tItemInfo.setCreateById(user.getUserId());

        // 2. 生成项目编号
        // 编号规则：GS + 申报年度 + 项目类别简写 + 三位序号
        // 项目类别简写：房屋修缮(FX)、基础设施改造(JC)、设备资料购置(SB)
        String abbreviation = getItemCategoryAbbreviation(tItemInfo.getItemCategory());
        // 获取当前年度项目数量，生成三位序号
        TItemInfo itemInfo = new TItemInfo();
        itemInfo.setApplyYear(tItemInfo.getApplyYear());
        itemInfo.setStatus("1");
        Integer number = tItemInfoMapper.selectTItemInfoList(itemInfo).size();
        String formattedNumber = String.format("%03d", number);
        String itemNumber = "GS" + tItemInfo.getApplyYear() + abbreviation + formattedNumber;
        tItemInfo.setItemNumber(itemNumber);

        // 3. 生成申报单位名称
        // 支持多部门申报，部门ID用逗号分隔
        String applyUnitId = tItemInfo.getApplyUnitId();
        String[] ids = applyUnitId.split(",");
        String applyUnit = Arrays.stream(ids).map(id -> deptMapper.selectDeptById(Long.valueOf(id))).filter(Objects::nonNull).map(SysDept::getDeptName).collect(Collectors.joining(","));
        tItemInfo.setApplyUnit(applyUnit);

        // 设置归口单位名称
        SysDept itemDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
        tItemInfo.setFocalUnit(itemDept.getDeptName());

        // 4. 保存项目基本信息
        int row = tItemInfoMapper.insertTItemInfo(tItemInfo);


        if (ids.length > 1) { // 如果有多个申报部门，即联合申报

            // 5. 创建项目审核流程记录
            // 5.1 设置 多学院 - 老师审批节点
            addChildOneAudit(tItemInfo, user, applyUnit, ids);

            // 5.2 设置 多学院 - 学院院长/副院长审核节点
            addChildTwoAudit(tItemInfo, applyUnit);

            // 5.3 归口单位领导专业审核
            SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
            List<SysUser> ldUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 105L);
            if (ldUsers != null && !ldUsers.isEmpty()) {
                String nickNames = ldUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + focalDept.getDeptName() + "领导审核";
                addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 3);
            }

            // 5.4 归口单位经办人专业审核
            List<SysUser> sysUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 104L);
            if (sysUsers != null && !sysUsers.isEmpty()) {
                String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "专业审核进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 4);
            }

            SysDept manageDept = deptMapper.selectDeptById(1207L);
            List<SysUser> manageUsers = userMapper.selectUserByDeptIdAndRole(1207L, 106L);
            if (manageUsers != null && !manageUsers.isEmpty()) {
                // 5.5 国资处经办人综合评审
                String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "综合评审进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 5);

                // 5.6 国资处经办人入库评审
                String reasonFive = "入库评审进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reasonFive, 6);

                // 5.7 国资处经办人执行计划排序
                String reasonSix = "执行计划排序进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reasonSix, 7);
            }

            // 5.8 项目执行节点（不创建审核记录）

            // 5.9 合约规划填报节点
            addItemAudit(tItemInfo.getId(), user.getNickName(), dept.getDeptName(), "合约规划填报", 9);

            // 5.10 归口单位经办人审核合约规划
            if (sysUsers != null && !sysUsers.isEmpty()) {
                String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + focalDept.getDeptName() + "经办人审核";
                addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 10);
            }

            // 5.11 国资处经办人审核合约规划
            if (manageUsers != null && !manageUsers.isEmpty()) {
                String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + manageDept.getDeptName() + "经办人审核";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 11);
            }

        } else { // 单部门申报

            // 5. 创建项目审核流程记录
            // 5.1 学院老师审核节点（自动通过）
            addItemAudit(tItemInfo, user, dept, "自动通过");

            // 5.2 学院院长/副院长审核节点
            SysDept collegeDept = deptMapper.selectDeptById(Long.valueOf(tItemInfo.getApplyUnitId()));
            List<SysUser> deanUsers = userMapper.selectUserByDeptIdAndRole(Long.valueOf(tItemInfo.getApplyUnitId()), 102L);
            if (deanUsers != null && !deanUsers.isEmpty()) {
                String nickNames = deanUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + collegeDept.getDeptName() + "领导审核";
                addItemAudit(tItemInfo.getId(), nickNames, collegeDept.getDeptName(), reason, 2);
            }

            // 5.3 归口单位领导专业审核
            SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
            List<SysUser> ldUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 105L);
            if (ldUsers != null && !ldUsers.isEmpty()) {
                String nickNames = ldUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + focalDept.getDeptName() + "领导审核";
                addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 3);
            }

            // 5.4 归口单位经办人专业审核
            List<SysUser> sysUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 104L);
            if (sysUsers != null && !sysUsers.isEmpty()) {
                String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "专业审核进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 4);
            }

            SysDept manageDept = deptMapper.selectDeptById(1207L);
            List<SysUser> manageUsers = userMapper.selectUserByDeptIdAndRole(1207L, 106L);
            if (manageUsers != null && !manageUsers.isEmpty()) {
                // 5.5 国资处经办人综合评审
                String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "综合评审进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 5);

                // 5.6 国资处经办人入库评审
                String reasonFive = "入库评审进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reasonFive, 6);

                // 5.7 国资处经办人执行计划排序
                String reasonSix = "执行计划排序进行中...";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reasonSix, 7);
            }

            // 5.8 项目执行节点（不创建审核记录）

            // 5.9 合约规划填报节点
            addItemAudit(tItemInfo.getId(), user.getNickName(), dept.getDeptName(), "合约规划填报", 9);

            // 5.10 归口单位经办人审核合约规划
            if (sysUsers != null && !sysUsers.isEmpty()) {
                String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + focalDept.getDeptName() + "经办人审核";
                addItemAudit(tItemInfo.getId(), nickNames, focalDept.getDeptName(), reason, 10);
            }

            // 5.11 国资处经办人审核合约规划
            if (manageUsers != null && !manageUsers.isEmpty()) {
                String nickNames = manageUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                String reason = "待" + manageDept.getDeptName() + "经办人审核";
                addItemAudit(tItemInfo.getId(), nickNames, manageDept.getDeptName(), reason, 11);
            }
        }

        // 6. 创建其他相关信息
        // 包括合约规划、绩效信息、里程碑等
        addOrUpdateOtherInfo(tItemInfo);

        return tItemInfo.getId();
    }

    /**
     * 新增 多学院 - flow为 1 的老师们的审核节点
     */
    private void addChildOneAudit(final TItemInfo tItemInfo, final SysUser user, String allDeptNames, String[] applyUnitIds) {
        // 过滤掉当前用户的部门ID,获取除当前用户申报部门之外的申报部门名称
        // 获取当前用户部门ID
        Long currentDeptId = user.getDeptId();
        String otherApplyUnits = Arrays.stream(applyUnitIds).map(Long::valueOf).filter(id -> !id.equals(currentDeptId)).map(id -> {
            SysDept deptObj = deptMapper.selectDeptById(id);
            return deptObj != null ? deptObj.getDeptName() : null;
        }).filter(Objects::nonNull).collect(Collectors.joining("、"));

        TItemAudit tItemAudit = new TItemAudit();
        tItemAudit.setItemId(tItemInfo.getId());
        tItemAudit.setFlow(1);
        tItemAudit.setAuditStatus("3");
        tItemAudit.setAuditBy(user.getNickName() + "、" + otherApplyUnits + "老师");
        tItemAudit.setAuditDeptName(allDeptNames);
        tItemAudit.setReason(user.getNickName() + "自动通过，待" + otherApplyUnits + "老师审核");
        tItemAudit.setCreateTime(new Date());
        auditMapper.insertTItemAudit(tItemAudit);

        Arrays.stream(otherApplyUnits.split("、")).map(String::trim).forEach(dept -> {
            // 处理每个部门
            TItemAuditDetail tAuditDetail = new TItemAuditDetail();
            tAuditDetail.setItemId(tItemInfo.getId());
            tAuditDetail.setNodeId(tItemAudit.getId());
            tAuditDetail.setFlow(1);
            tAuditDetail.setAuditStatus("3");
            tAuditDetail.setReason("待" + dept + "老师审核");
            tAuditDetail.setAuditDeptName(dept);
            tAuditDetail.setCreateTime(new Date());
            tAuditDetail.setStatus("1");
            auditDetailMapper.insertTItemAuditDetail(tAuditDetail);
        });
    }

    /**
     * 新增 多学院 - flow为 2 的领导们的审核节点
     */
    private void addChildTwoAudit(final TItemInfo tItemInfo, String allDeptNames) {

        TItemAudit tItemAudit = new TItemAudit();
        tItemAudit.setItemId(tItemInfo.getId());
        tItemAudit.setFlow(2);
        tItemAudit.setAuditStatus("3");
        tItemAudit.setAuditBy(allDeptNames + "领导");
        tItemAudit.setAuditDeptName(allDeptNames);
        tItemAudit.setReason("待" + allDeptNames + "领导审核");
        tItemAudit.setCreateTime(new Date());
        auditMapper.insertTItemAudit(tItemAudit);

        Arrays.stream(allDeptNames.split(",")).map(String::trim).forEach(dept -> {
            // 处理每个部门
            TItemAuditDetail tAuditDetail = new TItemAuditDetail();
            tAuditDetail.setItemId(tItemInfo.getId());
            tAuditDetail.setNodeId(tItemAudit.getId());
            tAuditDetail.setFlow(2);
            tAuditDetail.setAuditStatus("3");
            tAuditDetail.setReason("待" + dept + "领导审核");
            tAuditDetail.setAuditDeptName(dept);
            tAuditDetail.setCreateTime(new Date());
            tAuditDetail.setStatus("1");
            auditDetailMapper.insertTItemAuditDetail(tAuditDetail);
        });
    }

    /**
     * 新增合约规划、绩效信息、里程碑等信息
     *
     * @param tItemInfo
     */
    private void addOrUpdateOtherInfo(final TItemInfo tItemInfo) {
        //添加合约规划V1
        List<ContractPlan> contractPlanList = tItemInfo.getContractPlanList();
        if (contractPlanList != null && !contractPlanList.isEmpty()) {
            // 添加实际的逻辑处理
            for (ContractPlan contractPlan : contractPlanList) {
                if (contractPlan.getId() == null) {
                    contractPlan.setItemId(tItemInfo.getId());
                    contractPlan.setContractType("V1");
                    contractPlanMapper.insertContractPlan(contractPlan);
                } else {
                    contractPlanMapper.updateContractPlan(contractPlan);
                }
            }
        }
        //添加合约规划V2
        List<ContractPlan> contractPlanListV2 = tItemInfo.getContractPlanListV2();
        if (contractPlanListV2 != null && !contractPlanListV2.isEmpty()) {
            // 添加实际的逻辑处理
            for (ContractPlan contractPlan : contractPlanListV2) {
                if (contractPlan.getId() == null) {
                    contractPlan.setItemId(tItemInfo.getId());
                    contractPlan.setContractType("V2");
                    contractPlanMapper.insertContractPlan(contractPlan);
                } else {
                    contractPlanMapper.updateContractPlan(contractPlan);
                }
            }
        }
        //添加绩效信息
        List<Performance> performanceList = tItemInfo.getPerformanceList();
        if (performanceList != null && !performanceList.isEmpty()) {
            // 添加实际的逻辑处理
            for (Performance performance : performanceList) {
                if (performance.getId() == null) {
                    performance.setItemId(tItemInfo.getId());
                    performanceMapper.insertPerformance(performance);
                } else {
                    performanceMapper.updatePerformance(performance);
                }
            }
        }
        //添加里程碑信息
        List<MilestonPlan> milestonPlanList = tItemInfo.getMilestonPlanList();
        if (milestonPlanList != null && !milestonPlanList.isEmpty()) {
            // 添加实际的逻辑处理
            for (MilestonPlan milestonPlan : milestonPlanList) {
                if (milestonPlan.getId() == null) {
                    milestonPlan.setItemId(tItemInfo.getId());
                    milestonPlan.setTemplateId(null);
                    milestonPlanService.insertMilestonPlan(milestonPlan);
                } else {
                    milestonPlanService.updateMilestonPlan(milestonPlan);
                }
            }
        }
    }

    /**
     * 新增审核记录
     *
     * @param tItemInfo
     * @param user
     * @param dept
     */
    private void addItemAudit(final TItemInfo tItemInfo, final SysUser user, final SysDept dept, String reason) {
        TItemAudit tItemAudit = new TItemAudit();
        tItemAudit.setItemId(tItemInfo.getId());
        tItemAudit.setFlow(1);
        // status等于2表示存草稿，如果存草稿，则老师审批状态应该不为1
        if (tItemInfo.getStatus() != null && tItemInfo.getStatus().equals("2")) {
            tItemAudit.setAuditStatus("3");
        } else {
            tItemAudit.setAuditStatus("1");
        }
        tItemAudit.setAuditBy(user.getNickName());
        tItemAudit.setAuditDeptName(dept.getDeptName());
        tItemAudit.setReason(reason);
        tItemAudit.setCreateTime(new Date());
        auditMapper.insertTItemAudit(tItemAudit);
    }

    /**
     * 新增审核记录
     *
     * @param itemId        项目id
     * @param nickNames     审核人姓名
     * @param auditDeptName 审核节点部门名称
     * @param reason        审核意见
     * @param flow          审核节点
     */
    private void addItemAudit(final Long itemId, String nickNames, String auditDeptName, String reason, Integer flow) {
        TItemAudit tItemAudit = new TItemAudit();
        tItemAudit.setAuditBy(nickNames);
        tItemAudit.setAuditDeptName(auditDeptName);
        tItemAudit.setAuditStatus("3");
        tItemAudit.setReason(reason);
        tItemAudit.setFlow(flow);
        tItemAudit.setItemId(itemId);
        tItemAudit.setCreateTime(new Date());
        auditMapper.insertTItemAudit(tItemAudit);
    }

    /**
     * 修改项目信息
     *
     * @param tItemInfo 项目信息
     * @return 结果
     */
    @Override
    public int updateTItemInfo(TItemInfo tItemInfo) {
        SysUser user = SecurityUtils.getLoginUser().getUser();

        TItemInfo info = tItemInfoMapper.selectTItemInfoById(tItemInfo.getId());

        //*/ 当项目节点为1，即学院领导未审批时，可以更新项目信息，并且更新相应的审核状态数据
        if (info.getFlow() != null && info.getFlow() == 1) {
            //*/ 获取项目审核列表
            TItemAudit tItemAudit1 = new TItemAudit();
            tItemAudit1.setItemId(tItemInfo.getId());
            List<TItemAudit> list = auditMapper.selectTItemAuditList(tItemAudit1);
            //*/
            //*/更新审核记录
            for (TItemAudit itemAudit : list) {
                if (itemAudit.getFlow() == 2) {
                    // 2:更新学院院长或副院长审核记录
                    SysDept collegeDept = deptMapper.selectDeptById(Long.valueOf(tItemInfo.getApplyUnitId()));
                    List<SysUser> deanUsers = userMapper.selectUserByDeptIdAndRole(Long.valueOf(tItemInfo.getApplyUnitId()), 102L);
                    if (deanUsers != null && !deanUsers.isEmpty()) {
                        String nickNames = deanUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                        String reason = "待" + collegeDept.getDeptName() + "领导审核";
                        itemAudit.setAuditBy(nickNames);
                        itemAudit.setAuditDeptName(collegeDept.getDeptName());
                        itemAudit.setReason(reason);
                        auditMapper.updateTItemAudit(itemAudit);
                    }
                } else if (itemAudit.getFlow() == 3) {
                    // 3:更新归口单位经办人审核记录
                    SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
                    List<SysUser> sysUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 104L);
                    if (sysUsers != null && !sysUsers.isEmpty()) {
                        String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                        String reason = "待" + focalDept.getDeptName() + "经办人审核";
                        itemAudit.setAuditBy(nickNames);
                        itemAudit.setAuditDeptName(focalDept.getDeptName());
                        itemAudit.setReason(reason);
                        auditMapper.updateTItemAudit(itemAudit);
                    }
                } else if (itemAudit.getFlow() == 4) {
                    // 4:更新归口单位领导审核记录
                    SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
                    List<SysUser> leader = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 105L);
                    if (leader != null && !leader.isEmpty()) {
                        String nickNames = leader.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                        String reason = "待" + focalDept.getDeptName() + "领导审核";
                        itemAudit.setAuditBy(nickNames);
                        itemAudit.setAuditDeptName(focalDept.getDeptName());
                        itemAudit.setReason(reason);
                        auditMapper.updateTItemAudit(itemAudit);
                    }
                } else if (itemAudit.getFlow() == 9) {
                    // 9:更新归口单位经办人审核V1合约规划记录
                    SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
                    List<SysUser> sysUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 104L);
                    if (sysUsers != null && !sysUsers.isEmpty()) {
                        String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                        String reason = "待" + focalDept.getDeptName() + "经办人审核";
                        itemAudit.setAuditBy(nickNames);
                        itemAudit.setAuditDeptName(focalDept.getDeptName());
                        itemAudit.setReason(reason);
                        auditMapper.updateTItemAudit(itemAudit);
                    }
                } else if (itemAudit.getFlow() == 13) {
                    // 13:更新归口单位经办人审核V2合约规划记录
                    SysDept focalDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
                    List<SysUser> sysUsers = userMapper.selectUserByDeptIdAndRole(tItemInfo.getFocalUnitId(), 104L);
                    if (sysUsers != null && !sysUsers.isEmpty()) {
                        String nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(","));
                        String reason = "待" + focalDept.getDeptName() + "经办人审核";
                        itemAudit.setAuditBy(nickNames);
                        itemAudit.setAuditDeptName(focalDept.getDeptName());
                        itemAudit.setReason(reason);
                        auditMapper.updateTItemAudit(itemAudit);
                    }
                }
                //*/更新审核记录
            }
        }
        //*/ 当项目节点为1，即学院领导未审批时，可以更新项目信息，并且更新相应的审核状态数据

        if (user.getUserId() == info.getCreateById() && info.getFlow() != null && info.getFlow() == 0) {
            // 当status不等于2，即不为存草稿状态时，将项目节点flow设置为1
            if (!tItemInfo.getStatus().equals("2")) {
                tItemInfo.setFlow(1);
            }
            //*/ 获取项目审核列表
            TItemAudit tItemAudit1 = new TItemAudit();
            tItemAudit1.setItemId(tItemInfo.getId());
            List<TItemAudit> list = auditMapper.selectTItemAuditList(tItemAudit1);
            //*/
            //*/ 修改审核信息
            for (TItemAudit itemAudit : list) {
                //*/ 只修改初始节点即可
                if (itemAudit != null && itemAudit.getFlow() != null && itemAudit.getFlow() == 1) {
                    itemAudit.setAuditStatus("1");
                    auditMapper.updateTItemAudit(itemAudit);
                }
            }
        }
        tItemInfo.setUpdateTime(DateUtils.getNowDate());
        //新增合约规划、绩效信息、里程碑等信息
        addOrUpdateOtherInfo(tItemInfo);
        return tItemInfoMapper.updateTItemInfo(tItemInfo);
    }

    /**
     * 修改项目信息Two（第二种更新方法）
     *
     * @param tItemInfo 项目信息对象，包含需要更新的项目数据
     * @return 更新影响的记录数
     */
    @Override
    public int updateTItemInfoTwo(TItemInfo tItemInfo) {
        // 获取当前登录用户信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 查询数据库中原始项目信息
        TItemInfo info = tItemInfoMapper.selectTItemInfoById(tItemInfo.getId());
        System.out.println("Condition matched: user.getUserId()=" + user.getUserId() +
                ", info.getCreateById()=" + info.getCreateById() +
                ", info.getFlow()=" + info.getFlow());
        // 处理项目初始状态：当当前用户是项目创建者且项目处于初始节点(flow=0)时的特殊处理
        if (user.getUserId().equals(info.getCreateById()) && info.getFlow() != null && info.getFlow() == 0) {

            // 获取项目申报单位的数量
            String applyUnitId = tItemInfo.getApplyUnitId();
            String[] ids = applyUnitId.split(",");

            if (ids.length > 1) { // 如果有多个申报部门，即联合申报
                tItemInfo.setFlow(1);

                // 获取当前用户部门ID
                Long currentDeptId = user.getDeptId();
                // 获取当前用户部门名称之外的其他申报单位名称
                String otherApplyUnits = Arrays.stream(ids).map(Long::valueOf).filter(id -> !id.equals(currentDeptId)).map(id -> {
                    SysDept deptObj = deptMapper.selectDeptById(id);
                    return deptObj != null ? deptObj.getDeptName() : null;
                }).filter(Objects::nonNull).collect(Collectors.joining("、"));

                // 获取项目所有审核记录
                TItemAudit tItemAudit1 = new TItemAudit();
                tItemAudit1.setItemId(tItemInfo.getId());
                List<TItemAudit> list = auditMapper.selectTItemAuditList(tItemAudit1);

                // 只更新初始节点(flow=1)的审核状态为已通过(1)
                list.stream().filter(itemAudit -> Objects.equals(itemAudit.getFlow(), 1)).findFirst().ifPresent(itemAudit -> {
                    itemAudit.setAuditBy(user.getNickName() + "、" + otherApplyUnits + "老师");
                    itemAudit.setReason(user.getNickName() + "已重新发起审批,等待" + otherApplyUnits + "老师审核");  // 设置审核意见
                    itemAudit.setCreateTime(DateUtils.getNowDate()); // 设置审核时间
                    auditMapper.updateTItemAudit(itemAudit);
                });

            } else {
                // 如果不是草稿状态(status!=2)，则将项目流程节点设置为1（推进到下一流程）
                if (!tItemInfo.getStatus().equals("2")) {
                    tItemInfo.setFlow(1);
                }

                // 获取项目所有审核记录
                TItemAudit tItemAudit1 = new TItemAudit();
                tItemAudit1.setItemId(tItemInfo.getId());
                List<TItemAudit> list = auditMapper.selectTItemAuditList(tItemAudit1);

                // 只更新初始节点(flow=1)的审核状态为已通过(1)
                list.stream().filter(itemAudit -> Objects.equals(itemAudit.getFlow(), 1)).findFirst().ifPresent(itemAudit -> {
                    itemAudit.setAuditStatus("1");  // 设置为已通过状态
                    itemAudit.setReason("已重新发起审批");  // 设置审核意见
                    SysDept dept = user.getDept();
                    itemAudit.setAuditBy(user.getNickName());  // 设置审核人名称
                    itemAudit.setAuditDeptName(dept.getDeptName()); // 设置审核人的审核部门名称
                    itemAudit.setCreateTime(DateUtils.getNowDate()); // 设置审核时间
                    auditMapper.updateTItemAudit(itemAudit);
                });
            }
        }

        // 更新项目信息的更新时间为当前时间
        tItemInfo.setUpdateTime(DateUtils.getNowDate());
        // 匹配部门名称
        SysDept itemDept = deptMapper.selectDeptById(tItemInfo.getFocalUnitId());
        tItemInfo.setFocalUnit(itemDept.getDeptName());

        // 处理项目相关信息：新增或更新合约规划、绩效信息、里程碑等关联数据
        addOrUpdateOtherInfo(tItemInfo);

        // 更新项目基本信息并返回影响的记录数
        return tItemInfoMapper.updateTItemInfo(tItemInfo);
    }

    /**
     * 批量删除项目信息
     *
     * @param ids 需要删除的项目信息主键
     * @return 结果
     */
    @Override
    public int deleteTItemInfoByIds(Long[] ids) {
        return tItemInfoMapper.deleteTItemInfoByIds(ids);
    }

    /**
     * 删除项目信息信息
     *
     * @param id 项目信息主键
     * @return 结果
     */
    @Override
    public int deleteTItemInfoById(Long id) {
        return tItemInfoMapper.deleteTItemInfoById(id);
    }


    /**
     * 导入项目信息
     *
     * @param itemInfoList    项目信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importItemInfo(List<TItemInfo> itemInfoList, Boolean isUpdateSupport) {
        if (StringUtils.isNull(itemInfoList) || itemInfoList.size() == 0) {
            throw new ServiceException("导入项目信息不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (TItemInfo itemInfo : itemInfoList) {
            try {
                // 验证是否存在该项目信息
                TItemInfo info = tItemInfoMapper.selectTItemInfoByItemName(itemInfo.getItemName());
                // 工具方法调用，用于判断变量 info 是否为 null 或者其字符串形式是否为空（""）或空白字符（如空格、制毛等）
                if (StringUtils.isNull(info)) {
                    tItemInfoMapper.insertTItemInfo(itemInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、项目 " + itemInfo.getItemName() + " 导入成功");
                } else if (isUpdateSupport) {
                    //*/更新项目信息表需要有数据id
                    itemInfo.setId(info.getId());
                    //*/
                    tItemInfoMapper.updateTItemInfo(itemInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、项目 " + itemInfo.getItemName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、项目 " + itemInfo.getItemName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目 " + itemInfo.getItemName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 处理专业审核信息
     *
     * @param reviewVO 专业审核信息
     * @return 结果
     */
    @Override
    public void processProfessionalReview(ProfessionalReviewVO<SortItemVO> reviewVO) {
        try {
            if (reviewVO.getSortList() != null && !reviewVO.getSortList().isEmpty()) {
                List<SortItemVO> list = reviewVO.getSortList();
                for (SortItemVO item : list) {

                    // 1. 获取项目基本信息
                    // 根据项目ID获取项目信息，包含当前流程节点等信息
                    TItemInfo tItemInfo = tItemInfoMapper.selectTItemInfoById(item.getId());

                    // 2. 获取当前项目流程节点
                    Integer flow = tItemInfo.getFlow();

                    // 3. 获取项目所有审核记录
                    // 查询该项目所有的审核记录，用于后续批量更新
                    TItemAudit tItemAudit1 = new TItemAudit();
                    tItemAudit1.setItemId(item.getId());
                    List<TItemAudit> elList = auditMapper.selectTItemAuditList(tItemAudit1);

                    // 4. 推往下一个流程节点
                    flow = flow + 1;
                    tItemInfo.setFlow(flow);
                    tItemInfoMapper.updateTItemInfo(tItemInfo);


                    // 5. 更新当前流程节点的审核记录
                    for (TItemAudit itemAudit : elList) {
                        if (Objects.equals(itemAudit.getFlow(), flow)) {
                            itemAudit.setAuditStatus("1");  // 设置为已通过状态
                            itemAudit.setReason("专业审核通过");  // 设置审核意见
                            itemAudit.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                            SysUser user = SecurityUtils.getLoginUser().getUser();
                            SysDept dept = user.getDept();
                            itemAudit.setAuditBy(user.getNickName());  // 设置审核人名称
                            itemAudit.setAuditDeptName(dept.getDeptName()); // 设置审核人的审核部门名称
                            auditMapper.updateTItemAudit(itemAudit);
                        }
                    }

                    // 6. 设置项目ID 设置专业审核排序值 设置专业审核相关文件IDs
                    tItemInfoMapper.updateMajor(item.getId(), item.getSort(), reviewVO.getFileIds());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("处理专业审核信息失败：" + e.getMessage());
        }
    }

    /**
     * 处理综合评审信息
     *
     * @param reviewVO 综合评审信息
     * @return 结果
     */
    @Override
    public void compositeReview(ProfessionalReviewVO<CompositeVO> reviewVO) {
        try {
            if (reviewVO.getSortList() != null && !reviewVO.getSortList().isEmpty()) {
                List<CompositeVO> list = reviewVO.getSortList();
                for (CompositeVO item : list) {

                    // 1. 获取项目基本信息
                    // 根据项目ID获取项目信息，包含当前流程节点等信息
                    TItemInfo tItemInfo = tItemInfoMapper.selectTItemInfoById(item.getId());

                    // 2. 获取当前项目流程节点
                    Integer flow = tItemInfo.getFlow();

                    // 3. 获取项目所有审核记录
                    // 查询该项目所有的审核记录，用于后续批量更新
                    TItemAudit tItemAudit1 = new TItemAudit();
                    tItemAudit1.setItemId(item.getId());
                    List<TItemAudit> elList = auditMapper.selectTItemAuditList(tItemAudit1);

                    // 4. 推往下一个流程节点
                    flow = flow + 1;
                    tItemInfo.setFlow(flow);
                    tItemInfoMapper.updateTItemInfo(tItemInfo);


                    // 5. 更新当前流程节点的审核记录
                    for (TItemAudit itemAudit : elList) {
                        if (Objects.equals(itemAudit.getFlow(), flow)) {
                            itemAudit.setAuditStatus("1");  // 设置为已通过状态
                            itemAudit.setReason("综合评审通过");  // 设置审核意见
                            itemAudit.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                            SysUser user = SecurityUtils.getLoginUser().getUser();
                            SysDept dept = user.getDept();
                            itemAudit.setAuditBy(user.getNickName());  // 设置审核人名称
                            itemAudit.setAuditDeptName(dept.getDeptName()); // 设置审核人的审核部门名称
                            auditMapper.updateTItemAudit(itemAudit);
                        }
                    }

                    // 6. 设置项目ID 设置综合评审排序值 设置综合评审相关文件IDs
                    tItemInfoMapper.updateCompo(item.getId(), item.getSort(), reviewVO.getFileIds());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("处理综合评审信息失败：" + e.getMessage());
        }
    }

    /**
     * 处理入库评审信息
     *
     * @param reviewVO 入库评审信息
     * @return 结果
     */
    @Override
    public void storeReview(ProfessionalReviewVO<StoreVO> reviewVO) {
        try {
            if (reviewVO.getSortList() != null && !reviewVO.getSortList().isEmpty()) {
                List<StoreVO> list = reviewVO.getSortList();
                for (StoreVO item : list) {

                    // 1. 获取项目基本信息
                    // 根据项目ID获取项目信息，包含当前流程节点等信息
                    TItemInfo tItemInfo = tItemInfoMapper.selectTItemInfoById(item.getId());

                    // 2. 获取当前项目流程节点
                    Integer flow = tItemInfo.getFlow();

                    // 3. 获取项目所有审核记录
                    // 查询该项目所有的审核记录，用于后续批量更新
                    TItemAudit tItemAudit1 = new TItemAudit();
                    tItemAudit1.setItemId(item.getId());
                    List<TItemAudit> elList = auditMapper.selectTItemAuditList(tItemAudit1);

                    // 4. 推往下一个流程节点
                    flow = flow + 1;
                    tItemInfo.setFlow(flow);
                    int currentYear = DateUtils.getNowDate().getYear() + 1900;
                    tItemInfo.setMinistryEnterYear(String.valueOf(currentYear));
                    tItemInfoMapper.updateTItemInfo(tItemInfo);

                    // 5. 更新当前流程节点的审核记录
                    Integer finalFlow = flow;
                    elList.stream().filter(itemAudit -> Objects.equals(itemAudit.getFlow(), finalFlow)).findFirst().ifPresent(itemAudit -> {
                        itemAudit.setAuditStatus("1");  // 设置为已通过状态
                        itemAudit.setReason("入库评审通过");  // 设置审核意见
                        itemAudit.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                        SysUser user = SecurityUtils.getLoginUser().getUser();
                        SysDept dept = user.getDept();
                        itemAudit.setAuditBy(user.getNickName());  // 设置审核人名称
                        itemAudit.setAuditDeptName(dept.getDeptName()); // 设置审核人的审核部门名称
                        auditMapper.updateTItemAudit(itemAudit);
                    });

                    // 6. 设置项目ID 设置综合评审排序值 设置综合评审相关文件IDs
                    tItemInfoMapper.updateStore(item.getId(), item.getStoreBudget(), item.getStoreOpinion(), reviewVO.getFileIds());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("处理入库评审信息失败：" + e.getMessage());
        }
    }

    /**
     * 处理执行计划排序信息
     *
     * @param reviewVO 执行计划排序信息
     * @return 结果
     */
    @Override
    public void planReview(ProfessionalReviewVO<PlanVO> reviewVO) {
        try {
            if (reviewVO.getSortList() != null && !reviewVO.getSortList().isEmpty()) {
                List<PlanVO> list = reviewVO.getSortList();
                for (PlanVO item : list) {

                    // 1. 获取项目基本信息
                    // 根据项目ID获取项目信息，包含当前流程节点等信息
                    TItemInfo tItemInfo = tItemInfoMapper.selectTItemInfoById(item.getId());

                    // 2. 获取当前项目流程节点
                    Integer flow = tItemInfo.getFlow();

                    // 3. 获取项目所有审核记录
                    // 查询该项目所有的审核记录，用于后续批量更新
                    TItemAudit tItemAudit1 = new TItemAudit();
                    tItemAudit1.setItemId(item.getId());
                    List<TItemAudit> elList = auditMapper.selectTItemAuditList(tItemAudit1);

                    // 4. 推往下一个流程节点
                    flow = flow + 1;
                    tItemInfo.setFlow(flow);
                    tItemInfoMapper.updateTItemInfo(tItemInfo);

                    // 5. 更新当前流程节点的审核记录
                    Integer finalFlow = flow;
                    elList.stream().filter(itemAudit -> Objects.equals(itemAudit.getFlow(), finalFlow)).findFirst().ifPresent(itemAudit -> {
                        itemAudit.setAuditStatus("1");  // 设置为已通过状态
                        itemAudit.setReason("执行计划排序通过");  // 设置审核意见
                        itemAudit.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                        SysUser user = SecurityUtils.getLoginUser().getUser();
                        SysDept dept = user.getDept();
                        itemAudit.setAuditBy(user.getNickName());  // 设置审核人名称
                        itemAudit.setAuditDeptName(dept.getDeptName()); // 设置审核人的审核部门名称
                        auditMapper.updateTItemAudit(itemAudit);
                    });

                    // 6. 设置项目ID 设置综合评审排序值 设置综合评审相关文件IDs
                    tItemInfoMapper.updatePlan(item.getId(), item.getStoreBudget(), item.getPlanSort(), reviewVO.getFileIds());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("处理执行计划排序信息失败：" + e.getMessage());
        }
    }
}
