package com.ruoyi.tiem.mapper;

import java.util.List;
import com.ruoyi.tiem.domain.MilestonPlan;

/**
 * 里程碑计划信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface MilestonPlanMapper 
{
    /**
     * 查询里程碑计划信息
     * 
     * @param id 里程碑计划信息主键
     * @return 里程碑计划信息
     */
    public MilestonPlan selectMilestonPlanById(Long id);

    /**
     * 查询里程碑计划信息列表
     * 
     * @param milestonPlan 里程碑计划信息
     * @return 里程碑计划信息集合
     */
    public List<MilestonPlan> selectMilestonPlanList(MilestonPlan milestonPlan);

    /**
     * 新增里程碑计划信息
     * 
     * @param milestonPlan 里程碑计划信息
     * @return 结果
     */
    public int insertMilestonPlan(MilestonPlan milestonPlan);

    /**
     * 修改里程碑计划信息
     * 
     * @param milestonPlan 里程碑计划信息
     * @return 结果
     */
    public int updateMilestonPlan(MilestonPlan milestonPlan);

    /**
     * 删除里程碑计划信息
     * 
     * @param id 里程碑计划信息主键
     * @return 结果
     */
    public int deleteMilestonPlanById(Long id);

    /**
     * 批量删除里程碑计划信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMilestonPlanByIds(Long[] ids);
}
