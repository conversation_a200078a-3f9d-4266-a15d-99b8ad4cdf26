package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TFoundingSource;
import com.ruoyi.tiem.mapper.TFoundingSourceMapper;
import com.ruoyi.tiem.service.ITFoundingSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目资金来源Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class TFoundingSourceServiceImpl implements ITFoundingSourceService
{
    @Autowired
    private TFoundingSourceMapper tFoundingSourceMapper;

    /**
     * 查询项目资金来源
     * 
     * @param id 项目资金来源主键
     * @return 项目资金来源
     */
    @Override
    public TFoundingSource selectTFoundingSourceById(Long id)
    {
        return tFoundingSourceMapper.selectTFoundingSourceById(id);
    }

    /**
     * 查询项目资金来源列表
     * 
     * @param tFoundingSource 项目资金来源
     * @return 项目资金来源
     */
    @Override
    public List<TFoundingSource> selectTFoundingSourceList(TFoundingSource tFoundingSource)
    {
        return tFoundingSourceMapper.selectTFoundingSourceList(tFoundingSource);
    }

    /**
     * 新增项目资金来源
     * 
     * @param tFoundingSource 项目资金来源
     * @return 结果
     */
    @Override
    public int insertTFoundingSource(TFoundingSource tFoundingSource)
    {
        return tFoundingSourceMapper.insertTFoundingSource(tFoundingSource);
    }

    /**
     * 修改项目资金来源
     * 
     * @param tFoundingSource 项目资金来源
     * @return 结果
     */
    @Override
    public int updateTFoundingSource(TFoundingSource tFoundingSource)
    {
        return tFoundingSourceMapper.updateTFoundingSource(tFoundingSource);
    }

    /**
     * 批量删除项目资金来源
     * 
     * @param ids 需要删除的项目资金来源主键
     * @return 结果
     */
    @Override
    public int deleteTFoundingSourceByIds(Long[] ids)
    {
        return tFoundingSourceMapper.deleteTFoundingSourceByIds(ids);
    }

    /**
     * 删除项目资金来源信息
     * 
     * @param id 项目资金来源主键
     * @return 结果
     */
    @Override
    public int deleteTFoundingSourceById(Long id)
    {
        return tFoundingSourceMapper.deleteTFoundingSourceById(id);
    }
}
