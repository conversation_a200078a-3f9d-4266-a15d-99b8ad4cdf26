package com.ruoyi.tiem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 多流程审核明细对象 t_item_audit_detail
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class TItemAuditDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    /** 审批节点 */
    @Excel(name = "审批节点")
    private Integer flow;

    /** 节点id */
    @Excel(name = "节点id")
    private Long nodeId;

    /** 审核状态 1：通过 2：拒绝 */
    @Excel(name = "审核状态 1：通过 2：拒绝")
    private String auditStatus;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reason;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 审核部门名称 */
    @Excel(name = "审核部门名称")
    private String auditDeptName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Integer getFlow()
    {
        return flow;
    }
    public void setFlow(Integer flow)
    {
        this.flow = flow;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setNodeId(Long nodeId) 
    {
        this.nodeId = nodeId;
    }

    public Long getNodeId() 
    {
        return nodeId;
    }
    public void setAuditStatus(String auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditBy(String auditBy) 
    {
        this.auditBy = auditBy;
    }

    public String getAuditBy() 
    {
        return auditBy;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setAuditDeptName(String auditDeptName) 
    {
        this.auditDeptName = auditDeptName;
    }

    public String getAuditDeptName() 
    {
        return auditDeptName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemId", getItemId())
            .append("flow", getFlow())
            .append("nodeId", getNodeId())
            .append("auditStatus", getAuditStatus())
            .append("auditBy", getAuditBy())
            .append("reason", getReason())
            .append("createTime", getCreateTime())
            .append("status", getStatus())
            .append("auditDeptName", getAuditDeptName())
            .toString();
    }
}
