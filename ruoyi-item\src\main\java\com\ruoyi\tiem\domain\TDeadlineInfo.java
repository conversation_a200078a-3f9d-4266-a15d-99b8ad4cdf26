package com.ruoyi.tiem.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目关门节点对象 t_deadline_info
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public class TDeadlineInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目类别 */
    @Excel(name = "项目类别")
    private String category;

    /** 里程碑计划截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "里程碑计划截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /** 年份 */
    @Excel(name = "年份")
    private String year;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setDeadline(Date deadline) 
    {
        this.deadline = deadline;
    }

    public Date getDeadline() 
    {
        return deadline;
    }
    public void setYear(String year) 
    {
        this.year = year;
    }

    public String getYear() 
    {
        return year;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("category", getCategory())
            .append("deadline", getDeadline())
            .append("year", getYear())
            .toString();
    }
}
