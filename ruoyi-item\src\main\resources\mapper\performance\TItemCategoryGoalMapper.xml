<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemCategoryGoalMapper">

    <resultMap type="TItemCategoryGoal" id="TItemCategoryGoalResult">
        <result property="id"    column="id"    />
        <result property="itemCategory"    column="item_category"    />
        <result property="executeYear"    column="execute_year"    />
        <result property="goal"    column="goal"    />
        <result property="actualCompletion"    column="actual_completion"    />
    </resultMap>

    <sql id="selectTItemCategoryGoalVo">
        select id, item_category, execute_year, goal, actual_completion from t_item_category_goal
    </sql>

    <select id="selectTItemCategoryGoalList" parameterType="TItemCategoryGoal" resultMap="TItemCategoryGoalResult">
        <include refid="selectTItemCategoryGoalVo"/>
        <where>
            <if test="itemCategory != null  and itemCategory != ''"> and item_category = #{itemCategory}</if>
            <if test="executeYear != null  and executeYear != ''"> and execute_year = #{executeYear}</if>
            <if test="goal != null  and goal != ''"> and goal = #{goal}</if>
            <if test="actualCompletion != null  and actualCompletion != ''"> and actual_completion = #{actualCompletion}</if>
        </where>
    </select>

    <select id="selectTItemCategoryGoalById" parameterType="Long" resultMap="TItemCategoryGoalResult">
        <include refid="selectTItemCategoryGoalVo"/>
        where id = #{id}
    </select>

    <insert id="insertTItemCategoryGoal" parameterType="TItemCategoryGoal" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_category_goal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemCategory != null">item_category,</if>
            <if test="executeYear != null">execute_year,</if>
            <if test="goal != null">goal,</if>
            <if test="actualCompletion != null">actual_completion,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemCategory != null">#{itemCategory},</if>
            <if test="executeYear != null">#{executeYear},</if>
            <if test="goal != null">#{goal},</if>
            <if test="actualCompletion != null">#{actualCompletion},</if>
         </trim>
    </insert>

    <update id="updateTItemCategoryGoal" parameterType="TItemCategoryGoal">
        update t_item_category_goal
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemCategory != null">item_category = #{itemCategory},</if>
            <if test="executeYear != null">execute_year = #{executeYear},</if>
            <if test="goal != null">goal = #{goal},</if>
            <if test="actualCompletion != null">actual_completion = #{actualCompletion},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemCategoryGoalById" parameterType="Long">
        update t_item_category_goal set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemCategoryGoalByIds" parameterType="String">
        update t_item_category_goal set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
