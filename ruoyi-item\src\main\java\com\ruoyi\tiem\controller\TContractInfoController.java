package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemInfo;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.TContractInfo;
import com.ruoyi.tiem.service.ITContractInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@RestController
@RequestMapping("/contract/info")
public class TContractInfoController extends BaseController
{
    @Autowired
    private ITContractInfoService tContractInfoService;

    @Autowired
    private TItemInfoMapper tItemInfoMapper;

    /**
     * 查询合同信息列表
     */
    @PreAuthorize("@ss.hasPermi('contract:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(TContractInfo tContractInfo)
    {

        startPage();
        List<TContractInfo> list = tContractInfoService.selectTContractInfoList(tContractInfo);
        // //查询合同
        // List<TItemInfo> itemInfoList = tItemInfoMapper.selectTItemInfoList(new TItemInfo());
        // for (TContractInfo contract:list) {
        //     String s = itemInfoList.stream().filter(itemInfo -> itemInfo.getId().equals(contract.getItemIds())).map(TItemInfo::getItemName).findFirst().orElse("");
        //     contract.setItemName(s);
        // }
        return getDataTable(list);
    }

    /**
     * 导出合同信息列表
     */
    @PreAuthorize("@ss.hasPermi('contract:info:export')")
    @Log(title = "合同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TContractInfo tContractInfo)
    {
        List<TContractInfo> list = tContractInfoService.selectTContractInfoList(tContractInfo);
        ExcelUtil<TContractInfo> util = new ExcelUtil<TContractInfo>(TContractInfo.class);
        util.exportExcel(response, list, "合同信息数据");
    }

    /**
     * 获取合同信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tContractInfoService.selectTContractInfoById(id));
    }

    /**
     * 新增合同信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:add')")
    @Log(title = "合同信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TContractInfo tContractInfo)
    {
        return toAjax(tContractInfoService.insertTContractInfo(tContractInfo));
    }

    /**
     * 修改合同信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:edit')")
    @Log(title = "合同信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TContractInfo tContractInfo)
    {
        return toAjax(tContractInfoService.updateTContractInfo(tContractInfo));
    }

    /**
     * 删除合同信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:remove')")
    @Log(title = "合同信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tContractInfoService.deleteTContractInfoByIds(ids));
    }
}
