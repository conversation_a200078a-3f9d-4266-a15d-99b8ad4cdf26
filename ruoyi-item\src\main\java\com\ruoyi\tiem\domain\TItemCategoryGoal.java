package com.ruoyi.tiem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目类别目标对象 t_item_category_goal
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public class TItemCategoryGoal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 项目类别 */
    @Excel(name = "项目类别")
    private String itemCategory;

    /** 执行年度 */
    @Excel(name = "执行年度")
    private String executeYear;

    /** 预期目标 */
    @Excel(name = "预期目标")
    private String goal;

    /** 实际完成情况 */
    @Excel(name = "实际完成情况")
    private String actualCompletion;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setItemCategory(String itemCategory) 
    {
        this.itemCategory = itemCategory;
    }

    public String getItemCategory() 
    {
        return itemCategory;
    }
    public void setExecuteYear(String executeYear) 
    {
        this.executeYear = executeYear;
    }

    public String getExecuteYear() 
    {
        return executeYear;
    }
    public void setGoal(String goal) 
    {
        this.goal = goal;
    }

    public String getGoal() 
    {
        return goal;
    }
    public void setActualCompletion(String actualCompletion) 
    {
        this.actualCompletion = actualCompletion;
    }

    public String getActualCompletion() 
    {
        return actualCompletion;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemCategory", getItemCategory())
            .append("executeYear", getExecuteYear())
            .append("goal", getGoal())
            .append("actualCompletion", getActualCompletion())
            .toString();
    }
}
