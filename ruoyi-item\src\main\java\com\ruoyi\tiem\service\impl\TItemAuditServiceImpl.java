package com.ruoyi.tiem.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.tiem.domain.TItemAuditDetail;
import com.ruoyi.tiem.domain.TItemInfo;
import com.ruoyi.tiem.domain.TItemRollback;
import com.ruoyi.tiem.mapper.TItemAuditDetailMapper;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import com.ruoyi.tiem.mapper.TItemRollbackMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.TItemAuditMapper;
import com.ruoyi.tiem.domain.TItemAudit;
import com.ruoyi.tiem.service.ITItemAuditService;
import com.ruoyi.common.core.domain.entity.SysRole;

/**
 * 项目审核信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class TItemAuditServiceImpl implements ITItemAuditService {
    @Autowired
    private TItemAuditMapper tItemAuditMapper;

    @Autowired
    private TItemAuditDetailMapper tItemAuditDetailMapper;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    @Autowired
    private TItemRollbackMapper itemRollbackMapper;

    /**
     * 查询项目审核信息
     *
     * @param id 项目审核信息主键
     * @return 项目审核信息
     */
    @Override
    public TItemAudit selectTItemAuditById(Long id) {
        return tItemAuditMapper.selectTItemAuditById(id);
    }

    /**
     * 查询项目审核信息
     *
     * @param id 项目审核信息主键
     * @return 项目审核记录列表
     */
    @Override
    public List<Map<String, Object>> handleRecord(Long id) {
        // 先根据ID查询项目信息
        TItemInfo itemInfo = itemInfoMapper.selectTItemInfoById(id);
        String[] isMulti = itemInfo.getApplyUnit() != null ? itemInfo.getApplyUnit().split(",") : new String[0];
        List<Map<String, Object>> resultList = new ArrayList<>();

        if (isMulti.length > 1) {
            // 多部门审批的情况
            // 先直接查询 t_item_audit_detail 表中该项目的所有子级通过记录 auditDetailRecord
            List<TItemAuditDetail> auditDetailList = tItemAuditDetailMapper.selectDetailApprovalRecord(id);
            List<Map<String, Object>> auditDetailRecord = auditDetailList.stream().map(detail -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", detail.getId());
                map.put("itemId", detail.getItemId());
                map.put("nodeId", detail.getNodeId());
                map.put("flow", detail.getFlow());
                map.put("auditStatus", detail.getAuditStatus());
                map.put("auditBy", detail.getAuditBy());
                map.put("reason", detail.getReason());
                map.put("createTime", detail.getCreateTime());
                map.put("auditDeptName", detail.getAuditDeptName());
                map.put("recordType", "child");  // 标记类型为 child
                return map;
            }).collect(Collectors.toList());

            // 再查询 t_item_rollback 表中是否有该项目的退回记录 rollbackRecord
            TItemRollback tItemRollback = new TItemRollback();
            tItemRollback.setItemId(id);
            List<TItemRollback> rollbackList = itemRollbackMapper.selectTItemRollbackList(tItemRollback);
            List<Map<String, Object>> rollbackRecord = rollbackList.stream().map(detail -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", detail.getId());
                map.put("itemId", detail.getItemId());
                map.put("flow", detail.getFlow());
                map.put("auditBy", detail.getPerson());
                map.put("reason", detail.getReason());
                map.put("createTime", detail.getTime());
                map.put("auditDeptName", detail.getAuditDeptName());
                map.put("recordType", "back");
                return map;
            }).collect(Collectors.toList());

            // 情况1：如果找不到该项目的退回记录 rollbackRecord 也找不到该项目的子级通过记录 auditDetailRecord 则说明该项目还未开始审核流程，返回空数组
            if (auditDetailRecord.isEmpty() && rollbackRecord.isEmpty()) {
                return resultList;
            }

            // 如果有该项目的子级通过记录
            if (!auditDetailRecord.isEmpty()) {
                // 查询该项目的父级通过记录 auditRecord
                List<TItemAudit> auditList = tItemAuditMapper.selectApprovalRecord(id);
                List<Map<String, Object>> auditRecord = auditList.stream().map(detail -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", detail.getId());
                    map.put("itemId", detail.getItemId());
                    map.put("flow", detail.getFlow());
                    map.put("auditStatus", detail.getAuditStatus());
                    map.put("auditBy", detail.getAuditBy());
                    map.put("reason", detail.getReason());
                    map.put("createTime", detail.getCreateTime());
                    map.put("auditDeptName", detail.getAuditDeptName());
                    map.put("recordType", "parent");  // 标记类型为 parent
                    // 如果flow等于4，则添加专业审核排序
                    if (detail.getFlow() == 4) {
                        map.put("majorSort", itemInfo.getMajorSort());
                    }
                    // 如果flow等于5，则添加综合评审意见
                    if (detail.getFlow() == 5) {
                        map.put("compoSort", itemInfo.getCompoSort());
                    }
                    // 如果flow等于6，则添加入库评审意见
                    if (detail.getFlow() == 6) {
                        map.put("storeBudget", itemInfo.getStoreBudget());
                        map.put("storeOpinion", itemInfo.getStoreOpinion());
                    }
                    // 如果flow等于7，则添加执行计划排序
                    if (detail.getFlow() == 7) {
                        map.put("planSort", itemInfo.getPlanSort());
                    }
                    return map;
                }).collect(Collectors.toList());

                // 情况2：有退回记录
                if (!rollbackRecord.isEmpty()) {
                    // 将三组数据合并
                    List<Map<String, Object>> allRecords = new ArrayList<>();
                    allRecords.addAll(auditDetailRecord);
                    allRecords.addAll(auditRecord);
                    allRecords.addAll(rollbackRecord);

                    // 按时间排序
                    allRecords.sort(Comparator.comparing(map -> (Date) map.get("createTime")));

                    // 判断最新记录是否为退回记录
                    Map<String, Object> latestRecord = allRecords.get(allRecords.size() - 1);
                    if ("back".equals(latestRecord.get("recordType"))) {
                        // 最新为退回记录，只返回最新的退回记录
                        resultList.add(latestRecord);
                    } else {
                        // 按时间顺序返回连贯的所有通过记录
                        List<Map<String, Object>> continuousRecords = new ArrayList<>();
                        Date latestRollbackTime = null;

                        // 找出最新的退回记录时间，从 allRecords 记录中倒序遍历
                        for (int i = allRecords.size() - 1; i >= 0; i--) {
                            Map<String, Object> record = allRecords.get(i);
                            if ("back".equals(record.get("recordType"))) {
                                latestRollbackTime = (Date) record.get("createTime");
                                break;
                            }
                        }

                        // 过滤出比最新退回时间还要新的通过记录
                        if (latestRollbackTime != null) {
                            final Date finalLatestRollbackTime = latestRollbackTime;
                            continuousRecords = allRecords.stream().filter(record -> !"back".equals(record.get("recordType")) && ((Date) record.get("createTime")).after(finalLatestRollbackTime)).collect(Collectors.toList());
                        } else {
                            // 没有退回记录，返回所有通过记录
                            continuousRecords = allRecords.stream().filter(record -> !"back".equals(record.get("recordType"))).collect(Collectors.toList());
                        }

                        resultList.addAll(continuousRecords);
                    }
                }
                // 情况3：没有退回记录
                else {
                    // 将两组数据合并并按时间排序
                    List<Map<String, Object>> allRecords = new ArrayList<>();
                    allRecords.addAll(auditDetailRecord);
                    allRecords.addAll(auditRecord);
                    allRecords.sort(Comparator.comparing(map -> (Date) map.get("createTime")));

                    resultList.addAll(allRecords);
                }
            }


        } else {
            // 单个部门审批的情况
            // 查询项目的审核记录
            List<TItemAudit> auditList = tItemAuditMapper.selectApprovalRecord(id);
            List<Map<String, Object>> auditRecord = auditList.stream().map(detail -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", detail.getId());
                map.put("itemId", detail.getItemId());
                map.put("flow", detail.getFlow());
                map.put("auditStatus", detail.getAuditStatus());
                map.put("auditBy", detail.getAuditBy());
                map.put("reason", detail.getReason());
                map.put("createTime", detail.getCreateTime());
                map.put("auditDeptName", detail.getAuditDeptName());
                map.put("recordType", "parent");  // 标记类型为 parent
                // 如果flow等于4，则添加专业审核排序
                if (detail.getFlow() == 4) {
                    map.put("majorSort", itemInfo.getMajorSort());
                }
                // 如果flow等于5，则添加综合评审意见
                if (detail.getFlow() == 5) {
                    map.put("compoSort", itemInfo.getCompoSort());
                }
                // 如果flow等于6，则添加入库评审意见
                if (detail.getFlow() == 6) {
                    map.put("storeBudget", itemInfo.getStoreBudget());
                    map.put("storeOpinion", itemInfo.getStoreOpinion());
                }
                // 如果flow等于7，则添加执行计划排序
                if (detail.getFlow() == 7) {
                    map.put("planSort", itemInfo.getPlanSort());
                }
                return map;
            }).collect(Collectors.toList());

            // 查询项目的退回记录
            TItemRollback tItemRollback = new TItemRollback();
            tItemRollback.setItemId(id);
            List<TItemRollback> rollbackList = itemRollbackMapper.selectTItemRollbackList(tItemRollback);
            List<Map<String, Object>> rollbackRecord = rollbackList.stream().map(detail -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", detail.getId());
                map.put("itemId", detail.getItemId());
                map.put("flow", detail.getFlow());
                map.put("auditBy", detail.getPerson());
                map.put("reason", detail.getReason());
                map.put("createTime", detail.getTime());
                map.put("auditDeptName", detail.getAuditDeptName());
                map.put("recordType", "back");
                return map;
            }).collect(Collectors.toList());

            // 如果没有审核记录也没有退回记录，返回空数组
            if (auditRecord.isEmpty() && rollbackRecord.isEmpty()) {
                return resultList;
            }

            // 合并审核记录和退回记录
            List<Map<String, Object>> allRecords = new ArrayList<>();
            allRecords.addAll(auditRecord);
            allRecords.addAll(rollbackRecord);

            // 按时间排序
            allRecords.sort(Comparator.comparing(map -> (Date) map.get("createTime")));

            // 判断最新记录是否为退回记录
            Map<String, Object> latestRecord = allRecords.get(allRecords.size() - 1);
            if ("back".equals(latestRecord.get("recordType"))) {
                // 最新为退回记录，只返回最新的退回记录
                resultList.add(latestRecord);
            } else {
                // 按时间顺序返回连贯的所有通过记录
                List<Map<String, Object>> continuousRecords = new ArrayList<>();
                Date latestRollbackTime = null;

                // 找出最新的退回记录时间
                for (int i = allRecords.size() - 1; i >= 0; i--) {
                    Map<String, Object> record = allRecords.get(i);
                    if ("back".equals(record.get("recordType"))) {
                        latestRollbackTime = (Date) record.get("createTime");
                        break;
                    }
                }

                // 过滤出比最新退回时间还要新的通过记录
                if (latestRollbackTime != null) {
                    final Date finalLatestRollbackTime = latestRollbackTime;
                    continuousRecords = allRecords.stream().filter(record -> !"back".equals(record.get("recordType")) && ((Date) record.get("createTime")).after(finalLatestRollbackTime)).collect(Collectors.toList());
                } else {
                    // 没有退回记录，返回所有通过记录
                    continuousRecords = allRecords.stream().filter(record -> !"back".equals(record.get("recordType"))).collect(Collectors.toList());
                }

                resultList.addAll(continuousRecords);
            }

        }

        // 在这里再次进行数据的筛选
        // "teacher" 和 "dean" 角色只返回  flow==3 这步以及这之前的流程记录
        // "focalLeader" 和 "focal" 角色只返回  flow==4 这步以及这之前的流程记录
        // "projectManager" 不做处理

        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        List<String> roleKeys = currentUser.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        // 判断用户角色类型
        if (roleKeys.contains("teacher") || roleKeys.contains("dean")) {
            // "teacher" 和 "dean" 角色只返回 flow==3 这步以及这之前的流程记录
            resultList = resultList.stream().filter(record -> {
                Integer flow = (Integer) record.get("flow");
                return flow != null && flow <= 3;
            }).collect(Collectors.toList());
        } else if (roleKeys.contains("focalLeader") || roleKeys.contains("focal")) {
            // "focalLeader" 和 "focal" 角色只返回 flow==4 这步以及这之前的流程记录
            resultList = resultList.stream().filter(record -> {
                Integer flow = (Integer) record.get("flow");
                return flow != null && flow <= 4;
            }).collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * 查询项目审核信息列表
     *
     * @param tItemAudit 项目审核信息
     * @return 项目审核信息
     */
    @Override
    public List<TItemAudit> selectTItemAuditList(TItemAudit tItemAudit) {
        return tItemAuditMapper.selectTItemAuditList(tItemAudit);
    }

    /**
     * 新增项目审核信息
     *
     * @param tItemAudit 项目审核信息
     * @return 结果
     */
    @Override
    public int insertTItemAudit(TItemAudit tItemAudit) {
        //获取项目当前所处流程环节
        TItemInfo tItemInfo = itemInfoMapper.selectTItemInfoById(tItemAudit.getItemId());
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        tItemAudit.setAuditDeptName(dept.getDeptName());
        if (tItemAudit.getAuditStatus().equals("2")) {
            //拒绝后进行审核环节降级
            tItemInfo.setFlow(0);
            itemInfoMapper.updateTItemInfo(tItemInfo);
        } else if (tItemAudit.getAuditStatus().equals("1")) {
            //通过后进行审核环节升级
            Integer flow = tItemInfo.getFlow();
            if (flow < 12) {
                flow = flow + 1;
                tItemInfo.setFlow(flow);
                itemInfoMapper.updateTItemInfo(tItemInfo);
            }
        }
        tItemAudit.setCreateTime(DateUtils.getNowDate());
        return tItemAuditMapper.insertTItemAudit(tItemAudit);
    }

    /**
     * 修改项目审核信息
     *
     * @param tItemAudit 项目审核信息
     * @return 结果
     */
    @Override
    public int updateTItemAudit(TItemAudit tItemAudit) {
        return tItemAuditMapper.updateTItemAudit(tItemAudit);
    }

    /**
     * 批量删除项目审核信息
     *
     * @param ids 需要删除的项目审核信息主键
     * @return 结果
     */
    @Override
    public int deleteTItemAuditByIds(Long[] ids) {
        return tItemAuditMapper.deleteTItemAuditByIds(ids);
    }

    /**
     * 删除项目审核信息信息
     *
     * @param id 项目审核信息主键
     * @return 结果
     */
    @Override
    public int deleteTItemAuditById(Long id) {
        return tItemAuditMapper.deleteTItemAuditById(id);
    }
}
