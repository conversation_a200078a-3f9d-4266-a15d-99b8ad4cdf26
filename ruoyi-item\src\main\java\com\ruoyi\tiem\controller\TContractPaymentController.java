package com.ruoyi.tiem.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;


import com.ruoyi.tiem.domain.TContractPayment;
import com.ruoyi.tiem.service.ITContractPaymentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同金额支付Controller
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@RestController
@RequestMapping("/contract/payment")
public class TContractPaymentController extends BaseController
{
    @Autowired
    private ITContractPaymentService tContractPaymentService;

    /**
     * 查询合同金额支付列表
     */
    @PreAuthorize("@ss.hasPermi('system:payment:list')")
    @GetMapping("/list")
    public TableDataInfo list(TContractPayment tContractPayment)
    {
        startPage();
        List<TContractPayment> list = tContractPaymentService.selectTContractPaymentList(tContractPayment);
        return getDataTable(list);
    }

    /**
     * 导出合同金额支付列表
     */
    @PreAuthorize("@ss.hasPermi('system:payment:export')")
    @Log(title = "合同金额支付", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TContractPayment tContractPayment)
    {
        List<TContractPayment> list = tContractPaymentService.selectTContractPaymentList(tContractPayment);
        ExcelUtil<TContractPayment> util = new ExcelUtil<TContractPayment>(TContractPayment.class);
        util.exportExcel(response, list, "合同金额支付数据");
    }

    /**
     * 获取合同金额支付详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:payment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tContractPaymentService.selectTContractPaymentById(id));
    }

    /**
     * 新增合同金额支付
     */
    @PreAuthorize("@ss.hasPermi('system:payment:add')")
    @Log(title = "合同金额支付", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TContractPayment tContractPayment)
    {
        return toAjax(tContractPaymentService.insertTContractPayment(tContractPayment));
    }

    /**
     * 修改合同金额支付
     */
    @PreAuthorize("@ss.hasPermi('system:payment:edit')")
    @Log(title = "合同金额支付", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TContractPayment tContractPayment)
    {
        return toAjax(tContractPaymentService.updateTContractPayment(tContractPayment));
    }

    /**
     * 删除合同金额支付
     */
    @PreAuthorize("@ss.hasPermi('system:payment:remove')")
    @Log(title = "合同金额支付", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tContractPaymentService.deleteTContractPaymentByIds(ids));
    }



    /**
     * 批量新增项目目标考核率
     */
//    @PreAuthorize("@ss.hasPermi('system:rate:add')")
    @Log(title = "项目目标考核率", businessType = BusinessType.INSERT)
    @PostMapping("/addInBatches")
    public AjaxResult addInBatches(@RequestBody List<TContractPayment> contractPaymentList) {
        for (TContractPayment contractPayment : contractPaymentList) {
            // 根据 contractId 和 year 和 month 查询是否存在相同记录
            TContractPayment existingContractPayment = tContractPaymentService.selectContractPaymentByYearAndcontractId(contractPayment.getYear(), contractPayment.getContractId(), contractPayment.getMonth(), contractPayment.getRemark());
            if (existingContractPayment != null) {
                // 如果存在，更新 payment 等
                existingContractPayment.setPaymentAmount(contractPayment.getPaymentAmount());
                existingContractPayment.setBillingAmount(contractPayment.getBillingAmount());
                existingContractPayment.setVoucher(contractPayment.getVoucher());
                existingContractPayment.setRemark(contractPayment.getRemark());
                tContractPaymentService.editTContractPayment(existingContractPayment);
            } else {
                // 如果不存在，插入新记录
                tContractPaymentService.insertTContractPayment(contractPayment);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 获取项目目标考核率详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rate:query')")
    @GetMapping("/listDetail")
    public AjaxResult getListDetail(Long contractId, String year) {
        TContractPayment contractPayment = new TContractPayment();
        contractPayment.setContractId(contractId);
        contractPayment.setYear(Integer.valueOf(year));
        List<TContractPayment> tContractPaymentList = tContractPaymentService.selectTContractPaymentList(contractPayment);
        return success(tContractPaymentList);
    }
}
