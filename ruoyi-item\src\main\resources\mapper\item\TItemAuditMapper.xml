<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemAuditMapper">

    <resultMap type="TItemAudit" id="TItemAuditResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="flow"    column="flow"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="reason"    column="reason"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="auditDeptName"    column="audit_dept_name"    />
    </resultMap>

    <sql id="selectTItemAuditVo">
        select id, item_id, flow, audit_status, audit_by, reason, create_time, status, audit_dept_name from t_item_audit
    </sql>

    <select id="selectTItemAuditList" parameterType="TItemAudit" resultMap="TItemAuditResult">
        <include refid="selectTItemAuditVo"/>
        <where>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="flow != null "> and flow = #{flow}</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
            <if test="auditBy != null  and auditBy != ''"> and audit_by = #{auditBy}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="auditDeptName != null  and auditDeptName != ''"> and audit_dept_name like concat('%', #{auditDeptName}, '%')</if>
        </where>
        order by flow desc
    </select>

    <select id="selectTItemAuditById" parameterType="Long" resultMap="TItemAuditResult">
        <include refid="selectTItemAuditVo"/>
        where id = #{id}
    </select>
    
    <select id="selectApprovalRecord" parameterType="Long" resultMap="TItemAuditResult">
        <include refid="selectTItemAuditVo"/>
        where item_id = #{id} and audit_status = '1' and status = '1'
        order by create_time desc
    </select>

    <select id="selectFlowStatusByItemId">
        select audit_status from t_item_audit
        where status = 1 and flow = #{flow} and item_id = #{id}
    </select>

    <insert id="insertTItemAudit" parameterType="TItemAudit" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="flow != null">flow,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="auditDeptName != null">audit_dept_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="flow != null">#{flow},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="auditDeptName != null">#{auditDeptName},</if>
         </trim>
    </insert>

    <update id="updateTItemAudit" parameterType="TItemAudit">
        update t_item_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="flow != null">flow = #{flow},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditDeptName != null">audit_dept_name = #{auditDeptName},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemAuditById" parameterType="Long">
        update t_item_audit set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemAuditByIds" parameterType="String">
        update t_item_audit set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectByItemIds" resultMap="TItemAuditResult">
        SELECT *
        FROM t_item_audit
        WHERE item_id IN
        <foreach collection="itemIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>

    <update id="batchUpdateTItemAudit">
        UPDATE t_item_audit
        SET audit_status = CASE id
        <foreach collection="audits" item="audit">
            WHEN #{audit.id} THEN #{audit.auditStatus}
        </foreach>
        END,
        reason = CASE id
        <foreach collection="audits" item="audit">
            WHEN #{audit.id} THEN #{audit.reason}
        </foreach>
        END
        WHERE id IN
        <foreach collection="audits" item="audit" open="(" separator="," close=")">
            #{audit.id}
        </foreach>
    </update>
</mapper>
