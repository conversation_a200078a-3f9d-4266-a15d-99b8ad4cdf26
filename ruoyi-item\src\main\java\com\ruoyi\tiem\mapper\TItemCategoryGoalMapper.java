package com.ruoyi.tiem.mapper;

import com.ruoyi.tiem.domain.TItemCategoryGoal;

import java.util.List;

/**
 * 项目类别目标Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface TItemCategoryGoalMapper 
{
    /**
     * 查询项目类别目标
     * 
     * @param id 项目类别目标主键
     * @return 项目类别目标
     */
    public TItemCategoryGoal selectTItemCategoryGoalById(Long id);

    /**
     * 查询项目类别目标列表
     * 
     * @param tItemCategoryGoal 项目类别目标
     * @return 项目类别目标集合
     */
    public List<TItemCategoryGoal> selectTItemCategoryGoalList(TItemCategoryGoal tItemCategoryGoal);

    /**
     * 新增项目类别目标
     * 
     * @param tItemCategoryGoal 项目类别目标
     * @return 结果
     */
    public int insertTItemCategoryGoal(TItemCategoryGoal tItemCategoryGoal);

    /**
     * 修改项目类别目标
     * 
     * @param tItemCategoryGoal 项目类别目标
     * @return 结果
     */
    public int updateTItemCategoryGoal(TItemCategoryGoal tItemCategoryGoal);

    /**
     * 删除项目类别目标
     * 
     * @param id 项目类别目标主键
     * @return 结果
     */
    public int deleteTItemCategoryGoalById(Long id);

    /**
     * 批量删除项目类别目标
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTItemCategoryGoalByIds(Long[] ids);
}
