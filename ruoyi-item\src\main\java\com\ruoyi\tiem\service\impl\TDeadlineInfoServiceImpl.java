package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TDeadlineInfo;
import com.ruoyi.tiem.mapper.TDeadlineInfoMapper;
import com.ruoyi.tiem.service.ITDeadlineInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目关门节点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
public class TDeadlineInfoServiceImpl implements ITDeadlineInfoService
{
    @Autowired
    private TDeadlineInfoMapper tDeadlineInfoMapper;

    /**
     * 查询项目关门节点
     * 
     * @param id 项目关门节点主键
     * @return 项目关门节点
     */
    @Override
    public TDeadlineInfo selectTDeadlineInfoById(Long id)
    {
        return tDeadlineInfoMapper.selectTDeadlineInfoById(id);
    }

    /**
     * 查询项目关门节点列表
     * 
     * @param tDeadlineInfo 项目关门节点
     * @return 项目关门节点
     */
    @Override
    public List<TDeadlineInfo> selectTDeadlineInfoList(TDeadlineInfo tDeadlineInfo)
    {
        return tDeadlineInfoMapper.selectTDeadlineInfoList(tDeadlineInfo);
    }

    /**
     * 新增项目关门节点
     * 
     * @param tDeadlineInfo 项目关门节点
     * @return 结果
     */
    @Override
    public int insertTDeadlineInfo(TDeadlineInfo tDeadlineInfo)
    {
        return tDeadlineInfoMapper.insertTDeadlineInfo(tDeadlineInfo);
    }

    /**
     * 修改项目关门节点
     * 
     * @param tDeadlineInfo 项目关门节点
     * @return 结果
     */
    @Override
    public int updateTDeadlineInfo(TDeadlineInfo tDeadlineInfo)
    {
        return tDeadlineInfoMapper.updateTDeadlineInfo(tDeadlineInfo);
    }

    /**
     * 批量删除项目关门节点
     * 
     * @param ids 需要删除的项目关门节点主键
     * @return 结果
     */
    @Override
    public int deleteTDeadlineInfoByIds(Long[] ids)
    {
        return tDeadlineInfoMapper.deleteTDeadlineInfoByIds(ids);
    }

    /**
     * 删除项目关门节点信息
     * 
     * @param id 项目关门节点主键
     * @return 结果
     */
    @Override
    public int deleteTDeadlineInfoById(Long id)
    {
        return tDeadlineInfoMapper.deleteTDeadlineInfoById(id);
    }
}
