package com.ruoyi.tiem.mapper;

import com.ruoyi.tiem.domain.TContractPayment;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 合同金额支付Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface TContractPaymentMapper 
{
    /**
     * 查询合同金额支付
     * 
     * @param id 合同金额支付主键
     * @return 合同金额支付
     */
    public TContractPayment selectTContractPaymentById(Long id);

    /**
     * 查询合同金额支付列表
     * 
     * @param tContractPayment 合同金额支付
     * @return 合同金额支付集合
     */
    public List<TContractPayment> selectTContractPaymentList(TContractPayment tContractPayment);

    /**
     * 新增合同金额支付
     * 
     * @param tContractPayment 合同金额支付
     * @return 结果
     */
    public int insertTContractPayment(TContractPayment tContractPayment);

    /**
     * 修改合同金额支付
     * 
     * @param tContractPayment 合同金额支付
     * @return 结果
     */
    public int updateTContractPayment(TContractPayment tContractPayment);

    /**
     * 删除合同金额支付
     * 
     * @param id 合同金额支付主键
     * @return 结果
     */
    public int deleteTContractPaymentById(Long id);

    /**
     * 批量删除合同金额支付
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTContractPaymentByIds(Long[] ids);

    /**
     * 根据项目id批量删除合同金额支付
     *
     * @param ids 需要删除的数据项目id集合
     * @return 结果
     */
    public int deleteTContractPaymentByItemIds(Long[] ids);

    /**
     * 查询截止当前月份总计支付金额
     *
     * @param tContractPayment 合同金额支付
     * @return
     */
    public BigDecimal selectTotalPaymentAmount(TContractPayment tContractPayment);

    /**
     * 根据执行年份查询所有执行项目每月支付额等
     * @param year 项目执行年度
     * @return
     */
    public List<Map> selectTotalEveryMonthPayment(String year);

    /**
     * 根据执行年份查询某个执行项目每月支付额等
     * @param year 项目执行年度
     * @param id 项目id
     * @return
     */
    public List<Map> selectItemEveryMonthPayment(String year, @Param("id")Long id);

    /**
     * 根据执行年份查询某个具体合同每月支付额等
     * @param year 项目执行年度
     * @param contractId 合同编号
     * @return
     */
    public List<Map> selectContractEveryMonthPayment(@Param("year")String year, @Param("contractId")Long contractId);

    /**
     * 根据执行年份查询某个具体合同某个月月支付额
     * @param year 项目执行年度
     * @param contractId 合同编号
     * @param month 月份
     * @return
     */
    public TContractPayment selectContractPaymentByYearAndcontractId(@Param("year")Integer year, @Param("contractId")Long contractId, @Param("month")Integer month, @Param("remark")String remark);

    /**
     * 根据month和year和itemCategory编辑支付额
     *
     */
    public int editTContractPayment(TContractPayment contractPayment);
}
