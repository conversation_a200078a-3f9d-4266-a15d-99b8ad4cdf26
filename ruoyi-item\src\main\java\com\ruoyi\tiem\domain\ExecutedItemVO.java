package com.ruoyi.tiem.domain;

public class ExecutedItemVO {
    /**
     *  开始年份
     */
    private Integer startYear;

    /**
     *  结束年份
     */
    private Integer endYear;

    /**
     *  申报单位id
     */
    private String applyUnitId;

    public Integer getStartYear() {
        return startYear;
    }

    public void setStartYear(Integer startYear) {
        this.startYear = startYear;
    }

    public Integer getEndYear() {
        return endYear;
    }

    public void setEndYear(Integer endYear) {
        this.endYear = endYear;
    }

    public String getApplyUnitId() {
        return applyUnitId;
    }

    public void setApplyUnitId(String applyUnitId) {
        this.applyUnitId = applyUnitId;
    }

    @Override
    public String toString() {
        return "ExecutedItemVO{" +
                "startYear='" + startYear + '\'' +
                ", endYear='" + endYear + '\'' +
                ", applyUnit='" + applyUnitId + '\'' +
                '}';
    }
}
