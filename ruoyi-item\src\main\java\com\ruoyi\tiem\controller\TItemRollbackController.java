package com.ruoyi.tiem.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemAudit;
import com.ruoyi.tiem.domain.TItemRollback;
import com.ruoyi.tiem.mapper.TItemAuditMapper;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import com.ruoyi.tiem.mapper.TItemRollbackMapper;
import com.ruoyi.tiem.service.ITItemRollbackService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目节点回退信息
 * Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/system/rollback")
public class TItemRollbackController extends BaseController {
    @Autowired
    private ITItemRollbackService tItemRollbackService;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    @Autowired
    private TItemAuditMapper tItemAuditMapper;

    @Autowired
    private TItemRollbackMapper tItemRollbackMapper;

    /**
     * 查询项目节点回退信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:list')")
    @GetMapping("/list")
    public TableDataInfo list(TItemRollback tItemRollback) {
        startPage();
        List<TItemRollback> list = tItemRollbackService.selectTItemRollbackList(tItemRollback);
        return getDataTable(list);
    }

    /**
     * 导出项目节点回退信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:export')")
    @Log(title = "项目节点回退信息 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemRollback tItemRollback) {
        List<TItemRollback> list = tItemRollbackService.selectTItemRollbackList(tItemRollback);
        ExcelUtil<TItemRollback> util = new ExcelUtil<TItemRollback>(TItemRollback.class);
        util.exportExcel(response, list, "项目节点回退信息数据");
    }

    /**
     * 获取项目节点回退信息
     * 详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tItemRollbackService.selectTItemRollbackById(id));
    }

    /**
     * 新增项目节点回退信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:add')")
    @Log(title = "项目节点回退信息 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemRollback tItemRollback) {
        return toAjax(tItemRollbackService.insertTItemRollback(tItemRollback));
    }

    /**
     * 批量新增项目节点回退信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:add')")
    @Log(title = "项目节点回退信息 ", businessType = BusinessType.INSERT)
    @PostMapping("/addInBatches")
    public AjaxResult addInBatches(@RequestBody List<TItemRollback> list) {
        int result = -1;
        for (TItemRollback itemRollback : list) {
            result = tItemRollbackService.insertTItemRollback(itemRollback);
        }
        return toAjax(result);
    }

    /**
     * 批量新增项目节点回退信息Two
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:add')")
    @Log(title = "项目节点回退信息Two", businessType = BusinessType.INSERT)
    @PostMapping("/addInBatchesTwo")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addInBatchesTwo(@RequestBody List<TItemRollback> list) {
        // 1. 批量插入 TItemRollback
        tItemRollbackMapper.insertTItemRollbackBatch(list);

        // 2. 提取所有 itemId
        List<String> itemIds = list.stream().map(item -> item.getItemId().toString()).collect(Collectors.toList());

        // 3. 一次性查询所有 TItemAudit
        List<TItemAudit> auditList = tItemAuditMapper.selectByItemIds(itemIds);

        // 4. 处理状态和原因，并收集需要更新的对象
        List<TItemAudit> auditsToUpdate = new ArrayList<>();

        for (TItemAudit itemAudit : auditList) {
            itemAudit.setAuditStatus("3");
            switch (itemAudit.getFlow()) {
                case 1:
                    itemAudit.setReason("待老师重新发起审批");
                    break;
                case 2:
                    itemAudit.setReason("待" + itemAudit.getAuditDeptName() + "领导审核");
                    break;
                case 3:
                    itemAudit.setReason("待" + itemAudit.getAuditDeptName() + "领导审核");
                    break;
                case 4:
                    itemAudit.setReason("专业审核进行中...");
                    break;
                case 5:
                    itemAudit.setReason("综合评审进行中...");
                    break;
                case 6:
                    itemAudit.setReason("入库评审进行中...");
                    break;
                case 7:
                    itemAudit.setReason("执行计划排序进行中...");
                    break;
                case 9:
                    itemAudit.setReason("合约规划填报");
                    break;
                case 11:
                    itemAudit.setReason("待" + itemAudit.getAuditDeptName() + "经办人审核");
                    break;
                default:
                    break;
            }
            auditsToUpdate.add(itemAudit);
        }

        // 5. 批量更新
        if (!auditsToUpdate.isEmpty()) {
            tItemAuditMapper.batchUpdateTItemAudit(auditsToUpdate);
        }

        // 6. 更新 item_flow_two 表字段
        itemInfoMapper.updateItemFlowTwo(itemIds, 0);

        return toAjax(list.size());
    }

    /**
     * 修改项目节点回退信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:edit')")
    @Log(title = "项目节点回退信息 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemRollback tItemRollback) {
        return toAjax(tItemRollbackService.updateTItemRollback(tItemRollback));
    }

    /**
     * 删除项目节点回退信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rollback:remove')")
    @Log(title = "项目节点回退信息 ", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tItemRollbackService.deleteTItemRollbackByIds(ids));
    }
}
