package com.ruoyi.tiem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.tiem.domain.TItemInfo;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.TContractInfoMapper;
import com.ruoyi.tiem.domain.TContractInfo;
import com.ruoyi.tiem.service.ITContractInfoService;

/**
 * 合同信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class TContractInfoServiceImpl implements ITContractInfoService
{
    @Autowired
    private TContractInfoMapper tContractInfoMapper;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    /**
     * 查询合同信息
     *
     * @param id 合同信息主键
     * @return 合同信息
     */
    @Override
    public TContractInfo selectTContractInfoById(Long id)
    {
        TContractInfo tContractInfo = tContractInfoMapper.selectTContractInfoById(id);
        return tContractInfo;
    }

    /**
     * 查询合同信息列表
     *
     * @param tContractInfo 合同信息
     * @return 合同信息
     */
    @Override
    public List<TContractInfo> selectTContractInfoList(TContractInfo tContractInfo)
    {
        return tContractInfoMapper.selectTContractInfoList(tContractInfo);
    }

    /**
     * 新增合同信息
     *
     * @param tContractInfo 合同信息
     * @return 结果
     */
    @Override
    public int insertTContractInfo(TContractInfo tContractInfo)
    {
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        tContractInfo.setCreateBy(nickName);
        tContractInfo.setCreateTime(DateUtils.getNowDate());
        int row = tContractInfoMapper.insertTContractInfo(tContractInfo);
        //更新合同开票金额、付款金额
        itemInfoMapper.updateContractAmount(tContractInfo.getItemIds());
        //更新签约率
        itemInfoMapper.updateSignRate(tContractInfo.getItemIds());
        return row;
    }

    /**
     * 修改合同信息
     *
     * @param tContractInfo 合同信息
     * @return 结果
     */
    @Override
    public int updateTContractInfo(TContractInfo tContractInfo)
    {
        tContractInfo.setUpdateTime(DateUtils.getNowDate());
        return tContractInfoMapper.updateTContractInfo(tContractInfo);
    }

    /**
     * 批量删除合同信息
     *
     * @param ids 需要删除的合同信息主键
     * @return 结果
     */
    @Override
    public int deleteTContractInfoByIds(Long[] ids)
    {
        return tContractInfoMapper.deleteTContractInfoByIds(ids);
    }

    /**
     * 删除合同信息信息
     *
     * @param id 合同信息主键
     * @return 结果
     */
    @Override
    public int deleteTContractInfoById(Long id)
    {
        return tContractInfoMapper.deleteTContractInfoById(id);
    }
}
