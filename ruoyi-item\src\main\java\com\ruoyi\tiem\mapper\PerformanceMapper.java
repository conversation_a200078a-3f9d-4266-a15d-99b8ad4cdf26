package com.ruoyi.tiem.mapper;

import java.util.List;
import com.ruoyi.tiem.domain.Performance;
import org.apache.ibatis.annotations.Param;

/**
 * 项目绩效信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface PerformanceMapper 
{
    /**
     * 查询项目绩效信息
     * 
     * @param id 项目绩效信息主键
     * @return 项目绩效信息
     */
    public Performance selectPerformanceById(Long id);

    /**
     * 查询项目绩效信息列表
     * 
     * @param performance 项目绩效信息
     * @return 项目绩效信息集合
     */
    public List<Performance> selectPerformanceList(Performance performance);

    /**
     * 新增项目绩效信息
     * 
     * @param performance 项目绩效信息
     * @return 结果
     */
    public int insertPerformance(Performance performance);

    /**
     * 修改项目绩效信息
     * 
     * @param performance 项目绩效信息
     * @return 结果
     */
    public int updatePerformance(Performance performance);

    /**
     * 删除项目绩效信息
     * 
     * @param id 项目绩效信息主键
     * @return 结果
     */
    public int deletePerformanceById(Long id);

    /**
     * 批量删除项目绩效信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePerformanceByIds(Long[] ids);

    /**
     * 根据执行年度和项目类别查询绩效指标信息
     *
     * @param category 项目绩效信息主键
     * @return 项目绩效信息
     */
    public List<Performance> selectPerformanceByCategory(@Param("category")String category, @Param("year")String year);

    /**
     * 根据项目类别查询分属所有项目绩效指标信息
     *
     * @param category 项目绩效信息主键
     * @return 项目绩效信息
     */
    public List<Performance> selectItemPerformanceByCategory(@Param("category")String category, @Param("year")String year);
}
