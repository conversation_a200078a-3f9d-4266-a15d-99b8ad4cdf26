package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同金额支付对象 t_contract_payment
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
public class TContractPayment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目id */
    private Long itemId;

    /** 合同id */
    @Excel(name = "合同id")
    private Long contractId;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal paymentAmount;

    /** 凭证号 */
    @Excel(name = "凭证号")
    private String voucher;

    /** 开票金额 */
    @Excel(name = "开票金额")
    private BigDecimal billingAmount;

    /** 支付年份 */
    @Excel(name = "支付年份")
    private Integer year;

    /** 支付月份 */
    @Excel(name = "支付月份")
    private Integer month;

    /** 状态（1：正常；9：删除） */
    @Excel(name = "状态", readConverterExp = "1=：正常；9：删除")
    private String status;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public String getRemark() { return remark;}

    public void setRemark(String remark) { this.remark = remark;}

    public String getVoucher() {
        return voucher;
    }

    public void setVoucher(String voucher) {
        this.voucher = voucher;
    }

    public BigDecimal getBillingAmount() {
        return billingAmount;
    }

    public void setBillingAmount(BigDecimal billingAmount) {
        this.billingAmount = billingAmount;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setContractId(Long contractId)
    {
        this.contractId = contractId;
    }

    public Long getContractId()
    {
        return contractId;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) 
    {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() 
    {
        return paymentAmount;
    }
    public void setMonth(Integer month)
    {
        this.month = month;
    }

    public Integer getMonth()
    {
        return month;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemId", getItemId())
            .append("contractId", getContractId())
            .append("paymentAmount", getPaymentAmount())
            .append("billingAmount", getBillingAmount())
            .append("voucher", getVoucher())
            .append("year", getYear())
            .append("month", getMonth())
            .append("status", getStatus())
            .append("remark", getRemark())
            .toString();
    }
}
