package com.ruoyi.tiem.service;

import java.util.List;
import com.ruoyi.tiem.domain.ExecutePlan;

/**
 * 执行计划Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface IExecutePlanService 
{
    /**
     * 查询执行计划
     * 
     * @param id 执行计划主键
     * @return 执行计划
     */
    public ExecutePlan selectExecutePlanById(Long id);

    /**
     * 查询执行计划列表
     * 
     * @param executePlan 执行计划
     * @return 执行计划集合
     */
    public List<ExecutePlan> selectExecutePlanList(ExecutePlan executePlan);

    /**
     * 新增执行计划
     * 
     * @param executePlan 执行计划
     * @return 结果
     */
    public int insertExecutePlan(ExecutePlan executePlan);

    /**
     * 修改执行计划
     * 
     * @param executePlan 执行计划
     * @return 结果
     */
    public int updateExecutePlan(ExecutePlan executePlan);

    /**
     * 批量删除执行计划
     * 
     * @param ids 需要删除的执行计划主键集合
     * @return 结果
     */
    public int deleteExecutePlanByIds(Long[] ids);

    /**
     * 删除执行计划信息
     * 
     * @param id 执行计划主键
     * @return 结果
     */
    public int deleteExecutePlanById(Long id);
}
