package com.ruoyi.tiem.service;

import java.util.List;
import com.ruoyi.tiem.domain.ContractPlan;

/**
 * 合约规划信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IContractPlanService 
{
    /**
     * 查询合约规划信息
     * 
     * @param id 合约规划信息主键
     * @return 合约规划信息
     */
    public ContractPlan selectContractPlanById(Long id);

    /**
     * 查询合约规划信息列表
     * 
     * @param contractPlan 合约规划信息
     * @return 合约规划信息集合
     */
    public List<ContractPlan> selectContractPlanList(ContractPlan contractPlan);

    /**
     * 根据项目id查询V2合规规划信息
     * @param itemId 项目id
     * @return
     */
    public List<ContractPlan> selectV2ContractPlanList(Long itemId);

    /**
     * 新增合约规划信息
     * 
     * @param contractPlan 合约规划信息
     * @return 结果
     */
    public int insertContractPlan(ContractPlan contractPlan);

    /**
     * 修改合约规划信息
     * 
     * @param contractPlan 合约规划信息
     * @return 结果
     */
    public int updateContractPlan(ContractPlan contractPlan);

    /**
     * 批量删除合约规划信息
     * 
     * @param ids 需要删除的合约规划信息主键集合
     * @return 结果
     */
    public int deleteContractPlanByIds(Long[] ids);

    /**
     * 删除合约规划信息信息
     * 
     * @param id 合约规划信息主键
     * @return 结果
     */
    public int deleteContractPlanById(Long id);
}
