package com.ruoyi.tiem.service;

import java.util.List;
import com.ruoyi.tiem.domain.TItemAuditDetail;

/**
 * 多流程审核明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ITItemAuditDetailService 
{
    /**
     * 查询多流程审核明细
     * 
     * @param id 多流程审核明细主键
     * @return 多流程审核明细
     */
    public TItemAuditDetail selectTItemAuditDetailById(Long id);

    /**
     * 查询多流程审核明细列表
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 多流程审核明细集合
     */
    public List<TItemAuditDetail> selectTItemAuditDetailList(TItemAuditDetail tItemAuditDetail);

    /**
     * 新增多流程审核明细
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 结果
     */
    public int insertTItemAuditDetail(TItemAuditDetail tItemAuditDetail);

    /**
     * 修改多流程审核明细
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 结果
     */
    public int updateTItemAuditDetail(TItemAuditDetail tItemAuditDetail);

    /**
     * 批量删除多流程审核明细
     * 
     * @param ids 需要删除的多流程审核明细主键集合
     * @return 结果
     */
    public int deleteTItemAuditDetailByIds(Long[] ids);

    /**
     * 删除多流程审核明细信息
     * 
     * @param id 多流程审核明细主键
     * @return 结果
     */
    public int deleteTItemAuditDetailById(Long id);
}
