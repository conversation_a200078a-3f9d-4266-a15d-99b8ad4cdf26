package com.ruoyi.tiem.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.tiem.domain.TItemAuditDetail;
import com.ruoyi.tiem.domain.TItemInfo;
import com.ruoyi.tiem.domain.TItemRollback;
import com.ruoyi.tiem.mapper.TItemAuditDetailMapper;
import com.ruoyi.tiem.mapper.TItemAuditMapper;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import com.ruoyi.tiem.mapper.TItemRollbackMapper;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.TItemAudit;
import com.ruoyi.tiem.service.ITItemAuditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目审核信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@RestController
@RequestMapping("/item/audit")
public class TItemAuditController extends BaseController {
    @Autowired
    private ITItemAuditService tItemAuditService;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private TItemAuditMapper tItemAuditMapper;

    @Autowired
    private TItemRollbackMapper tItemRollbackMapper;

    @Autowired
    private TItemAuditDetailMapper tItemAuditDetailMapper;

    /**
     * 查询项目审核信息列表
     */
    // @PreAuthorize("@ss.hasPermi('item:audit:list')")
    @GetMapping("/list")
    public AjaxResult list(TItemAudit tItemAudit) {
        List<TItemAudit> list = tItemAuditService.selectTItemAuditList(tItemAudit);
        return AjaxResult.success(list);
    }

    /**
     * 导出项目审核信息列表
     */
    @PreAuthorize("@ss.hasPermi('item:audit:export')")
    @Log(title = "项目审核信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemAudit tItemAudit) {
        List<TItemAudit> list = tItemAuditService.selectTItemAuditList(tItemAudit);
        ExcelUtil<TItemAudit> util = new ExcelUtil<TItemAudit>(TItemAudit.class);
        util.exportExcel(response, list, "项目审核信息数据");
    }

    /**
     * 获取项目审核信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('item:audit:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tItemAuditService.selectTItemAuditById(id));
    }

    /**
     * 获整合项目审核记录
     */
    @GetMapping(value = "/approvalRecord/{id}")
    public AjaxResult approvalRecord(@PathVariable("id") Long id) {
        return success(tItemAuditService.handleRecord(id));
    }

    /**
     * 新增项目审核信息
     */
    // @PreAuthorize("@ss.hasPermi('item:audit:add')")
    @Log(title = "项目审核信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemAudit tItemAudit) {
        return toAjax(tItemAuditService.insertTItemAudit(tItemAudit));
    }

    /**
     * 项目审核接口
     *
     * @param tItemAudit
     * @return
     */
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody TItemAudit tItemAudit) {
        // 1. 获取项目基本信息
        // 根据项目ID获取项目信息，包含当前流程节点等信息
        TItemInfo tItemInfo = itemInfoMapper.selectTItemInfoById(tItemAudit.getItemId());

        // 2. 获取当前操作用户信息
        // 获取当前登录用户及其所属部门信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        tItemAudit.setAuditDeptName(dept.getDeptName());

        // 3. 获取当前项目流程节点
        Integer flow = tItemInfo.getFlow();

        // 4. 获取项目所有审核记录
        // 查询该项目所有的审核记录，用于后续批量更新
        TItemAudit tItemAudit1 = new TItemAudit();
        tItemAudit1.setItemId(tItemAudit.getItemId());
        List<TItemAudit> list = tItemAuditMapper.selectTItemAuditList(tItemAudit1);

        // 5. 处理审核拒绝的情况（审核状态为2）
        if (tItemAudit.getAuditStatus().equals("2")) {
            // 5.1 遍历所有审核记录进行状态更新
            for (TItemAudit itemAudit : list) {
                // 5.2 特殊处理流程节点8和10的情况
                if (itemAudit.getFlow() == 8 && flow == 8) {
                    itemAudit.setAuditStatus("3");  // 设置为已拒绝状态
                    tItemAuditMapper.updateTItemAudit(itemAudit);
                } else if (itemAudit.getFlow() == 10 && flow == 10) {
                    itemAudit.setAuditStatus("3");
                    tItemAuditMapper.updateTItemAudit(itemAudit);
                } else {
                    // 5.3 处理其他流程节点
                    itemAudit.setAuditStatus("3");
                    // 根据不同的流程节点设置不同的拒绝原因
                    switch (itemAudit.getFlow()) {
                        case 1:
                            itemAudit.setReason("自动通过");
                            break;
                        case 2:
                        case 4:
                            itemAudit.setReason("待" + itemAudit.getAuditDeptName() + "领导审核");
                            break;
                        case 3:
                        case 5:
                            itemAudit.setReason("待" + itemAudit.getAuditDeptName() + "经办人审核");
                            break;
                        default:
                            break;
                    }
                    tItemAuditMapper.updateTItemAudit(itemAudit);
                }
            }
            // 5.4 拒绝后重置项目流程到初始状态
            tItemInfo.setFlow(0);
            itemInfoMapper.updateTItemInfo(tItemInfo);
        }
        // 6. 处理审核通过的情况（审核状态为1）
        else if (tItemAudit.getAuditStatus().equals("1")) {
            // 6.1 如果当前流程节点小于14，则进入下一个流程节点
            if (flow < 14) {
                flow = flow + 1;
                tItemInfo.setFlow(flow);
                itemInfoMapper.updateTItemInfo(tItemInfo);
            }
            // 6.2 更新当前流程节点的审核记录
            for (TItemAudit itemAudit : list) {
                if (Objects.equals(itemAudit.getFlow(), flow)) {
                    itemAudit.setAuditStatus("1");  // 设置为已通过状态
                    itemAudit.setReason(tItemAudit.getReason());  // 设置审核意见
                    tItemAuditMapper.updateTItemAudit(itemAudit);
                }
            }
        }
        return AjaxResult.success();
    }

    /**
     * 项目审核接口New
     *
     * @param tItemAudit
     * @return
     */
    @PostMapping("/submitTwo")
    public AjaxResult submitTwo(@RequestBody TItemAudit tItemAudit) {

        // 1. 获取项目基本信息
        // 根据项目ID获取项目信息，包含当前流程节点等信息
        TItemInfo tItemInfo = itemInfoMapper.selectTItemInfoById(tItemAudit.getItemId());

        // 2. 获取当前操作用户信息
        // 获取当前登录用户及其所属部门信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        // 取出部门名称备用
        String userDeptName = user.getDept().getDeptName();
        tItemAudit.setAuditDeptName(dept.getDeptName());

        // 3. 获取当前项目流程节点
        Integer flow = tItemInfo.getFlow();


        String[] isMulti = tItemInfo.getApplyUnit().split(",");
        if (isMulti.length > 1) { // 如果是多个申报部门，即联合申报
            switch (flow) {
                case 1:
                    // 先查询该flow==1流程下的子流程是否都进行了审批
                    // 查询t_item_audit_detail表中item_id == item.getId且flow为1的数据项
                    List<TItemAuditDetail> auditDetails = tItemAuditDetailMapper.selectByItemIdAndFlow(tItemInfo.getId(), 1);
                    // 检查auditDetails中的所有数据项的auditStatus是否都为1
                    boolean allApproved = auditDetails.stream().allMatch(detail -> "1".equals(detail.getAuditStatus()));
                    if (allApproved) {  // 如果所有数据项的auditStatus都为1，则说明所有老师都审批通过
                        // 查询t_item_audit_detail表中item_id == item.getId且flow为2的数据项
                        List<TItemAuditDetail> deanAuditDetails = tItemAuditDetailMapper.selectByItemIdAndFlow(tItemInfo.getId(), 2);
                        // 在这里找到 deanAuditDetails 中 auditStatus 不为 "1"，且 auditDeptName 和 user 的部门名称相同的数据项
                        Optional<TItemAuditDetail> deanMatchingDetail = deanAuditDetails.stream().filter(detail -> !"1".equals(detail.getAuditStatus()) && userDeptName.equals(detail.getAuditDeptName())).findFirst();
                        // 获得存在匹配的数据
                        TItemAuditDetail denMatchedDetail = deanMatchingDetail.get();
                        // 处理通过的情况
                        if (tItemAudit.getAuditStatus().equals("1")) {
                            denMatchedDetail.setAuditStatus("1");  // 设置为已通过状态
                            denMatchedDetail.setReason(tItemAudit.getReason());  // 设置审核意见
                            denMatchedDetail.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                            denMatchedDetail.setAuditBy(user.getNickName());  // 设置审核人
                            tItemAuditDetailMapper.updateTItemAuditDetail(denMatchedDetail);   // 更新审核记录

                            // 再次查询t_item_audit_detail表中item_id == item.getId且flow为2的数据项
                            List<TItemAuditDetail> freshDenMatchedDetail = tItemAuditDetailMapper.selectByItemIdAndFlow(tItemInfo.getId(), 2);
                            // 检查freshDenMatchedDetail中的所有数据项的auditStatus是否都为1
                            boolean frshDenAllApproved = freshDenMatchedDetail.stream().allMatch(detail -> "1".equals(detail.getAuditStatus()));
                            if (frshDenAllApproved) {  // 如果所有数据项的auditStatus都为1，则说明所有学院领导都审批通过
                                // 则进入下一个流程节点
                                flow = flow + 1;
                                tItemInfo.setFlow(flow);
                                itemInfoMapper.updateTItemInfo(tItemInfo);

                                // 那么就更新父项目流程节点为2的数据的auditStatus为1
                                TItemAudit parentAudit = new TItemAudit();
                                parentAudit.setItemId(tItemInfo.getId());
                                parentAudit.setFlow(2);
                                List<TItemAudit> parentAudits = tItemAuditMapper.selectTItemAuditList(parentAudit);
                                if (parentAudits != null && !parentAudits.isEmpty()) {
                                    for (TItemAudit audit : parentAudits) {
                                        audit.setAuditStatus("1");
                                        audit.setReason("所有学院领导审批通过");
                                        audit.setCreateTime(DateUtils.getNowDate());
                                        tItemAuditMapper.updateTItemAudit(audit);
                                    }
                                }
                            }
                        } else if (tItemAudit.getAuditStatus().equals("2") || tItemAudit.getAuditStatus().equals("3")) {  // 处理拒绝的情况
                            TItemRollback tItemRollback = new TItemRollback();
                            tItemRollback.setFlow(flow);
                            tItemRollback.setItemId(tItemAudit.getItemId());
                            tItemRollback.setPerson(tItemAudit.getAuditBy());
                            tItemRollback.setTime(DateUtils.getNowDate());
                            tItemRollback.setReason(tItemAudit.getReason());
                            tItemRollback.setAuditDeptName(userDeptName);
                            List<TItemRollback> listRollBack = new ArrayList<>();
                            listRollBack.add(tItemRollback);
                            tItemRollbackMapper.insertTItemRollbackBatch(listRollBack);

                            if (tItemAudit.getAuditStatus().equals("2")) {
                                // 遍历所有老师审核记录进行状态更新
                                for (TItemAuditDetail itemAuditDetail : auditDetails) {
                                    itemAuditDetail.setAuditStatus("3");
                                    itemAuditDetail.setReason("待" + itemAuditDetail.getAuditDeptName() + "老师审批");
                                    tItemAuditDetailMapper.updateTItemAuditDetail(itemAuditDetail);
                                }

                                // 遍历所有学院领导审核记录进行状态更新
                                for (TItemAuditDetail itemAuditDetail : deanAuditDetails) {
                                    itemAuditDetail.setAuditStatus("3");
                                    itemAuditDetail.setReason("待" + itemAuditDetail.getAuditDeptName() + "领导审批");
                                    tItemAuditDetailMapper.updateTItemAuditDetail(itemAuditDetail);
                                }

                                // 接着更新父项目流程节点为1的数据的审核原因
                                TItemAudit parentAudit = new TItemAudit();
                                parentAudit.setItemId(tItemInfo.getId());
                                parentAudit.setFlow(1);

                                List<TItemAudit> parentAudits = tItemAuditMapper.selectTItemAuditList(parentAudit);
                                if (parentAudits != null && !parentAudits.isEmpty()) {
                                    for (TItemAudit audit : parentAudits) {
                                        audit.setAuditStatus("3");
                                        audit.setReason("待老师重新发起审批");
                                        tItemAuditMapper.updateTItemAudit(audit);
                                    }
                                }
                                // 拒绝后重置项目流程到初始状态
                                tItemInfo.setFlow(0);
                            } else {
                                tItemInfo.setFlow(101);
                            }

                            itemInfoMapper.updateTItemInfo(tItemInfo);
                        }
                    } else {
                        // 否则就说明存在老师未审批通过
                        // 在这里找到 auditDetails 中 auditStatus 不为 "1"，且 auditDeptName 和 user 的部门名称相同的数据项
                        Optional<TItemAuditDetail> matchingDetail = auditDetails.stream().filter(detail -> !"1".equals(detail.getAuditStatus()) && userDeptName.equals(detail.getAuditDeptName())).findFirst();
                        // 获得存在匹配的数据
                        TItemAuditDetail matchedDetail = matchingDetail.get();
                        // 处理通过的情况
                        if (tItemAudit.getAuditStatus().equals("1")) {
                            matchedDetail.setAuditStatus("1");  // 设置为已通过状态
                            matchedDetail.setReason(tItemAudit.getReason());  // 设置审核意见
                            matchedDetail.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                            matchedDetail.setAuditBy(user.getNickName());  // 设置审核人
                            tItemAuditDetailMapper.updateTItemAuditDetail(matchedDetail);   // 更新审核记录

                            // 再次查询t_item_audit_detail表中item_id == item.getId且flow为1的数据项
                            List<TItemAuditDetail> freshAuditDetails = tItemAuditDetailMapper.selectByItemIdAndFlow(tItemInfo.getId(), 1);
                            // 检查freshAuditDetails中的所有数据项的auditStatus是否都为1
                            boolean frshAllApproved = freshAuditDetails.stream().allMatch(detail -> "1".equals(detail.getAuditStatus()));
                            if (frshAllApproved) {  // 如果所有数据项的auditStatus都为1，则说明所有老师都审批通过
                                // 那么就更新父项目流程节点为1的数据的auditStatus为1
                                TItemAudit parentAudit = new TItemAudit();
                                parentAudit.setItemId(tItemInfo.getId());
                                parentAudit.setFlow(1);
                                List<TItemAudit> parentAudits = tItemAuditMapper.selectTItemAuditList(parentAudit);
                                if (parentAudits != null && !parentAudits.isEmpty()) {
                                    for (TItemAudit audit : parentAudits) {
                                        audit.setAuditStatus("1");
                                        audit.setReason("所有学院老师审批通过");
                                        audit.setCreateTime(DateUtils.getNowDate());
                                        tItemAuditMapper.updateTItemAudit(audit);
                                    }
                                }
                            }
                        } else if (tItemAudit.getAuditStatus().equals("2") || tItemAudit.getAuditStatus().equals("3")) {  // 处理拒绝的情况
                            TItemRollback tItemRollback = new TItemRollback();
                            tItemRollback.setItemId(tItemAudit.getItemId());
                            tItemRollback.setFlow(flow);
                            tItemRollback.setPerson(tItemAudit.getAuditBy());
                            tItemRollback.setTime(DateUtils.getNowDate());
                            tItemRollback.setReason(tItemAudit.getReason());
                            tItemRollback.setAuditDeptName(userDeptName);
                            List<TItemRollback> listRollBack = new ArrayList<>();
                            listRollBack.add(tItemRollback);
                            tItemRollbackMapper.insertTItemRollbackBatch(listRollBack);

                            if (tItemAudit.getAuditStatus().equals("2")) {

                                // 遍历所有老师审核记录进行状态更新
                                for (TItemAuditDetail itemAuditDetail : auditDetails) {
                                    // 处理其他流程节点
                                    itemAuditDetail.setAuditStatus("3");
                                    itemAuditDetail.setReason("待" + itemAuditDetail.getAuditDeptName() + "老师审批");
                                    tItemAuditDetailMapper.updateTItemAuditDetail(itemAuditDetail);
                                }

                                // 接着更新父项目流程节点为1的数据的审核原因
                                TItemAudit parentAudit = new TItemAudit();
                                parentAudit.setItemId(tItemInfo.getId());
                                parentAudit.setFlow(1);

                                List<TItemAudit> parentAudits = tItemAuditMapper.selectTItemAuditList(parentAudit);
                                if (parentAudits != null && !parentAudits.isEmpty()) {
                                    for (TItemAudit audit : parentAudits) {
                                        audit.setAuditStatus("3");
                                        audit.setReason("待老师重新发起审批");
                                        tItemAuditMapper.updateTItemAudit(audit);
                                    }
                                }
                                // 5.4 拒绝后重置项目流程到初始状态
                                tItemInfo.setFlow(0);
                            }else{
                                tItemInfo.setFlow(101);
                            }
                            itemInfoMapper.updateTItemInfo(tItemInfo);
                        }
                    }
                    break;
                case 2:  // 处理归口管理部门领导审核
                    // 获取项目所有审核记录
                    // 查询该项目所有的审核记录，用于后续批量更新
                    TItemAudit tItemAudit1 = new TItemAudit();
                    tItemAudit1.setItemId(tItemAudit.getItemId());
                    List<TItemAudit> list = tItemAuditMapper.selectTItemAuditList(tItemAudit1);

                    if (tItemAudit.getAuditStatus().equals("1")) {  // 处理审核通过的情况（审核状态为1）
                        // 如果当前流程节点小于11，则进入下一个流程节点
                        if (flow < 11) {
                            flow = flow + 1;
                            tItemInfo.setFlow(flow);
                            itemInfoMapper.updateTItemInfo(tItemInfo);
                        }

                        // 更新当前流程节点的审核记录
                        for (TItemAudit itemAudit : list) {
                            if (Objects.equals(itemAudit.getFlow(), flow)) {
                                itemAudit.setAuditStatus("1");  // 设置为已通过状态
                                itemAudit.setAuditBy(tItemAudit.getAuditBy());  // 设置审核意见
                                itemAudit.setReason(tItemAudit.getReason());  // 设置审核意见
                                itemAudit.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                                tItemAuditMapper.updateTItemAudit(itemAudit);
                            }
                        }
                    }
                    // 处理审核拒绝的情况（审核状态为2）
                    else if (tItemAudit.getAuditStatus().equals("2") || tItemAudit.getAuditStatus().equals("3")) {
                        TItemRollback tItemRollback = new TItemRollback();
                        tItemRollback.setItemId(tItemAudit.getItemId());
                        tItemRollback.setFlow(flow);
                        tItemRollback.setPerson(tItemAudit.getAuditBy());
                        tItemRollback.setTime(DateUtils.getNowDate());
                        tItemRollback.setReason(tItemAudit.getReason());
                        tItemRollback.setAuditDeptName(userDeptName);
                        List<TItemRollback> listRollBack = new ArrayList<>();
                        listRollBack.add(tItemRollback);
                        tItemRollbackMapper.insertTItemRollbackBatch(listRollBack);

                        if (tItemAudit.getAuditStatus().equals("2")) {
                            // 遍历所有审核记录进行状态更新
                            for (TItemAudit itemAudit : list) {
                                // 处理其他流程节点
                                itemAudit.setAuditStatus("3");
                                // 根据不同的流程节点设置不同的拒绝原因
                                switch (itemAudit.getFlow()) {
                                    case 1:
                                        itemAudit.setReason("待老师重新发起审批");
                                        break;
                                    case 2:
                                        itemAudit.setReason("待" + itemAudit.getAuditDeptName() + "领导审核");
                                        break;
                                    default:
                                        break;
                                }
                                tItemAuditMapper.updateTItemAudit(itemAudit);
                            }
                            // 拒绝后重置项目流程到初始状态
                            tItemInfo.setFlow(0);
                        } else {
                            // 拒绝后重置项目流程到初始状态
                            tItemInfo.setFlow(101);
                        }

                        itemInfoMapper.updateTItemInfo(tItemInfo);
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 单部门申报
            // 4. 获取项目所有审核记录
            // 查询该项目所有的审核记录，用于后续批量更新
            TItemAudit tItemAudit1 = new TItemAudit();
            tItemAudit1.setItemId(tItemAudit.getItemId());
            List<TItemAudit> list = tItemAuditMapper.selectTItemAuditList(tItemAudit1);

            // 5. 处理审核拒绝的情况（审核状态为2）
            if (tItemAudit.getAuditStatus().equals("2") || tItemAudit.getAuditStatus().equals("3")) {
                TItemRollback tItemRollback = new TItemRollback();
                tItemRollback.setItemId(tItemAudit.getItemId());
                tItemRollback.setFlow(flow);
                tItemRollback.setPerson(tItemAudit.getAuditBy());
                tItemRollback.setTime(DateUtils.getNowDate());
                tItemRollback.setReason(tItemAudit.getReason());
                tItemRollback.setAuditDeptName(userDeptName);
                List<TItemRollback> listRollBack = new ArrayList<>();
                listRollBack.add(tItemRollback);
                tItemRollbackMapper.insertTItemRollbackBatch(listRollBack);

                if (tItemAudit.getAuditStatus().equals("2")) {
                    // 5.1 遍历所有审核记录进行状态更新
                    for (TItemAudit itemAudit : list) {
                        // 5.3 处理其他流程节点
                        itemAudit.setAuditStatus("3");
                        // 根据不同的流程节点设置不同的拒绝原因
                        switch (itemAudit.getFlow()) {
                            case 1:
                                itemAudit.setReason("待老师重新发起审批");
                                break;
                            default:
                                break;
                        }
                        tItemAuditMapper.updateTItemAudit(itemAudit);
                    }
                    // 5.4 拒绝后重置项目流程到初始状态
                    tItemInfo.setFlow(0);
                } else {
                    // 5.4 拒绝后重置项目流程到初始状态
                    tItemInfo.setFlow(101);
                }

                itemInfoMapper.updateTItemInfo(tItemInfo);
            } else if (tItemAudit.getAuditStatus().equals("1")) {  // 6. 处理审核通过的情况（审核状态为1）
                // 6.1 如果当前流程节点小于11，则进入下一个流程节点
                if (flow < 11) {
                    flow = flow + 1;
                    tItemInfo.setFlow(flow);
                    itemInfoMapper.updateTItemInfo(tItemInfo);
                }
                // 6.2 更新当前流程节点的审核记录
                for (TItemAudit itemAudit : list) {
                    if (Objects.equals(itemAudit.getFlow(), flow)) {
                        itemAudit.setAuditStatus("1");  // 设置为已通过状态
                        itemAudit.setAuditBy(tItemAudit.getAuditBy());  // 设置审核意见
                        itemAudit.setReason(tItemAudit.getReason());  // 设置审核意见
                        itemAudit.setCreateTime(DateUtils.getNowDate());  // 设置审核时间
                        tItemAuditMapper.updateTItemAudit(itemAudit);
                    }
                }
            }
        }

        return AjaxResult.success();
    }

    /**
     * 修改项目审核信息
     */
    // @PreAuthorize("@ss.hasPermi('item:audit:edit')")
    @Log(title = "项目审核信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemAudit tItemAudit) {
        return toAjax(tItemAuditService.updateTItemAudit(tItemAudit));
    }

    /**
     * 删除项目审核信息
     */
    @PreAuthorize("@ss.hasPermi('item:audit:remove')")
    @Log(title = "项目审核信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tItemAuditService.deleteTItemAuditByIds(ids));
    }
}
