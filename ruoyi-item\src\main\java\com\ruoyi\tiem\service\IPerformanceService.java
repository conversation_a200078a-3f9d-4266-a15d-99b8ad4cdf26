package com.ruoyi.tiem.service;

import java.util.List;
import com.ruoyi.tiem.domain.Performance;

/**
 * 项目绩效信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IPerformanceService 
{
    /**
     * 查询项目绩效信息
     * 
     * @param id 项目绩效信息主键
     * @return 项目绩效信息
     */
    public Performance selectPerformanceById(Long id);

    /**
     * 查询项目绩效信息列表
     * 
     * @param performance 项目绩效信息
     * @return 项目绩效信息集合
     */
    public List<Performance> selectPerformanceList(Performance performance);

    /**
     * 新增项目绩效信息
     * 
     * @param performance 项目绩效信息
     * @return 结果
     */
    public int insertPerformance(Performance performance);

    /**
     * 修改项目绩效信息
     * 
     * @param performance 项目绩效信息
     * @return 结果
     */
    public int updatePerformance(Performance performance);

    /**
     * 批量删除项目绩效信息
     * 
     * @param ids 需要删除的项目绩效信息主键集合
     * @return 结果
     */
    public int deletePerformanceByIds(Long[] ids);

    /**
     * 删除项目绩效信息信息
     * 
     * @param id 项目绩效信息主键
     * @return 结果
     */
    public int deletePerformanceById(Long id);
}
