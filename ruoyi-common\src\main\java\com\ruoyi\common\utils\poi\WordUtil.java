package com.ruoyi.common.utils.poi;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcBorders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.Docx4J;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

/**
 * WordUtil 工具类
 * <p>
 * 提供基于Apache POI和Jsoup的Word文档（.docx）模板填充、表格插入、富文本写入、PDF转换等功能。
 * 适用于将数据动态填充到Word模板、生成报表、导出PDF等场景。
 * </p>
 *
 * <ul>
 *   <li>支持段落和表格的占位符（${key}）替换</li>
 *   <li>支持表格数据批量插入</li>
 *   <li>支持HTML富文本内容写入Word</li>
 *   <li>支持docx转pdf</li>
 * </ul>
 *
 * <AUTHOR>
public class WordUtil {


    /**
     * 根据模板生成新word文档，支持段落和表格的占位符替换及表格数据插入。
     *
     * @param inputUrl  模板文件路径（.docx）
     * @param outputUrl 生成的新文档路径（.docx）
     * @param textMap   需要替换的占位符及其对应值（如{"itemName":"南航第一食堂改造"}）
     * @param tableList 需要插入的表格数据集合（每个List<String[]>为一张表的数据）
     * @return 生成成功返回true，失败返回false
     */
    public static boolean changWord(String inputUrl, String outputUrl,
                                    Map<String, String> textMap, List<List<String[]>> tableList) {
        boolean changeFlag = true;
        try {
            // 1. 读取模板文档
            XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(inputUrl));
            // 2. 替换段落中的占位符
            WordUtil.changeText(document, textMap);
            // 3. 替换表格中的占位符及插入表格数据
            WordUtil.changeTable(document, textMap, tableList);
            // 4. 写出新文档
            File file = new File(outputUrl);
            try (FileOutputStream stream = new FileOutputStream(file)) {
                document.write(stream);
            }
            document.close(); // 释放资源
        } catch (Exception e) {
            e.printStackTrace();
            changeFlag = false;
        }
        return changeFlag;
    }

    /**
     * 根据模板生成新word文档，支持段落和表格的占位符替换及表格数据插入，并添加审核信息表格。
     *
     * @param inputUrl  模板文件路径（.docx）
     * @param outputUrl 生成的新文档路径（.docx）
     * @param textMap   需要替换的占位符及其对应值（如{"itemName":"南航第一食堂改造"}）
     * @param tableList 需要插入的表格数据集合（每个List<String[]>为一张表的数据）
     * @param flowList  审核流程数据
     * @return 生成成功返回true，失败返回false
     */
    public static boolean changWordWithAudit(String inputUrl, String outputUrl,
                                    Map<String, String> textMap, List<List<String[]>> tableList, List<Map<String, Object>> flowList) {
        boolean changeFlag = true;
        try {
            // 1. 读取模板文档
            XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(inputUrl));
            // 2. 替换段落中的占位符
            WordUtil.changeText(document, textMap);
            // 3. 替换表格中的占位符及插入表格数据
            WordUtil.changeTable(document, textMap, tableList);
            // 4. 添加审核信息表格
            WordUtil.addAuditTable(document, flowList);
            // 5. 写出新文档
            File file = new File(outputUrl);
            try (FileOutputStream stream = new FileOutputStream(file)) {
                document.write(stream);
            }
        } catch (Exception e) {
            e.printStackTrace();
            changeFlag = false;
        }
        return changeFlag;
    }

    /**
     * 替换文档中所有段落的占位符（${key}）为指定内容，支持HTML富文本。
     * 本方法遍历文档中的所有段落和文本运行(run)，查找并替换包含占位符的文本。
     * 替换时会将原有的文本运行分割成三部分：占位符前文本、替换内容和占位符后文本，
     * 并分别创建新的文本运行进行替换，保持原有的格式风格。
     *
     * @param document docx文档对象，需要进行占位符替换的Word文档
     * @param textMap  占位符及其替换内容的映射，key为不含${}的占位符名称，value为替换内容
     *                 如果value包含HTML标签，将被识别为富文本并保留格式
     */
    public static void changeText(XWPFDocument document, Map<String, String> textMap) {
        // 获取文档中的所有段落
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            // 获取段落中的所有文本运行(run)
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) continue; // 跳过没有文本运行的段落
            
            // 遍历所有占位符键值对
            for (Map.Entry<String, String> entry : textMap.entrySet()) {
                // 构造完整的占位符格式 ${key}
                String key = "${" + entry.getKey() + "}";
                
                // 遍历段落中的每个文本运行
                for (int i = 0; i < runs.size(); i++) {
                    String runText = runs.get(i).toString();
                    if (runText.contains(key)) {
                        // 拆分占位符前后文本
                        String before = runText.substring(0, runText.indexOf(key));
                        String after = runText.substring(runText.indexOf(key) + key.length());
                        
                        // 移除原有文本运行(因为要分成多个运行替换)
                        paragraph.removeRun(i);
                        int insertPos = i; // 新运行的插入位置
                        
                        // 1. 插入占位符前的文本(如果存在)
                        if (!before.isEmpty()) {
                            XWPFRun beforeRun = paragraph.insertNewRun(insertPos++);
                            beforeRun.setText(before, 0);
                            beforeRun.setFontSize(16);
                            try {
                                // 设置字号为16pt (32半点)
                                beforeRun.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(32));
                            } catch (Exception e) {
                                // 忽略样式设置异常，不影响替换功能
                            }
                        }
                        
                        // 2. 插入替换内容(根据是否为HTML内容采用不同处理)
                        String value = entry.getValue();
                        if (isHtmlContent(value)) {
                            // HTML内容需要特殊处理以保留富文本格式
                            insertHtmlToParagraph(paragraph, insertPos, value);
                            insertPos++; // HTML内容只计为一个位置增量
                        } else {
                            // 普通文本直接插入新运行
                            XWPFRun valueRun = paragraph.insertNewRun(insertPos++);
                            valueRun.setText(value, 0);
                            valueRun.setFontSize(16);
                            try {
                                // 设置字号为16pt
                                valueRun.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(32));
                            } catch (Exception e) {
                                // 忽略样式异常
                            }
                        }
                        
                        // 3. 插入占位符后的文本(如果存在)
                        if (!after.isEmpty()) {
                            XWPFRun afterRun = paragraph.insertNewRun(insertPos);
                            afterRun.setText(after, 0);
                            afterRun.setFontSize(16);
                            try {
                                // 设置字号为16pt
                                afterRun.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(32));
                            } catch (Exception e) {
                                // 忽略样式异常
                            }
                        }
                        
                        break; // 每次只处理找到的第一个匹配项，处理完后中断内层循环
                    }
                }
            }
        }
    }

    /**
     * 替换所有表格中的占位符，并按需插入表格数据。
     *
     * @param document  docx文档对象
     * @param textMap   占位符及其替换内容
     * @param tableList 需要插入的表格数据集合
     */
    public static void changeTable(XWPFDocument document, Map<String, String> textMap,
                                   List<List<String[]>> tableList) {
        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            // 替换表格内占位符
            List<XWPFTableRow> rows = tables.get(i).getRows();
            eachTable(rows, textMap);
            // 按表格索引插入数据（可根据实际模板调整）
            if (i == 3) { // 项目支出预算明细表
                insertTable(tables.get(i), tableList.get(0), 10, 6, 2);
            } else if (i == 4) { // 项目预期绩效目标申报表
                insertTable(tables.get(i), tableList.get(1), 10, 8, 1);
            }
        }
    }

    /**
     * 遍历表格所有单元格，替换其中的占位符。
     *
     * @param rows    表格行集合
     * @param textMap 占位符及其替换内容
     */
    public static void eachTable(List<XWPFTableRow> rows, Map<String, String> textMap) {
        for (XWPFTableRow row : rows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                List<XWPFParagraph> paragraphs = cell.getParagraphs();
                for (XWPFParagraph paragraph : paragraphs) {
                    List<XWPFRun> runs = paragraph.getRuns();
                    if (runs == null || runs.isEmpty()) continue;
                    for (Map.Entry<String, String> entry : textMap.entrySet()) {
                        String key = "${" + entry.getKey() + "}";
                        for (int i = 0; i < runs.size(); i++) {
                            String runText = runs.get(i).toString();
                            if (runText.contains(key)) {
                                String before = runText.substring(0, runText.indexOf(key));
                                String after = runText.substring(runText.indexOf(key) + key.length());
                                paragraph.removeRun(i);
                                int insertPos = i;
                                if (!before.isEmpty()) {
                                    XWPFRun beforeRun = paragraph.insertNewRun(insertPos++);
                                    beforeRun.setText(before, 0);
                                    beforeRun.setFontSize(16);
                                    try {
                                        beforeRun.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(32));
                                    } catch (Exception e) {
                                        // 忽略样式异常
                                    }
                                }
                                String value = entry.getValue();
                                if (isHtmlContent(value)) {
                                    insertHtmlToParagraph(paragraph, insertPos, value);
                                    insertPos++;
                                } else {
                                    XWPFRun valueRun = paragraph.insertNewRun(insertPos++);
                                    valueRun.setText(value, 0);
                                    valueRun.setFontSize(16);
                                    try {
                                        valueRun.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(32));
                                    } catch (Exception e) {
                                        // 忽略样式异常
                                    }
                                }
                                if (!after.isEmpty()) {
                                    XWPFRun afterRun = paragraph.insertNewRun(insertPos);
                                    afterRun.setText(after, 0);
                                    afterRun.setFontSize(16);
                                    try {
                                        afterRun.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(32));
                                    } catch (Exception e) {
                                        // 忽略样式异常
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 为表格插入数据，行数不够时自动添加新行，保持样式一致。
     *
     * @param table       目标表格对象
     * @param tableList   插入的数据集合（每个String[]为一行）
     * @param lines       表格预留的行数
     * @param startRow    插入数据的起始行号（0基）
     * @param startColumn 插入数据的起始列号（0基）
     */
    public static void insertTable(XWPFTable table, List<String[]> tableList, Integer lines, Integer startRow, Integer startColumn) {
        // 若数据行数大于预留行数，则补充新行
        if (tableList.size() > lines) {
            for (int i = 0; i < tableList.size() - lines; i++) {
                insertRow(table, startRow, tableList.size() - lines + startRow);
            }
        }
        // 填充数据到表格
        List<XWPFTableRow> rows = table.getRows();
        for (int i = startRow; i < tableList.size() + startRow; i++) {
            XWPFTableRow newRow = table.getRow(i);
            List<XWPFTableCell> cells = newRow.getTableCells();
            for (int j = startColumn; j < cells.size(); j++) {
                XWPFTableCell cell = cells.get(j);
                CTTc ctTc = cell.getCTTc();
                CTP ctp = (ctTc.sizeOfPArray() == 0) ? ctTc.addNewP() : ctTc.getPArray(0);
                XWPFParagraph paragraph = cell.getParagraph(ctp);
                XWPFRun run = paragraph.createRun();
                run.setFontFamily("宋体");
                run.setFontSize(12);
                run.setText(tableList.get(i - startRow)[j - startColumn]);
            }
        }
    }

    /**
     * 判断文本中是否包含占位符（$）。
     *
     * @param text 文本内容
     * @return 包含返回true，不包含返回false
     */
    public static boolean checkText(String text) {
        return text != null && text.contains("$");
    }

    /**
     * 在Word表格指定位置插入一行，并复制指定行的样式。
     *
     * @param table        目标表格
     * @param copyrowIndex 样式来源行的索引
     * @param newrowIndex  新行插入位置索引
     */
    public static void insertRow(XWPFTable table, int copyrowIndex, int newrowIndex) {
        XWPFTableRow targetRow = table.insertNewTableRow(newrowIndex);
        XWPFTableRow copyRow = table.getRow(copyrowIndex);
        // 复制行属性
        targetRow.getCtRow().setTrPr(copyRow.getCtRow().getTrPr());
        List<XWPFTableCell> copyCells = copyRow.getTableCells();
        for (int i = 0; i < copyCells.size(); i++) {
            XWPFTableCell copyCell = copyCells.get(i);
            XWPFTableCell targetCell = targetRow.addNewTableCell();
            targetCell.getCTTc().setTcPr(copyCell.getCTTc().getTcPr());
            if (copyCell.getParagraphs() != null && !copyCell.getParagraphs().isEmpty()) {
                targetCell.getParagraphs().get(0).getCTP().setPPr(copyCell.getParagraphs().get(0).getCTP().getPPr());
                if (copyCell.getParagraphs().get(0).getRuns() != null
                        && !copyCell.getParagraphs().get(0).getRuns().isEmpty()) {
                    XWPFRun cellR = targetCell.getParagraphs().get(0).createRun();
                    cellR.setBold(copyCell.getParagraphs().get(0).getRuns().get(0).isBold());
                }
            }
        }
    }

    /**
     * 匹配模板占位符与传入信息集合，返回替换后的值。
     *
     * @param value   模板中的占位符
     * @param textMap 占位符及其替换内容
     * @return 替换后的内容，未匹配则返回空字符串
     */
    public static String changeValue(String value, Map<String, String> textMap) {
        for (Entry<String, String> textSet : textMap.entrySet()) {
            String key = "${" + textSet.getKey() + "}";
            if (value.contains(key)) {
                value = textSet.getValue();
            }
        }
        // 未匹配到则清空
        if (checkText(value)) {
            value = "";
        }
        return value;
    }

    /**
     * 将HTML富文本内容写入Word段落，支持加粗、斜体、下划线、删除线、字号等样式。
     *
     * @param html      HTML富文本内容
     * @param paragraph 目标段落
     */
    public static void writeHtmlToWord(String html, XWPFParagraph paragraph) {
        Document doc = Jsoup.parse(html);
        for (Node node : doc.body().childNodes()) {
            appendNodeToParagraph(node, paragraph, null);
        }
    }

    /**
     * 递归将HTML节点内容及样式追加到Word段落。
     *
     * @param node        HTML节点
     * @param paragraph   目标段落
     * @param parentStyle 父节点样式
     */
    private static void appendNodeToParagraph(Node node, XWPFParagraph paragraph, StyleState parentStyle) {
        StyleState style = parentStyle == null ? new StyleState() : parentStyle.copy();
        if (node instanceof TextNode) {
            String text = ((TextNode) node).text();
            if (!text.isEmpty()) {
                XWPFRun run = paragraph.createRun();
                run.setText(text, 0);
                if (style.bold) run.setBold(true);
                if (style.italic) run.setItalic(true);
                if (style.underline) run.setUnderline(org.apache.poi.xwpf.usermodel.UnderlinePatterns.SINGLE);
                if (style.strike) run.setStrikeThrough(true);
                if (style.fontSize > 0) run.setFontSize(style.fontSize);
            }
            return;
        }
        if (node instanceof Element) {
            Element el = (Element) node;
            String tag = el.tagName().toLowerCase();
            // 块级标签换行
            if ("p".equals(tag) || "div".equals(tag)) {
                paragraph = paragraph.getDocument().createParagraph();
            }
            if ("br".equals(tag)) {
                paragraph = paragraph.getDocument().createParagraph();
            }
            if ("strong".equals(tag) || "b".equals(tag)) style.bold = true;
            if ("em".equals(tag) || "i".equals(tag)) style.italic = true;
            if ("u".equals(tag)) style.underline = true;
            if ("s".equals(tag) || "del".equals(tag)) style.strike = true;
            if ("span".equals(tag)) {
                String styleAttr = el.attr("style");
                if (styleAttr != null && !styleAttr.isEmpty()) {
                    java.util.regex.Matcher m = java.util.regex.Pattern.compile("font-size\\s*:\\s*([\\d.]+)px").matcher(styleAttr);
                    if (m.find()) {
                        style.fontSize = (int) Math.round(Double.parseDouble(m.group(1)));
                    }
                }
            }
            for (Node child : el.childNodes()) {
                appendNodeToParagraph(child, paragraph, style);
            }
        }
    }

    /**
     * 在指定位置插入HTML富文本内容到段落。
     *
     * @param paragraph 目标段落
     * @param insertPos 插入位置
     * @param html      HTML富文本内容
     */
    private static void insertHtmlToParagraph(XWPFParagraph paragraph, int insertPos, String html) {
        Document doc = Jsoup.parse(html);
        for (Node node : doc.body().childNodes()) {
            appendNodeToParagraphAt(paragraph, insertPos, node, null);
            insertPos++;
        }
    }

    /**
     * 在指定位置插入富文本run。
     *
     * @param paragraph  目标段落
     * @param insertPos  插入位置
     * @param node       HTML节点
     * @param parentStyle 父节点样式
     */
    private static void appendNodeToParagraphAt(XWPFParagraph paragraph, int insertPos, Node node, StyleState parentStyle) {
        // 继承父节点的样式（如加粗、斜体、字号等），保证HTML标签嵌套时样式能正确叠加和传递
        StyleState style = parentStyle == null ? new StyleState() : parentStyle.copy();
        if (node instanceof TextNode) {
            String text = ((TextNode) node).text();
            if (!text.isEmpty()) {
                XWPFRun run = paragraph.insertNewRun(insertPos);
                run.setText(text, 0);
                if (style.bold) run.setBold(true);
                if (style.italic) run.setItalic(true);
                if (style.underline) run.setUnderline(org.apache.poi.xwpf.usermodel.UnderlinePatterns.SINGLE);
                if (style.strike) run.setStrikeThrough(true);
                // 设置字号：如未指定则用默认值16
                int fontSize = style.fontSize > 0 ? style.fontSize : 16;
                run.setFontSize(fontSize);
                try {
                    run.getCTR().getRPr().addNewSz().setVal(BigInteger.valueOf(fontSize * 2));
                } catch (Exception e) {
                    // 忽略样式异常
                }
            }
            return;
        }
        if (node instanceof Element) {
            Element el = (Element) node;
            String tag = el.tagName().toLowerCase();
            if ("strong".equals(tag) || "b".equals(tag)) style.bold = true;
            if ("em".equals(tag) || "i".equals(tag)) style.italic = true;
            if ("u".equals(tag)) style.underline = true;
            if ("s".equals(tag) || "del".equals(tag)) style.strike = true;
            if ("span".equals(tag)) {
                String styleAttr = el.attr("style");
                if (styleAttr != null && !styleAttr.isEmpty()) {
                    java.util.regex.Matcher m = java.util.regex.Pattern.compile("font-size\\s*:\\s*([\\d.]+)px").matcher(styleAttr);
                    if (m.find()) {
                        style.fontSize = (int) Math.round(Double.parseDouble(m.group(1)));
                    }
                }
            }
            for (Node child : el.childNodes()) {
                appendNodeToParagraphAt(paragraph, insertPos, child, style);
                insertPos++;
            }
        }
    }

    /**
     * 样式状态对象，记录富文本递归处理时的样式继承。
     */
    private static class StyleState {
        boolean bold = false;
        boolean italic = false;
        boolean underline = false;
        boolean strike = false;
        int fontSize = -1;
        StyleState copy() {
            StyleState s = new StyleState();
            s.bold = this.bold;
            s.italic = this.italic;
            s.underline = this.underline;
            s.strike = this.strike;
            s.fontSize = this.fontSize;
            return s;
        }
    }

    /**
     * 将docx文件转换为pdf文件（基于docx4j）。
     *
     * @param docxPath 输入docx文件路径
     * @param pdfPath  输出pdf文件路径
     * @return 转换成功返回true，失败返回false
     */
    public static boolean convertDocxToPdf(String docxPath, String pdfPath) {
        boolean success = true;
        try {
            // WordprocessingMLPackage加载docx
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(new java.io.File(docxPath));
            java.io.OutputStream os = new java.io.FileOutputStream(pdfPath);
            Docx4J.toPDF(wordMLPackage, os);
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
            success = false;
        }
        return success;
    }

    /**
     * 判断字符串是否为HTML内容（简单标签判断）。
     *
     * @param str 字符串
     * @return 是HTML内容返回true，否则false
     */
    private static boolean isHtmlContent(String str) {
        if (str == null) return false;
        return str.contains("<p") || str.contains("<div") || str.contains("<span") || str.contains("<br")
            || str.contains("<strong") || str.contains("<em") || str.contains("<u")
            || str.contains("<s") || str.contains("<b") || str.contains("<i");
    }

    /**
     * 在Word文档末尾添加审核信息表格
     *
     * @param document Word文档对象
     * @param flowList 审核流程数据
     */
    public static void addAuditTable(XWPFDocument document, List<Map<String, Object>> flowList) {
        if (flowList == null || flowList.isEmpty()) {
            return;
        }

        try {
            // 添加一个空段落作为间隔
            XWPFParagraph spaceParagraph = document.createParagraph();
            spaceParagraph.createRun().addBreak();

            // 根据审核流程数据动态创建表格
            for (Map<String, Object> flow : flowList) {
                // 创建表格 (1列，高度根据内容动态调整)
                XWPFTable table = document.createTable(1, 1);

                // 设置表格样式
                table.setWidth("100%");

                // 获取第一行第一列
                XWPFTableRow row = table.getRow(0);
                XWPFTableCell cell = row.getCell(0);

                // 设置单元格边框
                setCellBorders(cell);

                // 构建审核信息内容
                StringBuilder content = new StringBuilder();

                // 获取审核部门名称和意见
                String auditDeptName = (String) flow.get("auditDeptName");
                String reason = (String) flow.get("reason");

                // 根据不同的审核类型构建不同的标题
                String title = getAuditTitle(flow);
                content.append(title).append("：").append(reason);

                // 如果有多学院申报的情况，添加标注
                if (auditDeptName != null && auditDeptName.contains(",")) {
                    content.append("    （多学院申报时）");
                }

                // 添加审核人和时间信息
                content.append("\n\n");
                String auditBy = (String) flow.get("auditBy");
                Object createTimeObj = flow.get("createTime");
                String createTime = "";
                if (createTimeObj != null) {
                    // 处理时间格式，提取年月日
                    String timeStr = createTimeObj.toString();
                    if (timeStr.length() >= 10) {
                        String dateStr = timeStr.substring(0, 10);
                        String[] dateParts = dateStr.split("-");
                        if (dateParts.length == 3) {
                            createTime = dateParts[0] + "年" + dateParts[1] + "月" + dateParts[2] + "日";
                        }
                    }
                }

                content.append("负责人：").append(auditBy != null ? auditBy : "")
                       .append("                    ").append(createTime);

                // 如果有专业审核排序信息
                if (flow.containsKey("majorSort") && flow.get("majorSort") != null) {
                    content.append("\n专业审核排序：").append(flow.get("majorSort"));
                }

                // 如果有综合评审排序信息
                if (flow.containsKey("compoSort") && flow.get("compoSort") != null) {
                    content.append("\n综合评审意见：").append(flow.get("compoSort"));
                }

                // 如果有入库预算和意见
                if (flow.containsKey("storeBudget") && flow.get("storeBudget") != null) {
                    content.append("\n入库预算：").append(flow.get("storeBudget"));
                }
                if (flow.containsKey("storeOpinion") && flow.get("storeOpinion") != null) {
                    content.append("\n入库意见：").append(flow.get("storeOpinion"));
                }

                // 如果有执行计划排序
                if (flow.containsKey("planSort") && flow.get("planSort") != null) {
                    content.append("\n执行计划排序：").append(flow.get("planSort"));
                }

                // 如果有附件信息
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> fileInfoList = (List<Map<String, Object>>) flow.get("fileInfoList");
                if (fileInfoList != null && !fileInfoList.isEmpty()) {
                    content.append("\n附件：");
                    for (int i = 0; i < fileInfoList.size(); i++) {
                        Map<String, Object> fileInfo = fileInfoList.get(i);
                        String originalFileName = (String) fileInfo.get("originalFileName");
                        if (originalFileName != null) {
                            if (i > 0) content.append("、");
                            content.append(originalFileName);
                        }
                    }
                    content.append("  （下载时此行不显示）");
                }

                // 设置单元格内容
                XWPFParagraph paragraph = cell.getParagraphs().get(0);
                XWPFRun run = paragraph.createRun();
                run.setFontFamily("宋体");
                run.setFontSize(12);
                run.setText(content.toString());

                // 设置段落对齐方式
                paragraph.setAlignment(ParagraphAlignment.LEFT);

                // 添加表格间的间隔
                XWPFParagraph spaceParagraph2 = document.createParagraph();
                spaceParagraph2.createRun().addBreak();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据审核流程信息获取审核标题
     */
    private static String getAuditTitle(Map<String, Object> flow) {
        Integer flowNum = (Integer) flow.get("flow");
        String auditDeptName = (String) flow.get("auditDeptName");

        if (flowNum == null) {
            return auditDeptName + "意见";
        }

        switch (flowNum) {
            case 1:
                return "申报单位意见";
            case 2:
                return auditDeptName + "意见";
            case 3:
                return "归口管理单位意见";
            case 4:
                return "专业审核排序";
            case 5:
                return "综合评审意见";
            case 6:
                return "入库评审通过";
            case 7:
                return "执行计划排序通过";
            default:
                return auditDeptName + "意见";
        }
    }

    /**
     * 设置单元格边框
     */
    private static void setCellBorders(XWPFTableCell cell) {
        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }

        CTTcBorders borders = tcPr.getTcBorders();
        if (borders == null) {
            borders = tcPr.addNewTcBorders();
        }

        // 设置边框样式
        CTBorder border = CTBorder.Factory.newInstance();
        border.setVal(STBorder.SINGLE);
        border.setSz(BigInteger.valueOf(4));
        border.setColor("000000");

        borders.setTop(border);
        borders.setBottom(border);
        borders.setLeft(border);
        borders.setRight(border);
    }
}