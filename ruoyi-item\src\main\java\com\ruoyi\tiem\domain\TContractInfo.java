package com.ruoyi.tiem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 合同信息对象 t_contract_info
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
public class TContractInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称")
    private String contractName;

    /**
     * 合同文件id
     */
    @Excel(name = "合同文件id")
    private String fileIds;

    /**
     * 关联项目id
     */
    @Excel(name = "关联项目id")
    private Long itemIds;

    /**
     * 公告状态（0正常 1关闭）
     */
    @Excel(name = "公告状态", readConverterExp = "0=正常,1=关闭")
    private String status;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    private String contractNumber;

    /**
     * 合同总金额
     */
    @Excel(name = "合同总金额")
    private BigDecimal totalAmount;

    /**
     * 截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 供应商
     */
    @Excel(name = "供应商")
    private String supplier;

    /**
     * 结算金额
     */
    @Excel(name = "结算金额")
    private BigDecimal calculateAmount;

    /**
     * 送审金额
     */
    @Excel(name = "送审金额")
    private BigDecimal checkAmount;

    /**
     * 累计开票金额
     */
    @Excel(name = "累计开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 累计付款金额
     */
    @Excel(name = "累计付款金额")
    private BigDecimal paymentAmount;

    /**
     * 合约规划id
     */
    @Excel(name = "合约规划id")
    private Long planId;

    /**
     * 合约规划金额
     */
    @Excel(name = "合约规划金额")
    private BigDecimal planAmount;

    /**
     * 合同名称
     */
    private String itemName;

    /**
     * 是否政采分类 政采指标、非政采指标
     */
    @Excel(name = "是否政采分类 政采指标、非政采指标")
    private String purchaseType;

    /**
     * 政府预算经济分类
     */
    @Excel(name = "政府预算经济分类")
    private String economyType;

    /**
     * 明细分类
     */
    @Excel(name = "明细分类")
    private String detailType;

    /**
     * 缴纳形式
     */
    @Excel(name = "缴纳形式")
    private String payType;

    /**
     * 缴纳金额
     */
    @Excel(name = "缴纳金额")
    private BigDecimal payAmount;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    private String payPhone;

    /**
     * 合同关联的项目
     */
    private List<TItemInfo> itemInfoList;
}
