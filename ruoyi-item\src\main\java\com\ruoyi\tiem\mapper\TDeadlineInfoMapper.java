package com.ruoyi.tiem.mapper;

import com.ruoyi.tiem.domain.TDeadlineInfo;

import java.util.List;

/**
 * 项目关门节点Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface TDeadlineInfoMapper 
{
    /**
     * 查询项目关门节点
     * 
     * @param id 项目关门节点主键
     * @return 项目关门节点
     */
    public TDeadlineInfo selectTDeadlineInfoById(Long id);

    /**
     * 查询项目关门节点列表
     * 
     * @param tDeadlineInfo 项目关门节点
     * @return 项目关门节点集合
     */
    public List<TDeadlineInfo> selectTDeadlineInfoList(TDeadlineInfo tDeadlineInfo);

    /**
     * 新增项目关门节点
     * 
     * @param tDeadlineInfo 项目关门节点
     * @return 结果
     */
    public int insertTDeadlineInfo(TDeadlineInfo tDeadlineInfo);

    /**
     * 修改项目关门节点
     * 
     * @param tDeadlineInfo 项目关门节点
     * @return 结果
     */
    public int updateTDeadlineInfo(TDeadlineInfo tDeadlineInfo);

    /**
     * 删除项目关门节点
     * 
     * @param id 项目关门节点主键
     * @return 结果
     */
    public int deleteTDeadlineInfoById(Long id);

    /**
     * 批量删除项目关门节点
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTDeadlineInfoByIds(Long[] ids);
}
