package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 年度立项总金额对象 t_project_amount
 * 
 * <AUTHOR>
 * @date 2025-02-28
 */
public class TProjectAmount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 年度 */
    @Excel(name = "年度")
    private String year;

    /** 立项总金额 */
    @Excel(name = "立项总金额")
    private BigDecimal amount;

    /** 状态（1：正常；9删除） */
    @Excel(name = "状态", readConverterExp = "1=：正常；9删除")
    private Integer status;

    /** 项目类别 */
    @Excel(name = "项目类别")
    private String itemCategory;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setYear(String year) 
    {
        this.year = year;
    }

    public String getYear() 
    {
        return year;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public String getItemCategory() {  return itemCategory; }

    public void setItemCategory(String itemCategory) { this.itemCategory = itemCategory; }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("year", getYear())
            .append("amount", getAmount())
            .append("status", getStatus())
            .append("itemCategory", getItemCategory())
            .toString();
    }
}
