//package com.ruoyi.framework.cas.config;
//
//import javax.annotation.PostConstruct;
//import org.jasig.cas.client.authentication.AuthenticationFilter;
//import org.jasig.cas.client.session.SingleSignOutFilter;
//import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
//import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
//import org.jasig.cas.client.validation.Cas30ProxyReceivingTicketValidationFilter;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.Ordered;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
///**
// * 功能描述：CAS集成核心配置类
// * 用于在 Spring Boot 应用中配置 CAS 客户端的相关过滤器和参数，以实现单点登录（SSO）功能
// */
//@Configuration
//public class CasFilterConfig {
//
//    private static final Logger logger = LoggerFactory.getLogger(CasFilterConfig.class);
//
//    @PostConstruct
//    public void init() {
//        System.out.println("====================================");
//        System.out.println("CasFilterConfig 配置类已加载");
//        System.out.println("casUrl = " + casUrl);
//        System.out.println("appUrl = " + appUrl);
//        System.out.println("====================================");
//
//        logger.info("====================================");
//        logger.info("CasFilterConfig 配置类已加载");
//        logger.info("casUrl = {}", casUrl);
//        logger.info("appUrl = {}", appUrl);
//        logger.info("====================================");
//    }
//
//    /**
//     * 需要走cas拦截的地址（/* 所有地址都拦截）
//     */
//    private final static String URL_PATTERN = "/*";
////    private final static String URL_PATTERN = "/cas-disabled-pattern";
//
//
//    /**
//     * 不需要拦截的URL
//     */
//    private final static String[] EXCLUDE_URLS = {"/captchaImage","/login"};
//    // private final static String[] EXCLUDE_URLS = {"/login"};
//
//    /**
//     * 默认的cas地址，防止通过配置信息获取不到
//     */
//    @Value("${cas.server-url:https://authserver.nuaa.edu.cn/authserver}")
//    private String casUrl;
//
//    /**
//     * 应用访问地址（这个地址需要在cas服务端进行配置）
//     */
//    @Value("${app.url:http://10.0.120.43/index}")
//    private String appUrl;
//
//    @Bean
//    public ServletListenerRegistrationBean servletListenerRegistrationBean() {
//        ServletListenerRegistrationBean listenerRegistrationBean = new ServletListenerRegistrationBean();
//        listenerRegistrationBean.setListener(new SingleSignOutHttpSessionListener());
//        listenerRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
//        return listenerRegistrationBean;
//    }
//
//    /**
//     * 单点登录退出
//     *
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean singleSignOutFilter() {
//        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(new SingleSignOutFilter());
//        registrationBean.addUrlPatterns(URL_PATTERN);
//        registrationBean.addInitParameter("casServerUrlPrefix", casUrl + "/logout");
//        registrationBean.setName("CAS Single Sign Out Filter");
//        registrationBean.setOrder(2);
//        return registrationBean;
//    }
//
//    /**
//     * 单点登录认证
//     *
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean AuthenticationFilter() {
//        logger.info("====================================");
//        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(new AuthenticationFilter());
//        registrationBean.addUrlPatterns(URL_PATTERN);
//        registrationBean.setName("CAS Filter");
////        registrationBean.addInitParameter("casServerLoginUrl", casUrl + "/login");
////        registrationBean.addInitParameter("serverName", appUrl);
//
//        //日志查看
//        String casServerLoginUrl = casUrl + "/login";
//        String serverNameValue = appUrl;
//        String ignorePatternValue = String.join("|", EXCLUDE_URLS);
//
//        System.out.println("CAS配置信息 - CAS Server Login URL: " + casServerLoginUrl);
//        System.out.println("CAS配置信息 - Server Name: " + serverNameValue);
//        System.out.println("CAS配置信息 - Ignore Pattern: " + ignorePatternValue);
//
//        logger.info("CAS配置信息 - CAS Server Login URL: {}", casServerLoginUrl);
//        logger.info("CAS配置信息 - Server Name: {}", serverNameValue);
//        logger.info("CAS配置信息 - Ignore Pattern: {}", ignorePatternValue);
//
//        registrationBean.addInitParameter("casServerLoginUrl", casServerLoginUrl);
//        registrationBean.addInitParameter("serverName", serverNameValue);
//        // 配置不需要拦截的URL
//        registrationBean.addInitParameter("ignorePattern", String.join("|", EXCLUDE_URLS));
//        registrationBean.setOrder(3);
//        return registrationBean;
//    }
//
//    /**
//     * 单点登录校验
//     *
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean Cas30ProxyReceivingTicketValidationFilter() {
//        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(new Cas30ProxyReceivingTicketValidationFilter());
//        registrationBean.addUrlPatterns(URL_PATTERN);
//        registrationBean.setName("CAS Validation Filter");
//        registrationBean.addInitParameter("casServerUrlPrefix", casUrl);
//        registrationBean.addInitParameter("serverName", appUrl);
//        // 配置不需要拦截的URL
//        registrationBean.addInitParameter("ignorePattern", String.join("|", EXCLUDE_URLS));
//        registrationBean.setOrder(4);
//        return registrationBean;
//    }
//
//    /**
//     * 单点登录请求包装
//     *
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean httpServletRequestWrapperFilter() {
//        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(new HttpServletRequestWrapperFilter());
//        registrationBean.addUrlPatterns(URL_PATTERN);
//        registrationBean.setName("CAS HttpServletRequest Wrapper Filter");
//        registrationBean.setOrder(5);
//        return registrationBean;
//    }
//}
