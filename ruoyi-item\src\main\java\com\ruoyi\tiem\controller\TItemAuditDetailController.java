package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.TItemAuditDetail;
import com.ruoyi.tiem.service.ITItemAuditDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 多流程审核明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/item/audit_detail")
public class TItemAuditDetailController extends BaseController
{
    @Autowired
    private ITItemAuditDetailService tItemAuditDetailService;

    /**
     * 查询多流程审核明细列表
     */
    @PreAuthorize("@ss.hasPermi('item:audit_detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(TItemAuditDetail tItemAuditDetail)
    {
        startPage();
        List<TItemAuditDetail> list = tItemAuditDetailService.selectTItemAuditDetailList(tItemAuditDetail);
        return getDataTable(list);
    }

    /**
     * 导出多流程审核明细列表
     */
    @PreAuthorize("@ss.hasPermi('item:audit_detail:export')")
    @Log(title = "多流程审核明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemAuditDetail tItemAuditDetail)
    {
        List<TItemAuditDetail> list = tItemAuditDetailService.selectTItemAuditDetailList(tItemAuditDetail);
        ExcelUtil<TItemAuditDetail> util = new ExcelUtil<TItemAuditDetail>(TItemAuditDetail.class);
        util.exportExcel(response, list, "多流程审核明细数据");
    }

    /**
     * 获取多流程审核明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('item:audit_detail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tItemAuditDetailService.selectTItemAuditDetailById(id));
    }

    /**
     * 新增多流程审核明细
     */
    @PreAuthorize("@ss.hasPermi('item:audit_detail:add')")
    @Log(title = "多流程审核明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemAuditDetail tItemAuditDetail)
    {
        return toAjax(tItemAuditDetailService.insertTItemAuditDetail(tItemAuditDetail));
    }

    /**
     * 修改多流程审核明细
     */
    @PreAuthorize("@ss.hasPermi('item:audit_detail:edit')")
    @Log(title = "多流程审核明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemAuditDetail tItemAuditDetail)
    {
        return toAjax(tItemAuditDetailService.updateTItemAuditDetail(tItemAuditDetail));
    }

    /**
     * 删除多流程审核明细
     */
    @PreAuthorize("@ss.hasPermi('item:audit_detail:remove')")
    @Log(title = "多流程审核明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tItemAuditDetailService.deleteTItemAuditDetailByIds(ids));
    }
}
