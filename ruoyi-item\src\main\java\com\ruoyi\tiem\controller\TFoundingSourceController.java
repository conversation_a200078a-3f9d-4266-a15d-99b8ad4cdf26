package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TFoundingSource;
import com.ruoyi.tiem.service.ITFoundingSourceService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目资金来源Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/system/source")
public class TFoundingSourceController extends BaseController
{
    @Autowired
    private ITFoundingSourceService tFoundingSourceService;

    /**
     * 查询项目资金来源列表
     */
//    @PreAuthorize("@ss.hasPermi('system:source:list')")
    @GetMapping("/list")
    public TableDataInfo list(TFoundingSource tFoundingSource)
    {
        startPage();
        List<TFoundingSource> list = tFoundingSourceService.selectTFoundingSourceList(tFoundingSource);
        return getDataTable(list);
    }

    /**
     * 导出项目资金来源列表
     */
//    @PreAuthorize("@ss.hasPermi('system:source:export')")
    @Log(title = "项目资金来源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TFoundingSource tFoundingSource)
    {
        List<TFoundingSource> list = tFoundingSourceService.selectTFoundingSourceList(tFoundingSource);
        ExcelUtil<TFoundingSource> util = new ExcelUtil<TFoundingSource>(TFoundingSource.class);
        util.exportExcel(response, list, "项目资金来源数据");
    }

    /**
     * 获取项目资金来源详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:source:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tFoundingSourceService.selectTFoundingSourceById(id));
    }

    /**
     * 新增项目资金来源
     */
//    @PreAuthorize("@ss.hasPermi('system:source:add')")
    @Log(title = "项目资金来源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TFoundingSource tFoundingSource)
    {
        return toAjax(tFoundingSourceService.insertTFoundingSource(tFoundingSource));
    }

    /**
     * 修改项目资金来源
     */
//    @PreAuthorize("@ss.hasPermi('system:source:edit')")
    @Log(title = "项目资金来源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TFoundingSource tFoundingSource)
    {
        return toAjax(tFoundingSourceService.updateTFoundingSource(tFoundingSource));
    }

    /**
     * 删除项目资金来源
     */
//    @PreAuthorize("@ss.hasPermi('system:source:remove')")
    @Log(title = "项目资金来源", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tFoundingSourceService.deleteTFoundingSourceByIds(ids));
    }
}
