<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemRollbackMapper">

    <resultMap type="TItemRollback" id="TItemRollbackResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="flow"    column="flow"    />
        <result property="person"    column="person"    />
        <result property="reason"    column="reason"    />
        <result property="time"    column="time"    />
        <result property="status"    column="status"    />
        <result property="auditDeptName"    column="audit_dept_name"    />
    </resultMap>

    <sql id="selectTItemRollbackVo">
        select id, item_id, flow, person, reason, time, status, audit_dept_name from t_item_rollback
    </sql>

    <select id="selectTItemRollbackList" parameterType="TItemRollback" resultMap="TItemRollbackResult">
        <include refid="selectTItemRollbackVo"/>
        <where>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="flow != null and flow != ''"> and flow = #{flow}</if>
            <if test="person != null  and person != ''"> and person = #{person}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="time != null "> and time = #{time}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="auditDeptName != null  and auditDeptName != ''"> and audit_dept_name like concat('%', #{auditDeptName}, '%')</if>
        </where>
        order by time DESC
    </select>

    <select id="selectTItemRollbackById" parameterType="Long" resultMap="TItemRollbackResult">
        <include refid="selectTItemRollbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertTItemRollback" parameterType="TItemRollback" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_rollback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="flow != null">flow,</if>
            <if test="person != null">person,</if>
            <if test="reason != null">reason,</if>
            time,
            <if test="status != null">status,</if>
            <if test="auditDeptName != null">#{auditDeptName},</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="flow != null">#{flow},</if>
            <if test="person != null">#{person},</if>
            <if test="reason != null">#{reason},</if>
            sysdate()
            <if test="status != null">#{status},</if>
            <if test="auditDeptName != null">#{auditDeptName},</if>
         </trim>
    </insert>

    <update id="updateTItemRollback" parameterType="TItemRollback">
        update t_item_rollback
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="flow != null">flow = #{flow},</if>
            <if test="person != null">person = #{person},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="time != null">time = #{time},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditDeptName != null">audit_dept_name = #{auditDeptName},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemRollbackById" parameterType="Long">
        update t_item_rollback set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemRollbackByIds" parameterType="String">
        update t_item_rollback set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="insertTItemRollbackBatch">
        INSERT INTO t_item_rollback (item_id, flow, person, reason, time, status, audit_dept_name)
        VALUES
        <foreach collection="itemList" item="item" separator=",">
            (#{item.itemId}, #{item.flow}, #{item.person}, #{item.reason}, #{item.time}, #{item.status}, #{item.auditDeptName})
        </foreach>
    </insert>
</mapper>
