package com.ruoyi.tiem.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.tiem.domain.TItemAudit;

/**
 * 项目审核信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface ITItemAuditService 
{
    /**
     * 查询项目审核信息
     * 
     * @param id 项目审核信息主键
     * @return 项目审核信息
     */
    public TItemAudit selectTItemAuditById(Long id);

    /**
     * 查询项目审核信息
     * 
     * @param id 项目审核信息主键
     * @return 项目审核记录列表
     */
    public List<Map<String, Object>> handleRecord(Long id);

    /**
     * 查询项目审核信息列表
     * 
     * @param tItemAudit 项目审核信息
     * @return 项目审核信息集合
     */
    public List<TItemAudit> selectTItemAuditList(TItemAudit tItemAudit);

    /**
     * 新增项目审核信息
     * 
     * @param tItemAudit 项目审核信息
     * @return 结果
     */
    public int insertTItemAudit(TItemAudit tItemAudit);

    /**
     * 修改项目审核信息
     * 
     * @param tItemAudit 项目审核信息
     * @return 结果
     */
    public int updateTItemAudit(TItemAudit tItemAudit);

    /**
     * 批量删除项目审核信息
     * 
     * @param ids 需要删除的项目审核信息主键集合
     * @return 结果
     */
    public int deleteTItemAuditByIds(Long[] ids);

    /**
     * 删除项目审核信息信息
     * 
     * @param id 项目审核信息主键
     * @return 结果
     */
    public int deleteTItemAuditById(Long id);
}
