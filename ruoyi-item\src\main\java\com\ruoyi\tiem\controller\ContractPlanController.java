package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.ContractPlan;
import com.ruoyi.tiem.service.IContractPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合约规划信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/contract/plan")
public class ContractPlanController extends BaseController
{
    @Autowired
    private IContractPlanService contractPlanService;

    /**
     * 查询合约规划信息列表
     */
    @PreAuthorize("@ss.hasPermi('contract:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractPlan contractPlan)
    {
        startPage();
        List<ContractPlan> list = contractPlanService.selectContractPlanList(contractPlan);
        return getDataTable(list);
    }

    /**
     * 根据项目id查询V2合规规划信息
     */
    @GetMapping("/listV2")
    public TableDataInfo listV2(@RequestParam Long itemId)
    {
        startPage();
        List<ContractPlan> list = contractPlanService.selectV2ContractPlanList(itemId);
        return getDataTable(list);
    }

    /**
     * 导出合约规划信息列表
     */
    @PreAuthorize("@ss.hasPermi('contract:plan:export')")
    @Log(title = "合约规划信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractPlan contractPlan)
    {
        List<ContractPlan> list = contractPlanService.selectContractPlanList(contractPlan);
        ExcelUtil<ContractPlan> util = new ExcelUtil<ContractPlan>(ContractPlan.class);
        util.exportExcel(response, list, "合约规划信息数据");
    }

    /**
     * 获取合约规划信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractPlanService.selectContractPlanById(id));
    }

    /**
     * 新增合约规划信息
     */
    @PreAuthorize("@ss.hasPermi('contract:plan:add')")
    @Log(title = "合约规划信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractPlan contractPlan)
    {
        return toAjax(contractPlanService.insertContractPlan(contractPlan));
    }

    /**
     * 修改合约规划信息
     */
    @PreAuthorize("@ss.hasPermi('contract:plan:edit')")
    @Log(title = "合约规划信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractPlan contractPlan)
    {
        return toAjax(contractPlanService.updateContractPlan(contractPlan));
    }

    /**
     * 删除合约规划信息
     */
    @PreAuthorize("@ss.hasPermi('contract:plan:remove')")
    @Log(title = "合约规划信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractPlanService.deleteContractPlanByIds(ids));
    }
}
