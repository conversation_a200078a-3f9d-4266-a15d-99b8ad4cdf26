package com.ruoyi.tiem.service.impl;

import java.util.List;

import com.ruoyi.tiem.domain.TContractPayment;
import com.ruoyi.tiem.mapper.TContractPaymentMapper;
import com.ruoyi.tiem.service.ITContractPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 合同金额支付Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Service
public class TContractPaymentServiceImpl implements ITContractPaymentService
{
    @Autowired
    private TContractPaymentMapper tContractPaymentMapper;

    /**
     * 查询合同金额支付
     * 
     * @param id 合同金额支付主键
     * @return 合同金额支付
     */
    @Override
    public TContractPayment selectTContractPaymentById(Long id)
    {
        return tContractPaymentMapper.selectTContractPaymentById(id);
    }

    /**
     * 查询合同金额支付列表
     * 
     * @param tContractPayment 合同金额支付
     * @return 合同金额支付
     */
    @Override
    public List<TContractPayment> selectTContractPaymentList(TContractPayment tContractPayment)
    {
        return tContractPaymentMapper.selectTContractPaymentList(tContractPayment);
    }

    /**
     * 新增合同金额支付
     * 
     * @param tContractPayment 合同金额支付
     * @return 结果
     */
    @Override
    public int insertTContractPayment(TContractPayment tContractPayment)
    {
        return tContractPaymentMapper.insertTContractPayment(tContractPayment);
    }

    /**
     * 修改合同金额支付
     * 
     * @param tContractPayment 合同金额支付
     * @return 结果
     */
    @Override
    public int updateTContractPayment(TContractPayment tContractPayment)
    {
        return tContractPaymentMapper.updateTContractPayment(tContractPayment);
    }

    /**
     * 批量删除合同金额支付
     * 
     * @param ids 需要删除的合同金额支付主键
     * @return 结果
     */
    @Override
    public int deleteTContractPaymentByIds(Long[] ids)
    {
        return tContractPaymentMapper.deleteTContractPaymentByIds(ids);
    }

    /**
     * 删除合同金额支付信息
     * 
     * @param id 合同金额支付主键
     * @return 结果
     */
    @Override
    public int deleteTContractPaymentById(Long id)
    {
        return tContractPaymentMapper.deleteTContractPaymentById(id);
    }


    /**
     * 根据执行年份查询某个具体合同某个月月支付额
     *
     */
    @Override
    public TContractPayment selectContractPaymentByYearAndcontractId(Integer year, Long contractId, Integer month,String remark){
        return tContractPaymentMapper.selectContractPaymentByYearAndcontractId(year, contractId,month,remark);
    };

    /**
     * 根据month和year和itemCategory编辑支付额
     *
     */
    @Override
    public int editTContractPayment(TContractPayment contractPayment)
    {
        return tContractPaymentMapper.editTContractPayment(contractPayment);
    }
}
