<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemInfoMapper">

    <resultMap type="TItemInfo" id="TItemInfoResult">
        <result property="id" column="id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemNumber" column="item_number"/>
        <result property="leader" column="leader"/>
        <result property="contact" column="contact"/>
        <result property="email" column="email"/>
        <result property="place" column="place"/>
        <result property="necessity" column="necessity"/>
        <result property="feasibility" column="feasibility"/>
        <result property="conditions" column="conditions"/>
        <result property="content" column="content"/>
        <result property="arrange" column="arrange"/>
        <result property="risk" column="risk"/>
        <result property="benefit" column="benefit"/>
        <result property="details" column="details"/>
        <result property="applyYear" column="apply_year"/>
        <result property="applyUnit" column="apply_unit"/>
        <result property="contract" column="contract"/>
        <result property="applyAmount" column="apply_amount"/>
        <result property="itemCategory" column="item_category"/>
        <result property="purchaseType" column="purchase_type"/>
        <result property="economyType" column="economy_type"/>
        <result property="detailType" column="detail_type"/>
        <result property="projectNumber" column="project_number"/>
        <result property="projectPrice" column="project_price"/>
        <result property="beginYearProjectAmount" column="begin_year_project_amount"/>
        <result property="adjustProjectAmount" column="adjust_project_amount"/>
        <result property="totalProjectAmount" column="total_project_amount"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="contractPrice" column="contract_price"/>
        <result property="contractAmount" column="contract_amount"/>
        <result property="supplier" column="supplier"/>
        <result property="calculateAmount" column="calculate_amount"/>
        <result property="dynamicCost" column="dynamic_cost"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="flow" column="flow"/>
        <result property="status" column="status"/>
        <result property="applyUnitId" column="apply_unit_id"/>
        <result property="majorSort" column="major_sort"/>
        <result property="majorFileIds" column="major_file_ids"/>
        <result property="compoSort" column="compo_sort"/>
        <result property="compoFileIds" column="compo_file_ids"/>
        <result property="storeBudget" column="store_budget"/>
        <result property="storeOpinion" column="store_opinion"/>
        <result property="storeFileIds" column="store_file_ids"/>
        <result property="planSort" column="plan_sort"/>
        <result property="planFileIds" column="plan_file_ids"/>
        <result property="miitNumber" column="miit_number"/>
        <result property="miitPrice" column="miit_price"/>
        <result property="mittAmount" column="mitt_amount"/>
        <result property="contractId" column="contract_id"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="invoiceAmount" column="invoice_amount"/>
        <result property="executeRate" column="execute_rate"/>
        <result property="signRate" column="sign_rate"/>
        <result property="createPhone" column="create_phone"/>
        <result property="assetsPerson" column="assets_person"/>
        <result property="assetsPersonPhone" column="assets_person_phone"/>
        <result property="focalUnit" column="focal_unit"/>
        <result property="focalUnitId" column="focal_unit_id"/>
        <result property="budgetFileIds" column="budget_file_ids"/>
        <result property="budget" column="budget"/>
        <result property="schoolReviewFileIds" column="school_review_file_ids"/>
        <result property="miitReviewFileIds" column="miit_review_file_ids"/>
        <result property="deepDesignFileIds" column="deep_design_file_ids"/>
        <result property="schoolSort" column="school_sort"/>
        <result property="ministrySort" column="ministry_sort"/>
        <result property="createById" column="create_by_id"/>
        <result property="projectCategory" column="project_category"/>
        <result property="schoolEnterYear" column="school_enter_year"/>
        <result property="ministryEnterYear" column="ministry_enter_year"/>
        <result property="executeYear" column="execute_year"/>
        <result property="cycles" column="cycles"/>
        <result property="goal" column="goal"/>
        <result property="explains" column="explains"/>
        <result property="actualCompletion" column="actual_completion"/>
    </resultMap>

    <sql id="selectTItemInfoVo">
        select id,
               item_name,
               item_number,
               leader,
               contact,
               email,
               place,
               necessity,
               feasibility,
               conditions,
               content,
               arrange,
               risk,
               benefit,
               details,
               apply_year,
               apply_unit,
               contract,
               apply_amount,
               item_category,
               purchase_type,
               economy_type,
               detail_type,
               project_number,
               project_price,
               begin_year_project_amount,
               adjust_project_amount,
               total_project_amount,
               contract_number,
               contract_price,
               contract_amount,
               supplier,
               calculate_amount,
               dynamic_cost,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               flow,
               status,
               apply_unit_id,
               major_sort,
               major_file_ids,
               compo_sort,
               compo_file_ids,
               store_budget,
               store_opinion,
               store_file_ids,
               plan_sort,
               plan_file_ids,
               miit_number,
               miit_price,
               mitt_amount,
               contract_id,
               payment_amount,
               invoice_amount,
               execute_rate,
               sign_rate,
               create_phone,
               assets_person,
               assets_person_phone,
               focal_unit,
               focal_unit_id,
               budget_file_ids,
               budget,
               school_review_file_ids,
               miit_review_file_ids,
               deep_design_file_ids,
               school_sort,
               ministry_sort,
               create_by_id,
               project_category,
               school_enter_year,
               ministry_enter_year,
               execute_year,
               cycles,
               goal,
               explains,
               actual_completion
        from t_item_info
    </sql>

    <select id="selectTItemInfoList" parameterType="TItemInfo" resultMap="TItemInfoResult">
        <include refid="selectTItemInfoVo"/>
        where status in (1, 2)
        <if test="itemName != null  and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
        <if test="itemNumber != null  and itemNumber != ''">and item_number = #{itemNumber}</if>
        <if test="details != null  and details != ''">and details = #{details}</if>
        <if test="applyYear != null ">and apply_year = #{applyYear}</if>
        <if test="executeYear != null ">and execute_year = #{executeYear}</if>
        <if test="applyUnit != null  and applyUnit != ''">and apply_unit like concat('%', #{applyUnit}, '%')</if>
        <if test="contract != null  and contract != ''">and contract = #{contract}</if>
        <if test="applyAmount != null ">and apply_amount = #{applyAmount}</if>
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>
        <if test="params.itemCategories != null and params.itemCategories.size()>0">
            AND item_category in
            <foreach item="itemCategory" collection="params.itemCategories" open="(" separator="," close=")">
                #{itemCategory}
            </foreach>
        </if>
        <if test="purchaseType != null  and purchaseType != ''">and purchase_type = #{purchaseType}</if>
        <if test="economyType != null  and economyType != ''">and economy_type = #{economyType}</if>
        <if test="detailType != null  and detailType != ''">and detail_type = #{detailType}</if>
        <if test="projectNumber != null ">and project_number = #{projectNumber}</if>
        <if test="projectPrice != null ">and project_price = #{projectPrice}</if>
        <if test="beginYearProjectAmount != null ">and begin_year_project_amount = #{beginYearProjectAmount}</if>
        <if test="adjustProjectAmount != null ">and adjust_project_amount = #{adjustProjectAmount}</if>
        <if test="totalProjectAmount != null ">and total_project_amount = #{totalProjectAmount}</if>
        <if test="contractNumber != null ">and contract_number = #{contractNumber}</if>
        <if test="contractPrice != null ">and contract_price = #{contractPrice}</if>
        <if test="contractAmount != null ">and contract_amount = #{contractAmount}</if>
        <if test="supplier != null  and supplier != ''">and supplier = #{supplier}</if>
        <if test="calculateAmount != null ">and calculate_amount = #{calculateAmount}</if>
        <if test="dynamicCost != null ">and dynamic_cost = #{dynamicCost}</if>
        <if test="flow != null ">and flow = #{flow}</if>
        <if test="params.flows != null and params.flows.size()>0">
            AND flows in
            <foreach item="flows" collection="params.flows" open="(" separator="," close=")">
                #{flows}
            </foreach>
        </if>
        <if test="status != null  and status != ''">and status = #{status}</if>
        <if test="applyUnitId != null ">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="params.possibleFlows != null and params.possibleFlows.size()>0">
            AND flow IN
            <foreach item="flowItem" collection="params.possibleFlows" open="(" separator="," close=")">
                #{flowItem}
            </foreach>
        </if>
        <if test="params.checkMultiDeptTeacher != null and params.checkMultiDeptTeacher == true">
            OR (flow = 1 AND FIND_IN_SET(#{params.currentDeptId}, apply_unit_id) > 0 AND STATUS = 1)
        </if>
        <if test="params.checkMultiDeptDean != null and params.checkMultiDeptDean == true">
            OR (flow = 1 AND FIND_IN_SET(#{params.currentDeptId}, apply_unit_id) > 0 AND STATUS = 1)
        </if>
        <if test="majorSort != null ">and major_sort = #{majorSort}</if>
        <if test="majorFileIds != null ">and major_file_ids = #{majorFileIds}</if>
        <if test="compoSort != null ">and compo_sort = #{compoSort}</if>
        <if test="compoFileIds != null ">and compo_file_ids = #{compoFileIds}</if>
        <if test="storeBudget != null ">and store_budget = #{storeBudget}</if>
        <if test="storeOpinion != null ">and store_opinion = #{storeOpinion}</if>
        <if test="storeFileIds != null ">and store_file_ids = #{storeFileIds}</if>
        <if test="planSort != null ">and plan_sort = #{planSort}</if>
        <if test="storeFileIds != null ">and plan_file_ids = #{planFileIds}</if>
        <if test="miitNumber != null ">and miit_number = #{miitNumber}</if>
        <if test="miitPrice != null ">and miit_price = #{miitPrice}</if>
        <if test="mittAmount != null ">and mitt_amount = #{mittAmount}</if>
        <if test='contractId != null and contractId == "-1"'>and contract_id is null</if>
        <if test='contractId != null and contractId != "-1"'>and (contract_id is null or contract_id = #{contractId})</if>
        <if test="paymentAmount != null ">and payment_amount = #{paymentAmount}</if>
        <if test="invoiceAmount != null ">and invoice_amount = #{invoiceAmount}</if>
        <if test="executeRate != null  and executeRate != ''">and execute_rate = #{executeRate}</if>
        <if test="signRate != null  and signRate != ''">and sign_rate = #{signRate}</if>
        <if test="createPhone != null  and createPhone != ''">and create_phone = #{createPhone}</if>
        <if test="assetsPerson != null  and assetsPerson != ''">and assets_person = #{assetsPerson}</if>
        <if test="assetsPersonPhone != null  and assetsPersonPhone != ''">and assets_person_phone =  #{assetsPersonPhone}</if>
        <if test="focalUnit != null  and focalUnit != ''">and focal_unit = #{focalUnit}</if>
        <if test="focalUnitId != null ">and focal_unit_id = #{focalUnitId}</if>
        <if test="budgetFileIds != null  and budgetFileIds != ''">and budget_file_ids = #{budgetFileIds}</if>
        <if test="budget != null ">and budget = #{budget}</if>
        <if test="createById != null ">and create_by_id = #{createById}</if>
        <if test="params.ids != null and params.ids.size()>0">
            AND id in
            <foreach item="id" collection="params.ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="params.pendingAudit != null and params.pendingAudit == true">
            AND (
            <!-- 需要当前用户审核的项目 -->
            flow IN
            <foreach item="flow" collection="params.possibleFlows" open="(" separator="," close=")">
                #{flow}
            </foreach>
            OR flow >= 3
            OR (flow = 0 AND create_by_id = #{params.currentUserId} AND #{params.hasTeacherRole} = true)

            <!-- 多单位联合申报项目的特殊处理 -->
            <if test="params.checkMultiDeptTeacher != null and params.checkMultiDeptTeacher == true">
                OR (flow = 1 AND apply_unit_id LIKE CONCAT('%', #{params.currentDeptId}, '%'))
            </if>
            <if test="params.checkMultiDeptDean != null and params.checkMultiDeptDean == true">
                OR (flow = 2 AND apply_unit_id LIKE CONCAT('%', #{params.currentDeptId}, '%'))
            </if>
            )
        </if>
        order by create_time desc
    </select>

    <select id="selectExecutedItemInfoList">
        <include refid="selectTItemInfoVo"/>
        where status in (1, 2)
        <if test="executeYear != null">and execute_year = #{executeYear}</if>
        <if test="itemCategory != null">and item_category = #{itemCategory}</if>
        <if test="applyUnitId != null">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null">and focal_unit_id = #{focalUnitId}</if>
        and flow in (7 ,8, 9, 10)
    </select>

    <select id="selectFinishedItemInfoList">
        <include refid="selectTItemInfoVo"/>
        where status in (1, 2)
        <if test="executeYear != null">and execute_year = #{executeYear}</if>
        <if test="itemCategory != null">and item_category = #{itemCategory}</if>
        <if test="applyUnitId != null">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null">and focal_unit_id = #{focalUnitId}</if>
        and flow >= 12
    </select>

    <select id="selectTItemInfoById" parameterType="Long" resultMap="TItemInfoResult">
        <include refid="selectTItemInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectTItemInfoByItemName" parameterType="String" resultMap="TItemInfoResult">
        <include refid="selectTItemInfoVo"/>
        where item_name = #{itemName}
    </select>
    <select id="selectByItemIds" resultMap="TItemInfoResult">
        <include refid="selectTItemInfoVo"/>
        where status = 1 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getStatisticsOne" resultType="java.util.Map">
        select
        COALESCE (count(*), 0) as projectNumber,
        COALESCE (sum(total_project_amount), 0) as projectAmount,
        COALESCE ((select sum(total_amount) from t_contract_info where status = 1 and item_ids in (
        select id from t_item_info
        where status = 1
        <if test="applyUnitId != null">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null">and focal_unit_id = #{focalUnitId}</if>
        <if test="executeYear != null">and execute_year = #{executeYear}</if>
        )), 0) as contractAmount,
        COALESCE ((select sum(payment_amount) from t_contract_payment where status = 1 and item_id in (
        select id from t_item_info
        where status = 1
        <if test="applyUnitId != null">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null">and focal_unit_id = #{focalUnitId}</if>
        <if test="executeYear != null">and execute_year = #{executeYear}</if>
        )), 0) as paymentAmount
        from t_item_info
        where status = 1 and flow >= 10
        <if test="applyUnitId != null">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null">and focal_unit_id = #{focalUnitId}</if>
        <if test="executeYear != null">and execute_year = #{executeYear}</if>
    </select>
    <select id="getStatisticsTwo" resultType="java.util.Map">
        SELECT b.dict_label,
               SUM(a.total_amount) AS amount
        FROM t_contract_info a
                 LEFT JOIN sys_dict_data b ON a.detail_type = b.dict_value
        WHERE a.STATUS = 1
          AND a.detail_type IS NOT NULL
          AND a.total_amount IS NOT NULL
          AND YEAR(a.create_time) = #{year}
        GROUP BY a.detail_type,
                 b.dict_label
        ORDER BY SUM(a.total_amount) DESC;
    </select>
    <select id="getStatisticsTwoByItemIds" resultType="java.util.Map">
        SELECT
        b.dict_label,
        SUM( a.total_amount ) AS amount
        FROM
        t_contract_info a
        LEFT JOIN sys_dict_data b ON a.detail_type = b.dict_value
        WHERE
        a.STATUS = 1
        AND a.detail_type IS NOT NULL
        AND a.total_amount IS NOT NULL
        AND a.item_ids in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getItemIdsByDeptId" resultType="Long" parameterType="Long">
        SELECT id
        FROM t_item_info
        WHERE apply_unit_id like concat('%', #{applyUnitId}, '%')
    </select>
    <select id="getItemIdsByFocalUnitId" resultType="Long" parameterType="Long">
        SELECT id
        FROM t_item_info
        WHERE focal_unit_id = #{focalUnitId}
    </select>
    <select id="getStatisticsThree" resultType="java.util.Map">
        SELECT
        id,
        item_name,
        execute_rate
        FROM
        t_item_info
        WHERE
        STATUS = 1
        AND execute_rate IS NOT NULL
        <if test="itemId != null">and id = #{itemId}</if>
        ORDER BY
        CAST(execute_rate AS DECIMAL(10, 2)) DESC;
    </select>
    <select id="getStatisticsFour" resultType="java.util.Map" parameterType="String">
        SELECT item_category, ifnull(sum(total_project_amount), 0) as amount
        FROM t_item_info
        WHERE STATUS = 1
          and create_time >= #{startTime}
          and create_time &lt;= #{endTime}
        GROUP BY item_category
    </select>
    <!-- 获取三大基础类别一级指标占比情况-->
    <select id="getAmountOfBasicCategory" resultType="java.util.Map" parameterType="String">
        SELECT c.economy_type                 as economy_type,
               ifnull(sum(c.total_amount), 0) as amount
        FROM t_contract_info c
                 left join t_item_info i on c.item_ids = i.id
        WHERE c.status = 1
          and i.item_category = #{itemCategory}
          and c.create_time >= #{startTime}
          and c.create_time &lt;= #{endTime}
        GROUP BY i.economy_type
    </select>
    <select id="getItemStatistics" resultType="java.util.Map">
        SELECT COALESCE(item_category, '') AS item_category,
        COALESCE(COUNT(*), 0) AS itemCount,
        COALESCE(SUM(apply_amount), 0) AS applyAmount,
        COALESCE(SUM(mitt_amount), 0) AS miitAmount
        FROM t_item_info
        WHERE STATUS = 1
        and item_category = #{itemCategory}
        <if test="applyUnitId != null">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null">and focal_unit_id = #{focalUnitId}</if>
    </select>
    <select id="getItemSort" resultType="ItemRank">
        SELECT item_name    as itemName,
               apply_unit   as applyUnit,
               create_by    as createBy,
               execute_rate as executeRate,
            total_project_amount as totalAmount,
               payment_amount as paymentAmount
        FROM t_item_info
        WHERE STATUS = 1
          AND execute_rate IS NOT NULL
          AND execute_year = #{year}
        ORDER BY CAST(execute_rate AS DECIMAL(5, 2)) DESC
    </select>
    <select id="getItemNumberLowerRank" resultType="Integer">
        SELECT COUNT(*)
        FROM t_item_info
        WHERE STATUS = 1
          AND execute_rate IS NOT NULL
          AND #{executeRate} > CAST(execute_rate AS DECIMAL(5, 2));
    </select>
    <!--获取执行阶段项目合计数据 -->
    <select id="executionItemInfo">
        select
        COALESCE (sum(apply_amount), 0) as applyAmount,
        COALESCE (sum(miit_number), 0) as ministryNumber,
        COALESCE (sum(miit_price), 0) as ministryUnitPrice,
        COALESCE (sum(mitt_amount), 0) as ministryTotalAmount,
        COALESCE (sum(project_number), 0) as projectNumber,
        COALESCE (sum(project_price), 0) as projectUnitPrice,
        COALESCE (sum(begin_year_project_amount), 0) as yearStartAmount,
        COALESCE (sum(adjust_project_amount), 0) as projectAdjustAmount,
        COALESCE (sum(total_project_amount), 0) as totalAmount,
        COALESCE ((select sum(total_amount) from t_contract_info
        where item_ids in (select id from t_item_info where `status` in (1, 2)
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>)), 0)
        as contractAmount,
        COALESCE ((select sum(payment_amount) from t_contract_payment
        where item_id in (select id from t_item_info where `status` in (1, 2)
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>)), 0)
        as settlementAmount,
        COALESCE ((select sum(payment_amount) from t_contract_payment
        where item_id in (select id from t_item_info where `status` in (1, 2)
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>)), 0)
        as billingAmount,
        COALESCE ((select sum(payment_amount) from t_contract_payment
        where item_id in (select id from t_item_info where `status` in (1, 2)
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>)), 0)
        as paymentAmount
        from t_item_info
        where `status` in (1, 2) and flow in (7 ,8, 9, 10) and execute_year = #{executeYear}
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>
        <if test="applyUnitId != null ">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null ">and focal_unit_id = #{focalUnitId}</if>
    </select>
    <!--获取不同项目类别下具体项目列表数据 -->
    <select id="executionItemDetail">
        select i.id                                     as itemId,
               i.create_by_id                           as createById,
               i.item_name                              as projectName,
               i.ministry_enter_year                    as ministryEnterYear,
               i.apply_unit                             as applyUnit,
               COALESCE(i.apply_amount, 0)              as applyAmount,
               COALESCE(i.miit_number, 0)               as ministryNumber,
               COALESCE(i.miit_price, 0)                as ministryUnitPrice,
               COALESCE(i.mitt_amount, 0)               as ministryTotalAmount,
               COALESCE(i.project_number, 0)            as projectNumber,
               COALESCE(i.project_price, 0)             as projectUnitPrice,
               COALESCE(i.begin_year_project_amount, 0) as yearStartAmount,
               COALESCE(i.adjust_project_amount, 0)     as projectAdjustAmount,
               COALESCE(i.total_project_amount, 0)      as totalAmount,
               COALESCE(sum(c.total_amount), 0)         as contractAmount,
               COALESCE(sum(p.payment_amount), 0)       as settlementAmount,
               COALESCE(sum(p.billing_amount), 0)       as billingAmount,
               COALESCE(sum(p.payment_amount), 0)       as paymentAmount
        from t_item_info i
                 left join t_contract_info c
                           on i.id = c.item_ids
                 left join t_contract_payment p
                           on i.id = p.item_id
        where i.`status` in (1, 2)
          and i.flow in (7 ,8, 9, 10)
          and i.execute_year = #{executeYear}
          and i.item_category = #{itemCategory}
        group by item_name
    </select>
    <!--获取不同项目下具体合同列表数据 -->
    <select id="executionContractDetail">
        select i.id                                     as itemId,
               c.id                                     as contractId,
               c.contract_name                          as projectName,
               COALESCE(i.apply_amount, 0)              as applyAmount,
               COALESCE(i.miit_number, 0)               as ministryNumber,
               COALESCE(i.miit_price, 0)                as ministryUnitPrice,
               COALESCE(i.mitt_amount, 0)               as ministryTotalAmount,
               COALESCE(i.begin_year_project_amount, 0) as yearStartAmount,
               COALESCE(i.adjust_project_amount, 0)     as projectAdjustAmount,
               COALESCE(i.total_project_amount, 0)      as totalAmount,
               COALESCE(c.purchase_type, "")            as purchaseType,
               COALESCE(c.economy_type, "")             as economyType,
               COALESCE(c.total_amount, 0)              as contractAmount,
               COALESCE(p.payment_amount, 0)            as settlementAmount,
               COALESCE(c.check_amount, 0)              as checkAmount,
               COALESCE(p.billing_amount, 0)            as billingAmount,
               COALESCE(p.payment_amount, 0)            as paymentAmount
        from t_contract_info c
                 left join t_item_info i
                           on i.id = c.item_ids
                 left join t_contract_payment p
                           on i.id = p.item_id
        where i.`status` = 1
          and c.status = 1
          and i.flow in (7 ,8, 9, 10)
          and i.execute_year = #{executeYear}
          and i.id = #{itemId}
    </select>

    <!--获取绩效评价模块列表数据 -->
    <select id="performanceItemInfo">
        select
        COALESCE (sum(total_project_amount), 0) as totalAmount
        from t_item_info
        where `status` in (1, 2)
          and flow >= 12
          and execute_year = #{executeYear}
        <if test="itemCategory != null ">and item_category = #{itemCategory}</if>
        <if test="applyUnitId != null ">and apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        <if test="focalUnitId != null ">and focal_unit_id = #{focalUnitId}</if>
    </select>
    <!--绩效评价模块根据项目分类以及执行年度，查询已执行完成项目 -->
    <select id="performanceItemDetail">
        select id                                     as itemId,
               item_name                              as projectName,
               COALESCE(total_project_amount, 0)      as totalAmount,
               apply_unit                             as applyUnit,
               apply_year                             as applyYear,
               focal_unit_id                          as focalUnitId,
               status                                 as status
        from t_item_info
        where `status` in (1, 2)
          and flow >= 12
          and execute_year = #{executeYear}
          and item_category = #{itemCategory}
    </select>
    <!--根据项目查询合同累计支付额 -->
    <select id="queryPaymentAmount">
        select COALESCE(sum(payment_amount), 0.00) as paymentAmount
        from t_contract_payment
        where status = 1 and year = #{year} and item_id = #{itemId}
    </select>
    <select id="queryExecutedItemInfo">
        select
               i.item_name as itemName,
               i.item_category as itemCategory,
               i.apply_unit as applyUnit,
               i.execute_year as executeYear,
               COALESCE (i.total_project_amount, 0) as totalAmount,
               COALESCE (sum(c.payment_amount), 0) as paymentAmount,
               COALESCE (sum(c.payment_amount) / NULLIF (i.total_project_amount, 0), 0) as implementationRate
        from t_item_info i left join t_contract_payment c
        on i.id = c.item_id
        where i.status = 1
        <if test="startYear != null">and i.execute_year >= #{startYear}</if>
        <if test="endYear != null">and i.execute_year &lt;= #{endYear}</if>
        <if test="applyUnitId != null">and i.apply_unit_id like concat('%', #{applyUnitId}, '%')</if>
        group by i.id
    </select>
    <insert id="insertTItemInfo" parameterType="TItemInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemName != null">item_name,</if>
            <if test="itemNumber != null">item_number,</if>
            <if test="leader != null">leader,</if>
            <if test="contact != null">contact,</if>
            <if test="email != null">email,</if>
            <if test="place != null">place,</if>
            <if test="necessity != null">necessity,</if>
            <if test="feasibility != null">feasibility,</if>
            <if test="conditions != null">conditions,</if>
            <if test="content != null">content,</if>
            <if test="arrange != null">arrange,</if>
            <if test="risk != null">risk,</if>
            <if test="benefit != null">benefit,</if>
            <if test="details != null">details,</if>
            <if test="applyYear != null">apply_year,</if>
            <if test="applyUnit != null">apply_unit,</if>
            <if test="contract != null">contract,</if>
            <if test="applyAmount != null">apply_amount,</if>
            <if test="itemCategory != null">item_category,</if>
            <if test="purchaseType != null">purchase_type,</if>
            <if test="economyType != null">economy_type,</if>
            <if test="detailType != null">detail_type,</if>
            <if test="projectNumber != null">project_number,</if>
            <if test="projectPrice != null">project_price,</if>
            <if test="beginYearProjectAmount != null">begin_year_project_amount,</if>
            <if test="adjustProjectAmount != null">adjust_project_amount,</if>
            <if test="totalProjectAmount != null">total_project_amount,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="contractPrice != null">contract_price,</if>
            <if test="contractAmount != null">contract_amount,</if>
            <if test="supplier != null">supplier,</if>
            <if test="calculateAmount != null">calculate_amount,</if>
            <if test="dynamicCost != null">dynamic_cost,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="flow != null">flow,</if>
            <if test="status != null">status,</if>
            <if test="applyUnitId != null">apply_unit_id,</if>
            <if test="miitNumber != null">miit_number,</if>
            <if test="miitPrice != null">miit_price,</if>
            <if test="mittAmount != null">mitt_amount,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="invoiceAmount != null">invoice_amount,</if>
            <if test="executeRate != null">execute_rate,</if>
            <if test="signRate != null">sign_rate,</if>
            <if test="createPhone != null">create_phone,</if>
            <if test="assetsPerson != null">assets_person,</if>
            <if test="assetsPersonPhone != null">assets_person_phone,</if>
            <if test="focalUnit != null">focal_unit,</if>
            <if test="focalUnitId != null">focal_unit_id,</if>
            <if test="budgetFileIds != null">budget_file_ids,</if>
            <if test="budget != null">budget,</if>
            <if test="schoolReviewFileIds != null">school_review_file_ids,</if>
            <if test="miitReviewFileIds != null">miit_review_file_ids,</if>
            <if test="deepDesignFileIds != null">deep_design_file_ids,</if>
            <if test="schoolSort != null">schoolSort,</if>
            <if test="ministrySort != null">ministrySort,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="projectCategory != null">project_category,</if>
            <if test="schoolEnterYear != null">school_enter_year,</if>
            <if test="ministryEnterYear != null">ministry_enter_year,</if>
            <if test="executeYear != null">execute_year,</if>
            <if test="cycles != null">cycles,</if>
            <if test="goal != null">goal,</if>
            <if test="explains != null">explains,</if>
            <if test="actualCompletion != null">actual_completion,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemName != null">#{itemName},</if>
            <if test="itemNumber != null">#{itemNumber},</if>
            <if test="leader != null">#{leader},</if>
            <if test="contact != null">#{contact},</if>
            <if test="email != null">#{email},</if>
            <if test="place != null">#{place},</if>
            <if test="necessity != null">#{necessity},</if>
            <if test="feasibility != null">#{feasibility},</if>
            <if test="conditions != null">#{conditions},</if>
            <if test="content != null">#{content},</if>
            <if test="arrange != null">#{arrange},</if>
            <if test="risk != null">#{risk},</if>
            <if test="benefit != null">#{benefit},</if>
            <if test="details != null">#{details},</if>
            <if test="applyYear != null">#{applyYear},</if>
            <if test="applyUnit != null">#{applyUnit},</if>
            <if test="contract != null">#{contract},</if>
            <if test="applyAmount != null">#{applyAmount},</if>
            <if test="itemCategory != null">#{itemCategory},</if>
            <if test="purchaseType != null">#{purchaseType},</if>
            <if test="economyType != null">#{economyType},</if>
            <if test="detailType != null">#{detailType},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="projectPrice != null">#{projectPrice},</if>
            <if test="beginYearProjectAmount != null">#{beginYearProjectAmount},</if>
            <if test="adjustProjectAmount != null">#{adjustProjectAmount},</if>
            <if test="totalProjectAmount != null">#{totalProjectAmount},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="contractPrice != null">#{contractPrice},</if>
            <if test="contractAmount != null">#{contractAmount},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="calculateAmount != null">#{calculateAmount},</if>
            <if test="dynamicCost != null">#{dynamicCost},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="flow != null">#{flow},</if>
            <if test="status != null">#{status},</if>
            <if test="applyUnitId != null">#{applyUnitId},</if>
            <if test="miitNumber != null">#{miitNumber},</if>
            <if test="miitPrice != null">#{miitPrice},</if>
            <if test="mittAmount != null">#{mittAmount},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="invoiceAmount != null">#{invoiceAmount},</if>
            <if test="executeRate != null">#{executeRate},</if>
            <if test="signRate != null">#{signRate},</if>
            <if test="createPhone != null">#{createPhone},</if>
            <if test="assetsPerson != null">#{assetsPerson},</if>
            <if test="assetsPersonPhone != null">#{assetsPersonPhone},</if>
            <if test="focalUnit != null">#{focalUnit},</if>
            <if test="focalUnitId != null">#{focalUnitId},</if>
            <if test="budgetFileIds != null">#{budgetFileIds},</if>
            <if test="budget != null">#{budget},</if>
            <if test="schoolReviewFileIds != null">#{schoolReviewFileIds},</if>
            <if test="miitReviewFileIds != null">#{miitReviewFileIds},</if>
            <if test="deepDesignFileIds != null">#{deepDesignFileIds},</if>
            <if test="schoolSort != null">#{schoolSort},</if>
            <if test="ministrySort != null">#{ministrySort},</if>
            <if test="createById != null">#{createById},</if>
            <if test="projectCategory != null">#{projectCategory},</if>
            <if test="schoolEnterYear != null">#{schoolEnterYear},</if>
            <if test="ministryEnterYear != null">#{ministryEnterYear},</if>
            <if test="executeYear != null">#{executeYear},</if>
            <if test="cycles != null">#{cycles},</if>
            <if test="goal != null">#{goal},</if>
            <if test="explains != null">#{explains},</if>
            <if test="actualCompletion != null">#{actualCompletion},</if>
        </trim>
    </insert>

    <update id="updateTItemInfo" parameterType="TItemInfo">
        update t_item_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="itemNumber != null">item_number = #{itemNumber},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="email != null">email = #{email},</if>
            <if test="place != null">place = #{place},</if>
            <if test="necessity != null">necessity = #{necessity},</if>
            <if test="feasibility != null">feasibility = #{feasibility},</if>
            <if test="conditions != null">conditions = #{conditions},</if>
            <if test="content != null">content = #{content},</if>
            <if test="arrange != null">arrange = #{arrange},</if>
            <if test="risk != null">risk = #{risk},</if>
            <if test="benefit != null">benefit = #{benefit},</if>
            <if test="details != null">details = #{details},</if>
            <if test="applyYear != null">apply_year = #{applyYear},</if>
            <if test="applyUnit != null">apply_unit = #{applyUnit},</if>
            <if test="contract != null">contract = #{contract},</if>
            <if test="applyAmount != null">apply_amount = #{applyAmount},</if>
            <if test="itemCategory != null">item_category = #{itemCategory},</if>
            <if test="purchaseType != null">purchase_type = #{purchaseType},</if>
            <if test="economyType != null">economy_type = #{economyType},</if>
            <if test="detailType != null">detail_type = #{detailType},</if>
            <if test="projectNumber != null">project_number = #{projectNumber},</if>
            <if test="projectPrice != null">project_price = #{projectPrice},</if>
            <if test="beginYearProjectAmount != null">begin_year_project_amount = #{beginYearProjectAmount},</if>
            <if test="adjustProjectAmount != null">adjust_project_amount = #{adjustProjectAmount},</if>
            <if test="totalProjectAmount != null">total_project_amount = #{totalProjectAmount},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="contractPrice != null">contract_price = #{contractPrice},</if>
            <if test="contractAmount != null">contract_amount = #{contractAmount},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="calculateAmount != null">calculate_amount = #{calculateAmount},</if>
            <if test="dynamicCost != null">dynamic_cost = #{dynamicCost},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="flow != null">flow = #{flow},</if>
            <if test="status != null">status = #{status},</if>
            <if test="applyUnitId != null">apply_unit_id = #{applyUnitId},</if>
            <if test="miitNumber != null">miit_number = #{miitNumber},</if>
            <if test="miitPrice != null">miit_price = #{miitPrice},</if>
            <if test="mittAmount != null">mitt_amount = #{mittAmount},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="invoiceAmount != null">invoice_amount = #{invoiceAmount},</if>
            <if test="executeRate != null">execute_rate = #{executeRate},</if>
            <if test="signRate != null">sign_rate = #{signRate},</if>
            <if test="createPhone != null">create_phone = #{createPhone},</if>
            <if test="assetsPerson != null">assets_person = #{assetsPerson},</if>
            <if test="assetsPersonPhone != null">assets_person_phone = #{assetsPersonPhone},</if>
            <if test="focalUnit != null">focal_unit = #{focalUnit},</if>
            <if test="focalUnitId != null">focal_unit_id = #{focalUnitId},</if>
            <if test="budgetFileIds != null">budget_file_ids = #{budgetFileIds},</if>
            <if test="budget != null">budget = #{budget},</if>
            <if test="schoolReviewFileIds != null">school_review_file_ids = #{schoolReviewFileIds},</if>
            <if test="miitReviewFileIds != null">miit_review_file_ids = #{miitReviewFileIds},</if>
            <if test="deepDesignFileIds != null">deep_design_file_ids = #{deepDesignFileIds},</if>
            school_sort = #{schoolSort},
            ministry_sort = #{ministrySort},
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="projectCategory != null">project_category = #{projectCategory},</if>
            <if test="schoolEnterYear != null">school_enter_year = #{schoolEnterYear},</if>
            <if test="ministryEnterYear != null">ministry_enter_year = #{ministryEnterYear},</if>
            <if test="executeYear != null">execute_year = #{executeYear},</if>
            <if test="cycles != null">cycles = #{cycles},</if>
            <if test="goal != null">goal = #{goal},</if>
            <if test="explains != null">explains = #{explains},</if>
            <if test="actualCompletion != null">actual_completion = #{actualCompletion},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemInfoById" parameterType="Long">
        update t_item_info
        set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemInfoByIds" parameterType="String">
        update t_item_info set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateItemContract">
        update t_item_info set contract_id = #{contractId}
        where status = 1 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateContractAmount">
        UPDATE t_item_info
        SET contract_amount = (SELECT SUM(total_amount) FROM t_contract_info WHERE STATUS = 1 AND item_ids = #{id})
        WHERE id = #{id}
    </update>
    <update id="updateInvoiceAndPayment">
        UPDATE t_item_info
        SET invoice_amount = (SELECT sum(invoice_amount) FROM t_contract_info WHERE item_ids = #{id} AND STATUS = 1),
            payment_amount = (SELECT sum(payment_amount) FROM t_contract_info WHERE item_ids = #{id} AND STATUS = 1)
        WHERE id = #{id}
    </update>
    <update id="updateExecuteRate">
        UPDATE t_item_info
        SET execute_rate = CONCAT(LEAST(ROUND((payment_amount * 100.0 / total_project_amount), 2), 100), '')
        WHERE id = #{id};
    </update>
    <update id="updateSignRate">
        UPDATE t_item_info
        SET sign_rate = CONCAT(LEAST(ROUND((contract_amount * 100.0 / total_project_amount), 2), 100), '')
        WHERE id = #{id};
    </update>
    <update id="updateItemFlow">
        UPDATE t_item_info
        SET flow = #{flow}
        WHERE flow = #{flow}-1 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateItemFlowTwo">
        UPDATE t_item_info
        SET flow = #{flow}
        WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateSchoolEnterYear">
        UPDATE t_item_info
        SET school_enter_year = #{year}
        WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateMinistryEnterYear">
        UPDATE t_item_info
        SET ministry_enter_year = #{year}
        WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateExecuteYear">
        UPDATE t_item_info
        SET execute_year = #{year}
        WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateMajor">
        UPDATE t_item_info
        SET major_sort = #{sort},
            major_file_ids = #{fileIds}
        WHERE id = #{id};
    </update>
    <update id="updateCompo">
        UPDATE t_item_info
        SET compo_sort = #{sort},
            compo_file_ids = #{fileIds}
        WHERE id = #{id};
    </update>
    <update id="updateStore">
        UPDATE t_item_info
        SET store_budget = #{budget},
            store_opinion = #{opinion},
            store_file_ids = #{fileIds}
        WHERE id = #{id};
    </update>
    <update id="updatePlan">
        UPDATE t_item_info
        SET store_budget = #{budget},
            plan_sort = #{sort},
            plan_file_ids = #{fileIds}
        WHERE id = #{id};
    </update>
</mapper>
