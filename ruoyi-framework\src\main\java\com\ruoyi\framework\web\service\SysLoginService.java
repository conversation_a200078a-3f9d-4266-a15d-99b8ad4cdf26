package com.ruoyi.framework.web.service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.cas.bean.UserInfo;
import com.ruoyi.framework.cas.util.CasUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.BlackListException;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.exception.user.UserNotExistsException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;

import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid)
    {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha))
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    public String loginV2(String ticket) throws ExecutionException
    {
        // 获取Cas传输的用户信息
//        String response = "<cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>\n" +
//                "    <cas:authenticationSuccess>\n" +
//                "        <cas:user>70207637</cas:user>\n" +
//                "        <cas:attributes>\n" +
//                "            <cas:isFromNewLogin>false</cas:isFromNewLogin>\n" +
//                "            <cas:authenticationDate>2025-08-30T10:27:26.125+08:00[GMT+08:00]</cas:authenticationDate>\n" +
//                "            <cas:loginType>1</cas:loginType>\n" +
//                "            <cas:successfulAuthenticationHandlers>com.wisedu.minos.config.login.RememberMeUsernamePasswordHandler\n" +
//                "            </cas:successfulAuthenticationHandlers>\n" +
//                "            <cas:ip>************</cas:ip>\n" +
//                "            <cas:cn>孙元鹏</cas:cn>\n" +
//                "            <cas:USER_LOGIN_DATE>Sat Aug 30 10:27:26 GMT+08:00 2025</cas:USER_LOGIN_DATE>\n" +
//                "            <cas:userName>孙元鹏</cas:userName>\n" +
//                "            <cas:ua>Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********\n" +
//                "                Safari/537.36</cas:ua>\n" +
//                "            <cas:samlAuthenticationStatementAuthMethod>urn:oasis:names:tc:SAML:1.0:am:unspecified\n" +
//                "            </cas:samlAuthenticationStatementAuthMethod>\n" +
//                "            <cas:credentialType>MyRememberMeCaptchaCredential</cas:credentialType>\n" +
//                "            <cas:uid>70207637</cas:uid>\n" +
//                "            <cas:authenticationMethod>com.wisedu.minos.config.login.RememberMeUsernamePasswordHandler\n" +
//                "            </cas:authenticationMethod>\n" +
//                "            <cas:longTermAuthenticationRequestTokenUsed>false</cas:longTermAuthenticationRequestTokenUsed>\n" +
//                "            <cas:containerId>ou=1000001,ou=People</cas:containerId>\n" +
//                "            <cas:cllt>userNameLogin</cas:cllt>\n" +
//                "            <cas:dllt>generalLogin</cas:dllt>\n" +
//                "            <cas:USER_LOGIN_TYPE>2</cas:USER_LOGIN_TYPE>\n" +
//                "        </cas:attributes>\n" +
//                "    </cas:authenticationSuccess>\n" +
//                "</cas:serviceResponse>";

        String response = HttpUtil.createGet("https://authserver.nuaa.edu.cn/authserver/serviceValidate?service=http://10.0.120.43/index&ticket=" + ticket + "")
                .execute()
                .body();

        log.info("loginV2, response:{}", response);

        UserInfo userInfo = CasUtil.getUserInfoByResponse(response);

        log.info("loginV2, userInfo:{}", JSON.toJSONString(userInfo));

//        UserInfo userInfo = CasUtil.getUserInfoFromCas(request);
////        UserInfo userInfo = JSON.parseObject("{\n" +
////                "  \"userName\": \"userName_61de383c6cf3\",\n" +
////                "  \"userAccount\": \"userAccount_471ccd278325\",\n" +
////                "  \"attributes\": {}\n" +
////                "}", UserInfo.class);
        if (Objects.isNull(userInfo)) {
            throw new RuntimeException("登录凭证过期");
        }
        //使用传参中的学号||工号。匹配用户信息
        SysUser sysUser = userService.selectUserByUserName(userInfo.getUserAccount());

        //用户为空，创建新用户
        if (Objects.isNull(sysUser)) {
            sysUser = new SysUser();

            sysUser.setUserName(userInfo.getUserAccount());
            sysUser.setNickName(userInfo.getUserName());
            sysUser.setPassword(SecurityUtils.encryptPassword("Szjh2024!@#"));

            //权限默认为老师
            Long[] roleIds = new Long[1];
            roleIds[0] = 100L;

            sysUser.setRoleIds(roleIds);

            userService.insertUser(sysUser);

            sysUser = userService.selectUserById(sysUser.getUserId());
        }

        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser,
                permissionService.getMenuPermission(sysUser));
        loginUser.setUser(sysUser);
        loginUser.setUserId(sysUser.getUserId());

        return tokenService.createToken(loginUser);
    }
}
