<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TContractInfoMapper">

    <resultMap type="TContractInfo" id="TContractInfoResult">
        <result property="id"    column="id"    />
        <result property="contractName"    column="contract_name"    />
        <result property="fileIds"    column="file_ids"    />
        <result property="itemIds"    column="item_ids"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="endTime"    column="end_time"    />
        <result property="supplier"    column="supplier"    />
        <result property="calculateAmount"    column="calculate_amount"    />
        <result property="checkAmount"    column="check_amount"    />
        <result property="invoiceAmount"    column="invoice_amount"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="planId"    column="plan_id"    />
        <result property="planAmount"    column="plan_amount"    />
        <result property="purchaseType"    column="purchase_type"    />
        <result property="economyType"    column="economy_type"    />
        <result property="detailType"    column="detail_type"    />
        <result property="payType"    column="pay_type"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payPhone"    column="pay_phone"    />
        <result property="itemName"    column="item_name"    />
    </resultMap>

    <sql id="selectTContractInfoVo">
        select id,
               contract_name,
               file_ids,
               item_ids,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               contract_number,
               total_amount,
               end_time,
               supplier,
               calculate_amount,
               check_amount,
               invoice_amount,
               payment_amount,
               plan_id,
               plan_amount,
               purchase_type,
               economy_type,
               detail_type,
               pay_type,
               pay_amount,
               pay_phone
        from t_contract_info
    </sql>

    <select id="selectTContractInfoList" parameterType="TContractInfo" resultMap="TContractInfoResult">
        SELECT
        c.*,
        i.item_name
        FROM
        t_contract_info c
        LEFT JOIN t_item_info i ON c.item_ids = i.id
        WHERE
        c.STATUS = 1
        AND i.STATUS = 1
            <if test="contractName != null  and contractName != ''"> and c.contract_name like concat('%', #{contractName}, '%')</if>
            <if test="fileIds != null  and fileIds != ''"> and c.file_ids = #{fileIds}</if>
            <if test="itemIds != null  and itemIds != ''"> and c.item_ids = #{itemIds}</if>
            <if test="contractNumber != null  and contractNumber != ''"> and c.contract_number = #{contractNumber}</if>
            <if test="payType != null  and payType != ''"> and c.pay_type = #{payType}</if>
            <if test="payAmount != null "> and c.pay_amount = #{payAmount}</if>
            <if test="payPhone != null  and payPhone != ''"> and c.pay_phone = #{payPhone}</if>
            <if test="itemName != null  and itemName != ''"> and i.item_name like concat('%', #{itemName}, '%')</if>
        order by c.create_time desc
    </select>

    <select id="selectTContractInfoById" parameterType="Long" resultMap="TContractInfoResult">
        SELECT
            c.*,
            i.item_name
        FROM
            t_contract_info c
                LEFT JOIN t_item_info i ON c.item_ids = i.id
        WHERE
            c.STATUS = 1
          AND i.STATUS = 1
          and c.id = #{id}
    </select>

    <select id="selectByItemIds" resultMap="TContractInfoResult">
        SELECT
        c.*,
        i.item_name
        FROM
        t_contract_info c
        LEFT JOIN t_item_info i ON c.item_ids = i.id
        WHERE
        c.STATUS = 1
        AND i.STATUS = 1 and c.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTContractInfo" parameterType="TContractInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_contract_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="contractName != null">contract_name,</if>
            <if test="fileIds != null">file_ids,</if>
            <if test="itemIds != null">item_ids,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="endTime != null">end_time,</if>
            <if test="supplier != null">supplier,</if>
            <if test="calculateAmount != null">calculate_amount,</if>
            <if test="checkAmount != null">check_amount,</if>
            <if test="invoiceAmount != null">invoice_amount,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="planId != null">plan_id,</if>
            <if test="planAmount != null">plan_amount,</if>
            <if test="purchaseType != null">purchase_type,</if>
            <if test="economyType != null">economy_type,</if>
            <if test="detailType != null">detail_type,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="payPhone != null">pay_phone,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="contractName != null">#{contractName},</if>
            <if test="fileIds != null">#{fileIds},</if>
            <if test="itemIds != null">#{itemIds},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="checkAmount != null">#{checkAmount},</if>
            <if test="calculateAmount != null">#{calculateAmount},</if>
            <if test="invoiceAmount != null">#{invoiceAmount},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="planId != null">#{planId},</if>
            <if test="planAmount != null">#{planAmount},</if>
            <if test="purchaseType != null">#{purchaseType},</if>
            <if test="economyType != null">#{economyType},</if>
            <if test="detailType != null">#{detailType},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payPhone != null">#{payPhone},</if>
         </trim>
    </insert>

    <update id="updateTContractInfo" parameterType="TContractInfo">
        update t_contract_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractName != null">contract_name = #{contractName},</if>
            <if test="fileIds != null">file_ids = #{fileIds},</if>
            <if test="itemIds != null">item_ids = #{itemIds},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="checkAmount != null">check_amount = #{checkAmount},</if>
            <if test="calculateAmount != null">calculate_amount = #{calculateAmount},</if>
            <if test="invoiceAmount != null">invoice_amount = #{invoiceAmount},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="planAmount != null">plan_amount = #{planAmount},</if>
            <if test="purchaseType != null">purchase_type = #{purchaseType},</if>
            <if test="economyType != null">economy_type = #{economyType},</if>
            <if test="detailType != null">detail_type = #{detailType},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payPhone != null">pay_phone = #{payPhone},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTContractInfoById" parameterType="Long">
        update t_contract_info set status = 9
        where id = #{id}
    </update>

    <update id="deleteTContractInfoByIds" parameterType="String">
        update t_contract_info set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="deleteTContractInfoByItemIds" parameterType="String">
        update t_contract_info set status = 9 where item_ids in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateInvoiceAndPayment">
        UPDATE t_contract_info
        SET invoice_amount = ( SELECT sum( invoice_amount ) FROM t_execute_plan WHERE contract_id = #{id} AND STATUS = 1 ),
            payment_amount = ( SELECT sum( payment_amount ) FROM t_execute_plan WHERE contract_id = #{id} AND STATUS = 1 )
        WHERE
            id = #{id}

    </update>
</mapper>
