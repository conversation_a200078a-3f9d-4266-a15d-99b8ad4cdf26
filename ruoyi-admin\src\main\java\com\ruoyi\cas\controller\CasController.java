package com.ruoyi.cas.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.framework.cas.bean.UserInfo;
import com.ruoyi.framework.cas.util.CasUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 功能描述：统一身份认证控制器
 */
@Slf4j
@RestController
@RequestMapping
public class CasController {
    
    @Value("${cas.server-url:https://authserver.nuaa.edu.cn/authserver}")
    private String casUrl;
    
    @Value("${app.url:http://10.0.120.43/index}")
    private String appUrl;
    
    /**
     * 获取当前登录用户信息
     * @param request
     * @return
     */

    @GetMapping("/getUserInfo")
    public UserInfo getUserInfo(HttpServletRequest request) {
        log.info("getUserInfo, start:{}", request.getPathInfo());
        return CasUtil.getUserInfoFromCas(request);
    }
    
    /**
     * 退出登录
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping("/logout")
    public void logout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 清除本地会话
        request.getSession().invalidate();
        // 重定向到CAS退出地址
        response.sendRedirect(casUrl + "/logout?service=" + URLEncoder.encode(appUrl, "UTF-8"));
    }
} 