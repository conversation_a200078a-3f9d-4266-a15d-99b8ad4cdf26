package com.ruoyi.tiem.service;

import com.ruoyi.tiem.domain.TProjectAmount;

import java.math.BigDecimal;
import java.util.List;

/**
 * 年度立项总金额Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-28
 */
public interface ITProjectAmountService 
{
    /**
     * 查询年度立项总金额
     * 
     * @param id 年度立项总金额主键
     * @return 年度立项总金额
     */
    public TProjectAmount selectTProjectAmountById(Long id);

    /**
     * 查询年度立项总金额列表
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 年度立项总金额集合
     */
    public List<TProjectAmount> selectTProjectAmountList(TProjectAmount tProjectAmount);

    /**
     * 根据年度查询立项总金额
     *
     * @param year 年度
     * @return 年度立项总金额集合
     */
    public BigDecimal selectAmountOfYear(String year);

    /**
     * 新增年度立项总金额
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    public int insertTProjectAmount(TProjectAmount tProjectAmount);

    /**
     * 修改年度立项总金额
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    public int updateTProjectAmount(TProjectAmount tProjectAmount);

    /**
     * 根据year和itemCategory编辑年度立项总金额
     *
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    public int editTProjectAmount(TProjectAmount tProjectAmount);

    /**
     * 批量删除年度立项总金额
     * 
     * @param ids 需要删除的年度立项总金额主键集合
     * @return 结果
     */
    public int deleteTProjectAmountByIds(Long[] ids);

    /**
     * 删除年度立项总金额信息
     * 
     * @param id 年度立项总金额主键
     * @return 结果
     */
    public int deleteTProjectAmountById(Long id);


    /**
     * 根据year 和 itemCategory 查询
     *
     */
    public TProjectAmount selectTProjectAmountByYearAndCategory(String year, String itemCategory);
}
