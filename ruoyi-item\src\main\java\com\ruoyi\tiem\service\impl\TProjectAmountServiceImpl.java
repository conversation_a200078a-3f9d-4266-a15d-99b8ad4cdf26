package com.ruoyi.tiem.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.tiem.domain.TProjectAmount;
import com.ruoyi.tiem.mapper.TProjectAmountMapper;
import com.ruoyi.tiem.service.ITProjectAmountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 年度立项总金额Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-28
 */
@Service
public class TProjectAmountServiceImpl implements ITProjectAmountService
{
    @Autowired
    private TProjectAmountMapper tProjectAmountMapper;

    /**
     * 查询年度立项总金额
     * 
     * @param id 年度立项总金额主键
     * @return 年度立项总金额
     */
    @Override
    public TProjectAmount selectTProjectAmountById(Long id)
    {
        return tProjectAmountMapper.selectTProjectAmountById(id);
    }

    /**
     * 查询年度立项总金额列表
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 年度立项总金额
     */
    @Override
    public List<TProjectAmount> selectTProjectAmountList(TProjectAmount tProjectAmount)
    {
        return tProjectAmountMapper.selectTProjectAmountList(tProjectAmount);
    }

    @Override
    public BigDecimal selectAmountOfYear(String year) {
        return tProjectAmountMapper.selectAmountOfYear(year);
    }

    /**
     * 新增年度立项总金额
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    @Override
    public int insertTProjectAmount(TProjectAmount tProjectAmount)
    {
        return tProjectAmountMapper.insertTProjectAmount(tProjectAmount);
    }

    /**
     * 修改年度立项总金额
     * 
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    @Override
    public int updateTProjectAmount(TProjectAmount tProjectAmount)
    {
        return tProjectAmountMapper.updateTProjectAmount(tProjectAmount);
    }

    /**
     * 根据year和itemCategory编辑年度立项总金额
     *
     * @param tProjectAmount 年度立项总金额
     * @return 结果
     */
    @Override
    public int editTProjectAmount(TProjectAmount tProjectAmount)
    {
        return tProjectAmountMapper.editTProjectAmount(tProjectAmount);
    }

    /**
     * 批量删除年度立项总金额
     * 
     * @param ids 需要删除的年度立项总金额主键
     * @return 结果
     */
    @Override
    public int deleteTProjectAmountByIds(Long[] ids)
    {
        return tProjectAmountMapper.deleteTProjectAmountByIds(ids);
    }

    /**
     * 删除年度立项总金额信息
     * 
     * @param id 年度立项总金额主键
     * @return 结果
     */
    @Override
    public int deleteTProjectAmountById(Long id)
    {
        return tProjectAmountMapper.deleteTProjectAmountById(id);
    }

    /**
     * 根据year 和 itemCategory 查询
     *
     */
    @Override
    public TProjectAmount selectTProjectAmountByYearAndCategory(String year, String itemCategory){
        return tProjectAmountMapper.selectTProjectAmountByYearAndCategory(year, itemCategory);
    };
}
