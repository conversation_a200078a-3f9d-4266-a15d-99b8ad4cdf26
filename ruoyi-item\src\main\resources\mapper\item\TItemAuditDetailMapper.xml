<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tiem.mapper.TItemAuditDetailMapper">

    <resultMap type="TItemAuditDetail" id="TItemAuditDetailResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="flow"    column="flow"    />
        <result property="nodeId"    column="node_id"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="reason"    column="reason"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="auditDeptName"    column="audit_dept_name"    />
    </resultMap>

    <sql id="selectTItemAuditDetailVo">
        select id, item_id, flow, node_id, audit_status, audit_by, reason, create_time, status, audit_dept_name from t_item_audit_detail
    </sql>

    <select id="selectByItemIdAndFlow" resultMap="TItemAuditDetailResult">
        SELECT * FROM t_item_audit_detail WHERE item_id = #{itemId} AND flow = #{flow} AND status != 9
    </select>

    <select id="selectTItemAuditDetailList" parameterType="TItemAuditDetail" resultMap="TItemAuditDetailResult">
        <include refid="selectTItemAuditDetailVo"/>
        <where>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="flow != null "> and flow = #{flow}</if>
            <if test="nodeId != null "> and node_id = #{nodeId}</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
            <if test="auditBy != null  and auditBy != ''"> and audit_by = #{auditBy}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="auditDeptName != null  and auditDeptName != ''"> and audit_dept_name like concat('%', #{auditDeptName}, '%')</if>
        </where>
    </select>

    <select id="selectTItemAuditDetailById" parameterType="Long" resultMap="TItemAuditDetailResult">
        <include refid="selectTItemAuditDetailVo"/>
        where id = #{id}
    </select>

     <select id="selectDetailApprovalRecord" parameterType="Long" resultMap="TItemAuditDetailResult">
        <include refid="selectTItemAuditDetailVo"/>
        where item_id = #{id} and audit_status = '1' and status = '1'
        order by create_time desc
    </select>

    <insert id="insertTItemAuditDetail" parameterType="TItemAuditDetail" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_audit_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="flow != null">flow,</if>
            <if test="nodeId != null">node_id,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="auditDeptName != null">audit_dept_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="flow != null">#{flow},</if>
            <if test="nodeId != null">#{nodeId},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="auditDeptName != null">#{auditDeptName},</if>
         </trim>
    </insert>

    <update id="updateTItemAuditDetail" parameterType="TItemAuditDetail">
        update t_item_audit_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="flow != null">flow = #{flow},</if>
            <if test="nodeId != null">node_id = #{nodeId},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditDeptName != null">audit_dept_name = #{auditDeptName},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTItemAuditDetailById" parameterType="Long">
        update t_item_audit_detail set status = 9
        where id = #{id}
    </update>

    <update id="deleteTItemAuditDetailByIds" parameterType="String">
        update t_item_audit_detail set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
