package com.ruoyi.tiem.domain;

import com.ruoyi.common.core.domain.BaseEntity;

public class ContractPaymentParams extends BaseEntity {
    private String year;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    private Long itemId;

    private Long contractId;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    @Override
    public String toString() {
        return "ContractPaymentParams{" +
                "year='" + year + '\'' +
                ", itemId=" + itemId +
                ", contractNumber='" + contractId + '\'' +
                '}';
    }
}
