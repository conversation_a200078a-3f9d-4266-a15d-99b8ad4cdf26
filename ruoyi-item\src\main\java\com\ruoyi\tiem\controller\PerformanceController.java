package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemInfo;
import com.ruoyi.tiem.mapper.PerformanceMapper;
import com.ruoyi.tiem.mapper.TItemInfoMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tiem.domain.Performance;
import com.ruoyi.tiem.service.IPerformanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目绩效信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/performance/info")
public class PerformanceController extends BaseController
{
    @Autowired
    private IPerformanceService performanceService;

    @Autowired
    private PerformanceMapper performanceMapper;

    @Autowired
    private TItemInfoMapper itemInfoMapper;

    /**
     * 查询项目绩效信息列表
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(Performance performance)
    {
        startPage();
        List<Performance> list = performanceService.selectPerformanceList(performance);
        for (Performance performance1 : list) {
            String itemName = itemInfoMapper.selectTItemInfoById(performance1.getItemId()).getItemName();
            performance1.getParams().put("itemName", itemName);
        }
        return getDataTable(list);
    }

    /**
     * 导出项目绩效信息列表
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:export')")
    @Log(title = "项目绩效信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Performance performance)
    {
        List<Performance> list = performanceService.selectPerformanceList(performance);
        ExcelUtil<Performance> util = new ExcelUtil<Performance>(Performance.class);
        util.exportExcel(response, list, "项目绩效信息数据");
    }

    /**
     * 获取项目绩效信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(performanceService.selectPerformanceById(id));
    }

    /**
     * 新增项目绩效信息
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:add')")
    @Log(title = "项目绩效信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Performance performance)
    {
        return toAjax(performanceService.insertPerformance(performance));
    }

    /**
     * 修改项目绩效信息
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:edit')")
    @Log(title = "项目绩效信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Performance performance)
    {
        return toAjax(performanceService.updatePerformance(performance));
    }

    /**
     * 批量修改项目绩效信息
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:edit')")
    @Log(title = "项目绩效信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editInBatches")
    public AjaxResult editInBatches(@RequestBody List<Performance> list)
    {
        int result = 200;
        for (Performance performance : list) {
            result = performanceService.updatePerformance(performance);
        }
        return toAjax(result);
    }

    /**
     * 删除项目绩效信息
     */
//    @PreAuthorize("@ss.hasPermi('performance:info:remove')")
    @Log(title = "项目绩效信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(performanceService.deletePerformanceByIds(ids));
    }

    /**
     * 查询项目分类汇总绩效信息列表
     */
    @GetMapping("/listByCategory")
    public AjaxResult listByCategory(String category, String year)
    {
        // 首先根据项目类别和执行年度查询绩效指标信息
        List<Performance> list = performanceMapper.selectPerformanceByCategory(category, year);

        // 如果list为空，则根据各个项目绩效信息，进行汇总查询
        if (list == null || list.size() == 0) {
            list = performanceMapper.selectItemPerformanceByCategory(category, year);
        }
        return AjaxResult.success(list);
    }
}
