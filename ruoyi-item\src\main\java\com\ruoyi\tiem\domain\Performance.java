package com.ruoyi.tiem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目绩效信息对象 t_performance
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public class Performance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 一级指标 */
    @Excel(name = "一级指标")
    private String firstIndex;

    /** 二级指标 */
    @Excel(name = "二级指标")
    private String secondIndex;

    /** 三级指标 */
    @Excel(name = "三级指标")
    private String threeIndex;

    /** 指标符号 */
    @Excel(name = "指标符号")
    private String symbol;

    /** 指标值 */
    @Excel(name = "指标值")
    private String indexValue;

    /** 指标单位 */
    @Excel(name = "指标单位")
    private String indexUnit;

    /** 实际完成值 */
    @Excel(name = "实际完成值")
    private String completionValue;

    /** 分值 */
    @Excel(name = "分值")
    private Integer scoreValue;

    /** 得分 */
    @Excel(name = "得分")
    private Integer score;

    /** 偏差原因分析及改进措施 */
    @Excel(name = "偏差原因分析及改进措施")
    private String reason;

    /** 项目类别 */
    @Excel(name = "项目类别")
    private String itemCategory;

    public String getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory;
    }

    public String getExecuteYear() {
        return executeYear;
    }

    public void setExecuteYear(String executeYear) {
        this.executeYear = executeYear;
    }

    /** 执行年度 */
    @Excel(name = "执行年度")
    private String executeYear;

    /** 状态 1：正常 2：删除 */
    @Excel(name = "状态 1：正常 2：删除")
    private String status;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFirstIndex(String firstIndex) 
    {
        this.firstIndex = firstIndex;
    }

    public String getFirstIndex() 
    {
        return firstIndex;
    }
    public void setSecondIndex(String secondIndex) 
    {
        this.secondIndex = secondIndex;
    }

    public String getSecondIndex() 
    {
        return secondIndex;
    }
    public void setThreeIndex(String threeIndex) 
    {
        this.threeIndex = threeIndex;
    }

    public String getThreeIndex() 
    {
        return threeIndex;
    }
    public void setIndexValue(String indexValue)
    {
        this.indexValue = indexValue;
    }

    public String getIndexValue()
    {
        return indexValue;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getIndexUnit() {
        return indexUnit;
    }

    public void setIndexUnit(String indexUnit) {
        this.indexUnit = indexUnit;
    }

    public String getCompletionValue() {
        return completionValue;
    }

    public void setCompletionValue(String completionValue) {
        this.completionValue = completionValue;
    }

    public Integer getScoreValue() {
        return scoreValue;
    }

    public void setScoreValue(Integer scoreValue) {
        this.scoreValue = scoreValue;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("firstIndex", getFirstIndex())
            .append("secondIndex", getSecondIndex())
            .append("threeIndex", getThreeIndex())
            .append("symbol", getSymbol())
            .append("indexValue", getIndexValue())
            .append("indexUnit", getIndexUnit())
            .append("completionValue", getCompletionValue())
            .append("scoreValue", getScoreValue())
            .append("score", getScore())
            .append("reason", getReason())
            .append("status", getStatus())
            .append("itemId", getItemId())
            .append("itemCategory", getItemCategory())
            .append("executeYear", getExecuteYear())
            .toString();
    }
}
