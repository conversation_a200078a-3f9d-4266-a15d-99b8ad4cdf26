package com.ruoyi.tiem.service;

import com.ruoyi.tiem.domain.TItemBudget;

import java.util.List;

/**
 * 项目支出预算明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface ITItemBudgetService 
{
    /**
     * 查询项目支出预算明细
     * 
     * @param id 项目支出预算明细主键
     * @return 项目支出预算明细
     */
    public TItemBudget selectTItemBudgetById(Long id);

    /**
     * 查询项目支出预算明细列表
     * 
     * @param tItemBudget 项目支出预算明细
     * @return 项目支出预算明细集合
     */
    public List<TItemBudget> selectTItemBudgetList(TItemBudget tItemBudget);

    /**
     * 新增项目支出预算明细
     * 
     * @param tItemBudget 项目支出预算明细
     * @return 结果
     */
    public int insertTItemBudget(TItemBudget tItemBudget);

    /**
     * 修改项目支出预算明细
     * 
     * @param tItemBudget 项目支出预算明细
     * @return 结果
     */
    public int updateTItemBudget(TItemBudget tItemBudget);

    /**
     * 批量删除项目支出预算明细
     * 
     * @param ids 需要删除的项目支出预算明细主键集合
     * @return 结果
     */
    public int deleteTItemBudgetByIds(Long[] ids);

    /**
     * 删除项目支出预算明细信息
     * 
     * @param id 项目支出预算明细主键
     * @return 结果
     */
    public int deleteTItemBudgetById(Long id);
}
