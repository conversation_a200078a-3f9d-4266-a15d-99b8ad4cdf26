package com.ruoyi.tiem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.tiem.domain.TItemBudget;
import com.ruoyi.tiem.service.ITItemBudgetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目支出预算明细Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/system/budget")
public class TItemBudgetController extends BaseController
{
    @Autowired
    private ITItemBudgetService tItemBudgetService;

    /**
     * 查询项目支出预算明细列表
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:list')")
    @GetMapping("/list")
    public TableDataInfo list(TItemBudget tItemBudget)
    {
        startPage();
        List<TItemBudget> list = tItemBudgetService.selectTItemBudgetList(tItemBudget);
        return getDataTable(list);
    }

    /**
     * 导出项目支出预算明细列表
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:export')")
    @Log(title = "项目支出预算明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TItemBudget tItemBudget)
    {
        List<TItemBudget> list = tItemBudgetService.selectTItemBudgetList(tItemBudget);
        ExcelUtil<TItemBudget> util = new ExcelUtil<TItemBudget>(TItemBudget.class);
        util.exportExcel(response, list, "项目支出预算明细数据");
    }

    /**
     * 获取项目支出预算明细详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tItemBudgetService.selectTItemBudgetById(id));
    }

    /**
     * 新增项目支出预算明细
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:add')")
    @Log(title = "项目支出预算明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TItemBudget tItemBudget)
    {
        return toAjax(tItemBudgetService.insertTItemBudget(tItemBudget));
    }

    /**
     * 批量新增项目支出预算明细
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:add')")
    @Log(title = "项目支出预算明细", businessType = BusinessType.INSERT)
    @PostMapping("/addInBatches")
    public AjaxResult addInBatches(@RequestBody List<TItemBudget> list)
    {
        int result = 0;
        for (TItemBudget tItemBudget : list) {
            result = tItemBudgetService.insertTItemBudget(tItemBudget);
        }
        return toAjax(result);
    }

    /**
     * 批量修改项目支出预算明细
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:edit')")
    @Log(title = "项目支出预算明细", businessType = BusinessType.UPDATE)
    @PutMapping("/editInBatches")
    public AjaxResult editInBatches(@RequestBody List<TItemBudget> list)
    {
        for (TItemBudget tItemBudget : list) {
            if (tItemBudget.getId() != null ) { // 判断id是否存在
                tItemBudgetService.updateTItemBudget(tItemBudget); // 执行更新操作
            } else {
                tItemBudgetService.insertTItemBudget(tItemBudget); // 执行新增操作
            }
        }
        return AjaxResult.success();
    }

    /**
     * 修改项目支出预算明细
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:edit')")
    @Log(title = "项目支出预算明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TItemBudget tItemBudget)
    {
        return toAjax(tItemBudgetService.updateTItemBudget(tItemBudget));
    }

    /**
     * 删除项目支出预算明细
     */
//    @PreAuthorize("@ss.hasPermi('system:budget:remove')")
    @Log(title = "项目支出预算明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tItemBudgetService.deleteTItemBudgetByIds(ids));
    }
}
