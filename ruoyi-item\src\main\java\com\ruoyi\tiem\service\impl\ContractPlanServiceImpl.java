package com.ruoyi.tiem.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.ContractPlanMapper;
import com.ruoyi.tiem.domain.ContractPlan;
import com.ruoyi.tiem.service.IContractPlanService;

/**
 * 合约规划信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
public class ContractPlanServiceImpl implements IContractPlanService 
{
    @Autowired
    private ContractPlanMapper contractPlanMapper;

    /**
     * 查询合约规划信息
     * 
     * @param id 合约规划信息主键
     * @return 合约规划信息
     */
    @Override
    public ContractPlan selectContractPlanById(Long id)
    {
        return contractPlanMapper.selectContractPlanById(id);
    }

    /**
     * 查询合约规划信息列表
     * 
     * @param contractPlan 合约规划信息
     * @return 合约规划信息
     */
    @Override
    public List<ContractPlan> selectContractPlanList(ContractPlan contractPlan)
    {
        return contractPlanMapper.selectContractPlanList(contractPlan);
    }

    @Override
    public List<ContractPlan> selectV2ContractPlanList(Long itemId) {
        return contractPlanMapper.selectV2ContractPlanList(itemId);
    }

    /**
     * 新增合约规划信息
     * 
     * @param contractPlan 合约规划信息
     * @return 结果
     */
    @Override
    public int insertContractPlan(ContractPlan contractPlan)
    {
        return contractPlanMapper.insertContractPlan(contractPlan);
    }

    /**
     * 修改合约规划信息
     * 
     * @param contractPlan 合约规划信息
     * @return 结果
     */
    @Override
    public int updateContractPlan(ContractPlan contractPlan)
    {
        return contractPlanMapper.updateContractPlan(contractPlan);
    }

    /**
     * 批量删除合约规划信息
     * 
     * @param ids 需要删除的合约规划信息主键
     * @return 结果
     */
    @Override
    public int deleteContractPlanByIds(Long[] ids)
    {
        return contractPlanMapper.deleteContractPlanByIds(ids);
    }

    /**
     * 删除合约规划信息信息
     * 
     * @param id 合约规划信息主键
     * @return 结果
     */
    @Override
    public int deleteContractPlanById(Long id)
    {
        return contractPlanMapper.deleteContractPlanById(id);
    }
}
