package com.ruoyi.tiem.mapper;

import java.util.List;
import com.ruoyi.tiem.domain.TItemAudit;
import com.ruoyi.tiem.domain.TItemAuditDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 项目审核信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface TItemAuditMapper 
{
    /**
     * 查询项目审核信息
     * 
     * @param id 项目审核信息主键
     * @return 项目审核信息
     */
    public TItemAudit selectTItemAuditById(Long id);

    /**
     * 查询某个项目具体节点的审核状态
     *
     * @param flow 节点
     * @param id 项目id
     * @return 项目审核信息
     */
    public String selectFlowStatusByItemId(@Param("flow") int flow, @Param("id") Long id);

    /**
     * 查询项目审核信息列表
     * 
     * @param tItemAudit 项目审核信息
     * @return 项目审核信息集合
     */
    public List<TItemAudit> selectTItemAuditList(TItemAudit tItemAudit);

    /**
     * 查询项目审批记录
     * 
     * @param id 项目ID
     * @return 项目审核信息
     */
    public List<TItemAudit> selectApprovalRecord(Long id);

    /**
     * 新增项目审核信息
     * 
     * @param tItemAudit 项目审核信息
     * @return 结果
     */
    public int insertTItemAudit(TItemAudit tItemAudit);

    /**
     * 修改项目审核信息
     * 
     * @param tItemAudit 项目审核信息
     * @return 结果
     */
    public int updateTItemAudit(TItemAudit tItemAudit);

    /**
     * 删除项目审核信息
     * 
     * @param id 项目审核信息主键
     * @return 结果
     */
    public int deleteTItemAuditById(Long id);

    /**
     * 批量删除项目审核信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTItemAuditByIds(Long[] ids);

    public List<TItemAudit> selectByItemIds(@Param("itemIds") List<String> itemIds);

    public void batchUpdateTItemAudit(@Param("audits") List<TItemAudit> audits);

}
