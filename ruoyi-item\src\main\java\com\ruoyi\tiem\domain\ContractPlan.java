package com.ruoyi.tiem.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合约规划信息对象 t_contract_plan
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public class ContractPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 合约名称 */
    @Excel(name = "合约名称")
    private String contractName;

    /** 合约类型：V1,V2,V3 */
    @Excel(name = "合约类型")
    private String contractType;

    /** 用途 */
    @Excel(name = "用途")
    private String purpose;

    /** 合约金额 */
    @Excel(name = "合约金额")
    private BigDecimal contractAmount;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    /** 是否政采分类 政采指标、非政采指标 */
    @Excel(name = "是否政采分类 政采指标、非政采指标")
    private String purchaseType;

    /** 政府预算经济分类 */
    @Excel(name = "政府预算经济分类")
    private String economyType;

    /** 明细分类 */
    @Excel(name = "明细分类")
    private String detailType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setContractName(String contractName)
    {
        this.contractName = contractName;
    }

    public String getContractName()
    {
        return contractName;
    }
    public void setPurpose(String purpose)
    {
        this.purpose = purpose;
    }

    public String getPurpose()
    {
        return purpose;
    }
    public void setContractAmount(BigDecimal contractAmount)
    {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getContractAmount()
    {
        return contractAmount;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }

    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(final String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getEconomyType() {
        return economyType;
    }

    public void setEconomyType(final String economyType) {
        this.economyType = economyType;
    }

    public String getDetailType() {
        return detailType;
    }

    public void setDetailType(final String detailType) {
        this.detailType = detailType;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("contractName", getContractName())
            .append("contractType", getContractType())
            .append("purpose", getPurpose())
            .append("contractAmount", getContractAmount())
            .append("status", getStatus())
            .append("itemId", getItemId())
            .toString();
    }
}
