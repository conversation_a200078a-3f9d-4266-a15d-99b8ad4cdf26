package com.ruoyi.tiem.service;

import com.ruoyi.tiem.domain.TImplementationRate;

import java.util.List;

/**
 * 项目目标考核率Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface ITImplementationRateService 
{
    /**
     * 查询项目目标考核率
     * 
     * @param id 项目目标考核率主键
     * @return 项目目标考核率
     */
    public TImplementationRate selectTImplementationRateById(Long id);

    /**
     * 查询项目目标考核率列表
     * 
     * @param tImplementationRate 项目目标考核率
     * @return 项目目标考核率集合
     */
    public List<TImplementationRate> selectTImplementationRateList(TImplementationRate tImplementationRate);

    /**
     * 新增项目目标考核率
     * 
     * @param tImplementationRate 项目目标考核率
     * @return 结果
     */
    public int insertTImplementationRate(TImplementationRate tImplementationRate);

    /**
     * 修改项目目标考核率
     * 
     * @param tImplementationRate 项目目标考核率
     * @return 结果
     */
    public int updateTImplementationRate(TImplementationRate tImplementationRate);

    /**
     * 批量删除项目目标考核率
     * 
     * @param ids 需要删除的项目目标考核率主键集合
     * @return 结果
     */
    public int deleteTImplementationRateByIds(Long[] ids);

    /**
     * 删除项目目标考核率信息
     * 
     * @param id 项目目标考核率主键
     * @return 结果
     */
    public int deleteTImplementationRateById(Long id);
}
