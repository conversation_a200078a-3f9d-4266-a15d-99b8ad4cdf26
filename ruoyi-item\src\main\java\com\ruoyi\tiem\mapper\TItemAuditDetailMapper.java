package com.ruoyi.tiem.mapper;

import java.util.List;

import com.ruoyi.tiem.domain.TItemAudit;
import com.ruoyi.tiem.domain.TItemAuditDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 多流程审核明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface TItemAuditDetailMapper 
{
    /**
     * 查询多流程审核明细
     * 
     * @param id 多流程审核明细主键
     * @return 多流程审核明细
     */
    public TItemAuditDetail selectTItemAuditDetailById(Long id);

    /**
     * 查询多流程审核明细列表
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 多流程审核明细集合
     */
    public List<TItemAuditDetail> selectTItemAuditDetailList(TItemAuditDetail tItemAuditDetail);

    /**
     * 新增多流程审核明细
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 结果
     */
    public int insertTItemAuditDetail(TItemAuditDetail tItemAuditDetail);

    /**
     * 修改多流程审核明细
     * 
     * @param tItemAuditDetail 多流程审核明细
     * @return 结果
     */
    public int updateTItemAuditDetail(TItemAuditDetail tItemAuditDetail);

    /**
     * 删除多流程审核明细
     * 
     * @param id 多流程审核明细主键
     * @return 结果
     */
    public int deleteTItemAuditDetailById(Long id);

    /**
     * 批量删除多流程审核明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTItemAuditDetailByIds(Long[] ids);

    List<TItemAuditDetail> selectByItemIdAndFlow(@Param("itemId") Long itemId, @Param("flow") Integer flow);


    /**
     * 查询项目审批记录
     *
     * @param id 项目ID
     * @return 项目审核信息
     */
    public List<TItemAuditDetail> selectDetailApprovalRecord(Long id);
}
