package com.ruoyi.tiem.service;

import java.util.List;
import com.ruoyi.tiem.domain.TContractInfo;

/**
 * 合同信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface ITContractInfoService 
{
    /**
     * 查询合同信息
     * 
     * @param id 合同信息主键
     * @return 合同信息
     */
    public TContractInfo selectTContractInfoById(Long id);

    /**
     * 查询合同信息列表
     * 
     * @param tContractInfo 合同信息
     * @return 合同信息集合
     */
    public List<TContractInfo> selectTContractInfoList(TContractInfo tContractInfo);

    /**
     * 新增合同信息
     * 
     * @param tContractInfo 合同信息
     * @return 结果
     */
    public int insertTContractInfo(TContractInfo tContractInfo);

    /**
     * 修改合同信息
     * 
     * @param tContractInfo 合同信息
     * @return 结果
     */
    public int updateTContractInfo(TContractInfo tContractInfo);

    /**
     * 批量删除合同信息
     * 
     * @param ids 需要删除的合同信息主键集合
     * @return 结果
     */
    public int deleteTContractInfoByIds(Long[] ids);

    /**
     * 删除合同信息信息
     * 
     * @param id 合同信息主键
     * @return 结果
     */
    public int deleteTContractInfoById(Long id);
}
