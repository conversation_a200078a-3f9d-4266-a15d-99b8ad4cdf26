package com.ruoyi;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * 部门数据同步客户端
 * 用于从外部API获取部门数据并同步到系统中
 * <p>
 * 实现了CommandLineRunner接口，可以在Spring Boot应用启动时执行
 * 但默认不会自动执行同步，需要手动调用main方法或syncDepartmentData方法
 */
@Component
public class RestfulClient implements CommandLineRunner {

    /**
     * 注入部门数据访问层Mapper
     */
    @Resource
    private SysDeptMapper deptMapper;

    /**
     * Spring Boot启动时执行的方法
     * 当前配置为不自动执行同步，需要手动调用main方法
     */
    @Override
    public void run(String... args) {
        // 在程序启动时不自动执行同步，需要手动调用main方法执行
    }

    /**
     * 主方法，用于手动启动数据同步
     * 先初始化Spring应用上下文，再获取RestfulClient实例调用同步方法
     *
     * @param args 命令行参数，未使用
     */
    public static void main(String[] args) {
        // 创建Spring上下文环境并启动应用
        ConfigurableApplicationContext context = SpringApplication.run(RuoYiApplication.class, args);

        try {
            // 从Spring容器中获取RestfulClient实例
            RestfulClient client = context.getBean(RestfulClient.class);

            // 调用同步方法
            System.out.println("开始执行部门数据同步...");
            client.syncDepartmentData();
            System.out.println("部门数据同步结束！");
        } catch (Exception e) {
            System.out.println("部门数据同步异常：" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭Spring上下文
            if (context != null) {
                context.close();
            }
        }
    }

    /**
     * 同步部门数据主方法
     * 负责从外部API获取数据，然后调用处理方法进行同步
     */
    @Transactional
    public void syncDepartmentData() {
        HttpURLConnection connection = null;
        try {
            // API访问地址
            String url = "http://ods4.nuaa.edu.cn/openApi/API/getREST_ZZJG_EJBM_20210225_0a4d691fea5748109ea0a3e707e5c401";

            // 构建请求参数
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("page", 1);           // 页码，从第1页开始
            jsonObject.put("pagesize", 2000);    // 每页数据量，设置为2000以获取全量数据

            // 以下为可选参数，当前未使用
            // JSONObject orderParams = new JSONObject();
            // orderParams.put("排序字段名", "DESC");
            // jsonObject.put("order", orderParams);
            //orderParams 为排序参数,根据传入的排序字段与排序规则对数据进行排序
            // JSONObject params = new JSONObject();
            // params.put("入参参数名", "入参参数值");
            // jsonObject.put("params", params);

            // 创建HTTP连接
            URL apiUrl = new URL(url);
            connection = (HttpURLConnection) apiUrl.openConnection();
            connection.setRequestMethod("POST");        // 设置请求方法为POST
            connection.setConnectTimeout(10000);        // 连接超时时间：10秒
            connection.setReadTimeout(10000);           // 读取超时时间：10秒

            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json");  // 内容类型为JSON
            connection.setRequestProperty("applyId", "37226788704198656");      // 应用ID
            connection.setRequestProperty("secretKey", "a2c4f2f1c28a4bb9b64b762db1ff50a0"); // 密钥

            // 设置允许输入输出
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 写入请求体（JSON格式）
            String requestBody = jsonObject.toString();
            try (OutputStream os = connection.getOutputStream()) {
                byte[] requestBodyBytes = requestBody.getBytes("UTF-8");
                os.write(requestBodyBytes, 0, requestBodyBytes.length);
                os.flush();
            }

            // 读取响应
            StringBuilder response = new StringBuilder();
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {  // HTTP状态码200表示成功
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                // 处理响应数据并同步到数据库
                processAndSyncData(response.toString());

            } else {
                System.out.println("请求失败，响应码：" + responseCode);
            }

        } catch (MalformedURLException e) {
            // URL格式异常
            e.printStackTrace();
        } catch (IOException e) {
            // IO异常
            e.printStackTrace();
        } finally {
            // 断开连接
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    /**
     * 处理API返回的数据并同步到sys_dept表
     * 采用三阶段处理策略：
     * 1. 先根据记录中的dwdlmc和dwdldm创建/更新顶级部门
     * 2. 然后根据记录中的dwxlmc和dwxldm创建/更新次级部门(如果存在)
     * 3. 最后将记录中的具体部门(mc和dm)归类到对应的次级部门下(如果有)或直接归类到顶级部门下
     *
     * @param responseData API返回的JSON字符串
     */
    @Transactional
    public void processAndSyncData(String responseData) {
        try {
            // 解析JSON响应
            JSONObject jsonResponse = JSONObject.parseObject(responseData);
            
            String message = jsonResponse.getString("msg");
            if ("成功".equals(message)) {
                // 获取数据记录
                JSONObject data = jsonResponse.getJSONObject("data");
                // 获取Rows属性下的字段数据记录
                JSONArray records = data.getJSONArray("Rows");
                int total = records.size();

                System.out.println("开始同步部门数据，总记录数：" + total);

                // 初始化计数器
                int insertCount = 0; // 新增记录计数
                int updateCount = 0; // 更新记录计数
                int skipCount = 0;   // 跳过记录计数
                int errorCount = 0;  // 错误记录计数

                // 记录已处理的部门代码
                Set<String> processedTopDeptCodes = new HashSet<>();
                Set<String> processedSecondDeptKeys = new HashSet<>();
                Set<String> processedDeptCodes = new HashSet<>();
                
                // 第一阶段：处理顶级部门
                System.out.println("第一阶段：处理顶级部门...");
                
                for (int i = 0; i < records.size(); i++) {
                    // 获取数据对象
                    JSONObject deptJson = records.getJSONObject(i);
                    String dwdldm = deptJson.getString("dwdldm"); // 部门大类代码
                    String dwdlmc = deptJson.getString("dwdlmc"); // 部门大类名称
                    
                    // 跳过已处理过的大类代码或大类代码为空
                    if (StringUtils.isEmpty(dwdldm) || processedTopDeptCodes.contains(dwdldm)) {
                        continue;
                    }
                    
                    // 先查询数据库中是否已存在该顶级部门
                    SysDept existDept = selectDeptByDm(dwdldm);
                    
                    if (existDept != null) {
                        // 部门已存在，执行更新操作
                        existDept.setDeptName(dwdlmc);
                        existDept.setUpdateBy("admin");
                        existDept.setUpdateTime(new Date());
                        existDept.setStatus("0");
                        existDept.setDelFlag("0");
                        
                        // 执行更新
                        deptMapper.updateDept(existDept);
                        updateCount++;
                        System.out.println("更新顶级部门: " + dwdlmc + " (代码: " + dwdldm + ")");
                    } else {
                        // 部门不存在，创建新部门
                        SysDept topDept = new SysDept();
                        topDept.setDeptName(dwdlmc);
                        topDept.setDm(dwdldm);         // 使用大类代码作为部门代码
                        topDept.setParentId(0L);       // 顶级部门，父ID为0
                        topDept.setAncestors("0");     // 顶级部门，祖先为0
                        topDept.setOrderNum(1);
                        topDept.setStatus("0");
                        topDept.setDelFlag("0");
                        topDept.setCreateBy("admin");
                        topDept.setCreateTime(new Date());
                        
                        // 插入顶级部门
                        deptMapper.insertDept(topDept);
                        insertCount++;
                        System.out.println("创建顶级部门: " + dwdlmc + " (代码: " + dwdldm + ")");
                    }
                    
                    // 记录已处理的顶级部门
                    processedTopDeptCodes.add(dwdldm);
                }
                
                // 第二阶段：处理次级部门
                System.out.println("第二阶段：处理次级部门...");
                
                for (int i = 0; i < records.size(); i++) {
                    JSONObject deptJson = records.getJSONObject(i);
                    String dwdldm = deptJson.getString("dwdldm");
                    String dwxldm = deptJson.getString("dwxldm");
                    String dwxlmc = deptJson.getString("dwxlmc");
                    
                    // 如果小类代码或小类名称为空，则该记录没有次级部门，跳过
                    if (StringUtils.isEmpty(dwxldm) || StringUtils.isEmpty(dwxlmc)) {
                        continue;
                    }
                    
                    // 生成次级部门的唯一键
                    String secondDeptKey = dwdldm + ":" + dwxldm;
                    
                    // 跳过已处理过的次级部门
                    if (processedSecondDeptKeys.contains(secondDeptKey)) {
                        continue;
                    }
                    
                    // 先查询数据库中是否已存在该次级部门
                    SysDept existDept = selectDeptByDm(dwxldm);
                    
                    // 从数据库中查询父部门（大类部门）的ID
                    SysDept parentDept = deptMapper.selectDeptByDm(dwdldm);
                    if (parentDept == null) {
                        System.out.println("警告: 无法找到次级部门 " + dwxlmc + " 对应的顶级部门，代码: " + dwdldm);
                        continue;
                    }
                    
                    if (existDept != null) {
                        // 次级部门已存在，执行更新操作
                        existDept.setDeptName(dwxlmc);
                        existDept.setParentId(parentDept.getDeptId());  // 更新父ID
                        existDept.setAncestors(parentDept.getAncestors() + "," + parentDept.getDeptId()); // 更新祖先链
                        existDept.setUpdateBy("admin");
                        existDept.setUpdateTime(new Date());
                        existDept.setStatus("0");
                        existDept.setDelFlag("0");
                        
                        // 执行更新
                        deptMapper.updateDept(existDept);
                        updateCount++;
                        System.out.println("更新次级部门: " + dwxlmc + " (代码: " + dwxldm + ") 隶属于: " + parentDept.getDeptName());
                    } else {
                        // 次级部门不存在，创建新部门
                        SysDept secondDept = new SysDept();
                        secondDept.setDeptName(dwxlmc);
                        secondDept.setDm(dwxldm);
                        secondDept.setParentId(parentDept.getDeptId());  // 设置父ID为顶级部门的ID
                        secondDept.setAncestors(parentDept.getAncestors() + "," + parentDept.getDeptId()); // 设置祖先链
                        secondDept.setOrderNum(1);
                        secondDept.setStatus("0");
                        secondDept.setDelFlag("0");
                        secondDept.setCreateBy("admin");
                        secondDept.setCreateTime(new Date());
                        
                        // 插入次级部门
                        deptMapper.insertDept(secondDept);
                        insertCount++;
                        System.out.println("创建次级部门: " + dwxlmc + " (代码: " + dwxldm + ") 隶属于: " + parentDept.getDeptName());
                    }
                    
                    // 记录已处理的次级部门
                    processedSecondDeptKeys.add(secondDeptKey);
                }
                
                // 第三阶段：处理具体部门，归类到对应的次级部门或顶级部门下
                System.out.println("第三阶段：处理具体部门...");
                
                for (int i = 0; i < records.size(); i++) {
                    JSONObject deptJson = records.getJSONObject(i);
                    String deptCode = deptJson.getString("dm");       // 部门代码
                    String deptName = deptJson.getString("mc");       // 部门名称
                    String dwdldm = deptJson.getString("dwdldm");     // 部门大类代码
                    String dwxldm = deptJson.getString("dwxldm");     // 部门小类代码
                    
                    // 如果已处理过该部门代码，跳过
                    if (processedDeptCodes.contains(deptCode)) {
                        skipCount++;
                        continue;
                    }
                    
                    // 查找父部门
                    SysDept parentDept = null;
                    
                    // 如果存在小类代码，则尝试找到对应的次级部门作为父部门
                    if (!StringUtils.isEmpty(dwxldm)) {
                        // 从数据库中查询次级部门
                        parentDept = deptMapper.selectDeptByDm(dwxldm);
                    }
                    
                    // 如果没有找到次级部门或小类代码为空，则使用顶级部门作为父部门
                    if (parentDept == null) {
                        // 从数据库中查询顶级部门
                        parentDept = deptMapper.selectDeptByDm(dwdldm);
                    }
                    
                    // 如果找不到任何父部门，记录错误并跳过
                    if (parentDept == null) {
                        System.out.println("错误: 无法为部门 " + deptName + " (代码: " + deptCode + ") 找到父部门");
                        errorCount++;
                        continue;
                    }
                    
                    // 检查是否与父部门同名，如果同名则跳过，避免重复
//                    if (deptName.equals(parentDept.getDeptName())) {
//                        skipCount++;
//                        System.out.println("跳过与父部门同名的部门: " + deptName);
//                        continue;
//                    }
                    
                    try {
                        // 先查询数据库中是否已存在该部门
                        SysDept existDept = selectDeptByDm(deptCode);
                        
                        if (existDept != null) {
                            // 部门已存在，执行更新操作
                            int[] result = updateDept(deptJson, parentDept, existDept);
                            if (result[1] == 1) {
                                updateCount++;
                            }
                            System.out.println("更新部门: " + deptName + " (代码: " + deptCode + ") 隶属于: " + parentDept.getDeptName());
                        } else {
                            // 部门不存在，执行新增操作
                            int[] result = processOneDept(deptJson, parentDept);
                            if (result[0] == 1) {
                                insertCount++;
                            }
                            System.out.println("创建部门: " + deptName + " (代码: " + deptCode + ") 隶属于: " + parentDept.getDeptName());
                        }
                        
                        // 记录已处理的具体部门
                        processedDeptCodes.add(deptCode);
                        
                    } catch (Exception e) {
                        System.out.println("处理部门 " + deptName + " 时出错: " + e.getMessage());
                        errorCount++;
                    }
                }

                // 输出最终处理结果统计
                System.out.println("部门数据同步完成！新增：" + insertCount + " 条，更新：" + updateCount +
                        " 条，跳过：" + skipCount + " 条，错误：" + errorCount + " 条");
            } else {
                System.out.println("API返回失败：" + jsonResponse.getString("msg"));
            }

        } catch (Exception e) {
            System.out.println("处理部门数据时发生异常：");
            e.printStackTrace();
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 处理单个部门数据（创建新部门）
     *
     * @param deptJson   部门JSON数据
     * @param parentDept 父部门对象，如果是顶级部门则为null
     * @return 结果数组，第一个元素为插入计数，第二个元素为更新计数
     */
    private int[] processOneDept(JSONObject deptJson, SysDept parentDept) {
        // 从API响应中提取部门数据
        String deptCode = deptJson.getString("dm");       // 部门代码
        String deptName = deptJson.getString("mc");       // 部门名称
        String deptJC = deptJson.getString("jc");         // 部门简称
        Integer deptLevel = deptJson.getInteger("cc");    // 部门层次
        String deptDWDLDM = deptJson.getString("dwdldm"); // 部门大类代码
        String deptDWDLMC = deptJson.getString("dwdlmc"); // 部门大类名称
        String deptDWXLDM = deptJson.getString("dwxldm"); // 部门小类代码
        String deptDWXLMC = deptJson.getString("dwxlmc"); // 部门小类名称
        String deptYWMC = deptJson.getString("ywmc");     // 部门英文名称

        int[] result = new int[2]; // [0]=插入计数，[1]=更新计数

        // 创建新部门
        SysDept newDept = new SysDept();
        newDept.setDeptName(deptName);   // 设置部门名称
        newDept.setDm(deptCode);         // 设置部门代码
        newDept.setJc(deptJC);           // 设置部门简称
        newDept.setCc(deptLevel);        // 设置部门层次
        newDept.setDwdldm(deptDWDLDM);   // 设置部门大类代码
        newDept.setDwdlmc(deptDWDLMC);   // 设置部门大类名称
        newDept.setDwxldm(deptDWXLDM);   // 设置部门小类代码
        newDept.setDwxlmc(deptDWXLMC);   // 设置部门小类名称
        if (!StringUtils.isEmpty(deptYWMC)) {
            // 如果有英文名称，设置英文名称到备注字段
            newDept.setRemark(deptYWMC);
        }
        
        newDept.setCreateBy("admin");    // 设置创建者
        newDept.setStatus("0");          // 设置状态为正常
        newDept.setDelFlag("0");         // 设置删除标志为未删除

        // 设置显示顺序（默认为1，可根据需要调整）
        newDept.setOrderNum(1);
        // 设置创建时间（无论是否有父部门）
        newDept.setCreateTime(new Date());

        // 如果有父部门，设置上级部门ID和祖级列表
        if (parentDept != null) {
            newDept.setParentId(parentDept.getDeptId());
            // 设置祖级列表（ancestors字段记录了从顶级部门到父部门的所有ID）
            String ancestors = parentDept.getAncestors() + "," + parentDept.getDeptId();
            newDept.setAncestors(ancestors);
        } else {
            // 没有父部门，设置为顶级部门
            newDept.setParentId(0L);
            newDept.setAncestors("0");
        }

        // 执行插入操作
        deptMapper.insertDept(newDept);
        result[0] = 1; // 插入计数+1

        return result;
    }
    
    /**
     * 更新已存在的部门信息
     *
     * @param deptJson   部门JSON数据
     * @param parentDept 父部门对象
     * @param existDept  已存在的部门对象
     * @return 结果数组，第一个元素为插入计数，第二个元素为更新计数
     */
    private int[] updateDept(JSONObject deptJson, SysDept parentDept, SysDept existDept) {
        // 从API响应中提取部门数据
        String deptName = deptJson.getString("mc");       // 部门名称
        String deptJC = deptJson.getString("jc");         // 部门简称
        Integer deptLevel = deptJson.getInteger("cc");    // 部门层次
        String deptDWDLDM = deptJson.getString("dwdldm"); // 部门大类代码
        String deptDWDLMC = deptJson.getString("dwdlmc"); // 部门大类名称
        String deptDWXLDM = deptJson.getString("dwxldm"); // 部门小类代码
        String deptDWXLMC = deptJson.getString("dwxlmc"); // 部门小类名称
        String deptYWMC = deptJson.getString("ywmc");     // 部门英文名称

        int[] result = new int[2]; // [0]=插入计数，[1]=更新计数

        // 更新部门信息
        existDept.setDeptName(deptName);
        existDept.setJc(deptJC);
        existDept.setCc(deptLevel);
        existDept.setDwdldm(deptDWDLDM);
        existDept.setDwdlmc(deptDWDLMC);
        existDept.setDwxldm(deptDWXLDM);
        existDept.setDwxlmc(deptDWXLMC);
        if (!StringUtils.isEmpty(deptYWMC)) {
            existDept.setRemark(deptYWMC);
        }

        // 更新父部门信息
        if (parentDept != null) {
            existDept.setParentId(parentDept.getDeptId());
            String ancestors = parentDept.getAncestors() + "," + parentDept.getDeptId();
            existDept.setAncestors(ancestors);
        }

        // 设置更新时间和更新人
        existDept.setUpdateBy("admin");
        existDept.setUpdateTime(new Date());
        existDept.setStatus("0");        // 设置状态为正常
        existDept.setDelFlag("0");       // 确保删除标志为未删除

        // 执行更新操作
        deptMapper.updateDept(existDept);
        result[1] = 1; // 更新计数+1

        return result;
    }

    /**
     * 根据部门代码查询部门信息
     *
     * @param deptCode 部门代码
     * @return 部门信息对象，如果不存在返回null
     */
    private SysDept selectDeptByDm(String deptCode) {
        if (StringUtils.isEmpty(deptCode)) {
            return null;
        }

        // 直接使用Mapper提供的方法
        return deptMapper.selectDeptByDm(deptCode);
    }
}
