package com.ruoyi.tiem.service;

import com.ruoyi.tiem.domain.TItemFunding;

import java.util.List;

/**
 * 绩效评价-项目资金Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface ITItemFundingService 
{
    /**
     * 查询绩效评价-项目资金
     * 
     * @param id 绩效评价-项目资金主键
     * @return 绩效评价-项目资金
     */
    public TItemFunding selectTItemFundingById(Long id);

    /**
     * 查询绩效评价-项目资金列表
     * 
     * @param tItemFunding 绩效评价-项目资金
     * @return 绩效评价-项目资金集合
     */
    public List<TItemFunding> selectTItemFundingList(TItemFunding tItemFunding);

    /**
     * 新增绩效评价-项目资金
     * 
     * @param tItemFunding 绩效评价-项目资金
     * @return 结果
     */
    public int insertTItemFunding(TItemFunding tItemFunding);

    /**
     * 修改绩效评价-项目资金
     * 
     * @param tItemFunding 绩效评价-项目资金
     * @return 结果
     */
    public int updateTItemFunding(TItemFunding tItemFunding);

    /**
     * 批量删除绩效评价-项目资金
     * 
     * @param ids 需要删除的绩效评价-项目资金主键集合
     * @return 结果
     */
    public int deleteTItemFundingByIds(Long[] ids);

    /**
     * 删除绩效评价-项目资金信息
     * 
     * @param id 绩效评价-项目资金主键
     * @return 结果
     */
    public int deleteTItemFundingById(Long id);
}
