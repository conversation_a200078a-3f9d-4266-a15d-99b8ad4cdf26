package com.ruoyi.tiem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目资金来源对象 t_founding_source
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public class TFoundingSource extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 项目id */
    @Excel(name = "项目id")
    private Long itemId;

    /** 申请拨款预算数（单位：万元） */
    @Excel(name = "申请拨款预算数", readConverterExp = "单位：万元")
    private String applyFounding;

    /** 其他资金预算数（单位：万元） */
    @Excel(name = "其他资金预算数", readConverterExp = "单位：万元")
    private String otherFounding;

    /** 状态（1：正常；9：删除） */
    @Excel(name = "状态", readConverterExp = "1=：正常；9：删除")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setApplyFounding(String applyFounding) 
    {
        this.applyFounding = applyFounding;
    }

    public String getApplyFounding() 
    {
        return applyFounding;
    }
    public void setOtherFounding(String otherFounding) 
    {
        this.otherFounding = otherFounding;
    }

    public String getOtherFounding() 
    {
        return otherFounding;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemId", getItemId())
            .append("applyFounding", getApplyFounding())
            .append("otherFounding", getOtherFounding())
            .append("status", getStatus())
            .toString();
    }
}
