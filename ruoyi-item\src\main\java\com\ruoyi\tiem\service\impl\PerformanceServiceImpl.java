package com.ruoyi.tiem.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.tiem.mapper.PerformanceMapper;
import com.ruoyi.tiem.domain.Performance;
import com.ruoyi.tiem.service.IPerformanceService;

/**
 * 项目绩效信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
public class PerformanceServiceImpl implements IPerformanceService 
{
    @Autowired
    private PerformanceMapper performanceMapper;

    /**
     * 查询项目绩效信息
     * 
     * @param id 项目绩效信息主键
     * @return 项目绩效信息
     */
    @Override
    public Performance selectPerformanceById(Long id)
    {
        return performanceMapper.selectPerformanceById(id);
    }

    /**
     * 查询项目绩效信息列表
     * 
     * @param performance 项目绩效信息
     * @return 项目绩效信息
     */
    @Override
    public List<Performance> selectPerformanceList(Performance performance)
    {
        return performanceMapper.selectPerformanceList(performance);
    }

    /**
     * 新增项目绩效信息
     * 
     * @param performance 项目绩效信息
     * @return 结果
     */
    @Override
    public int insertPerformance(Performance performance)
    {
        return performanceMapper.insertPerformance(performance);
    }

    /**
     * 修改项目绩效信息
     * 
     * @param performance 项目绩效信息
     * @return 结果
     */
    @Override
    public int updatePerformance(Performance performance)
    {
        return performanceMapper.updatePerformance(performance);
    }

    /**
     * 批量删除项目绩效信息
     * 
     * @param ids 需要删除的项目绩效信息主键
     * @return 结果
     */
    @Override
    public int deletePerformanceByIds(Long[] ids)
    {
        return performanceMapper.deletePerformanceByIds(ids);
    }

    /**
     * 删除项目绩效信息信息
     * 
     * @param id 项目绩效信息主键
     * @return 结果
     */
    @Override
    public int deletePerformanceById(Long id)
    {
        return performanceMapper.deletePerformanceById(id);
    }
}
